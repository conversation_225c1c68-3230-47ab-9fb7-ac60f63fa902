// Caching layer for scaling to millions of users

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>()
  private maxSize = 10000 // Prevent memory leaks
  
  constructor() {
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000)
  }

  set<T>(key: string, data: T, ttlSeconds: number = 300): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    })
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  private cleanup(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize
    }
  }
}

// Singleton cache instance
export const cache = new MemoryCache()

// Cache key generators for consistent naming
export const cacheKeys = {
  user: (userId: string) => `user:${userId}`,
  userProfile: (userId: string) => `user:profile:${userId}`,
  duoPost: (duoId: string) => `duo:${duoId}`,
  duoParts: (duoId: string) => `duo:parts:${duoId}`,
  userDuos: (userId: string) => `user:duos:${userId}`,
  timeline: (userId: string, page: number = 0) => `timeline:${userId}:${page}`,
  notifications: (userId: string) => `notifications:${userId}`,
  signedUrl: (key: string) => `signed:${key}`,
}

// High-level caching functions
export async function getCachedOrFetch<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttlSeconds: number = 300
): Promise<T> {
  // Try cache first
  const cached = cache.get<T>(key)
  if (cached !== null) {
    return cached
  }

  // Fetch and cache
  const data = await fetcher()
  cache.set(key, data, ttlSeconds)
  return data
}

// Invalidation helpers
export function invalidateUserCache(userId: string): void {
  cache.delete(cacheKeys.user(userId))
  cache.delete(cacheKeys.userProfile(userId))
  cache.delete(cacheKeys.userDuos(userId))
  cache.delete(cacheKeys.notifications(userId))
  
  // Invalidate timeline pages (up to 10 pages)
  for (let i = 0; i < 10; i++) {
    cache.delete(cacheKeys.timeline(userId, i))
  }
}

export function invalidateDuoCache(duoId: string): void {
  cache.delete(cacheKeys.duoPost(duoId))
  cache.delete(cacheKeys.duoParts(duoId))
}

// For production scaling, implement Redis cache
export class RedisCache {
  private client: any // Redis client

  constructor(redisClient: any) {
    this.client = redisClient
  }

  async set<T>(key: string, data: T, ttlSeconds: number = 300): Promise<void> {
    await this.client.setex(key, ttlSeconds, JSON.stringify(data))
  }

  async get<T>(key: string): Promise<T | null> {
    const data = await this.client.get(key)
    return data ? JSON.parse(data) : null
  }

  async delete(key: string): Promise<void> {
    await this.client.del(key)
  }

  async clear(): Promise<void> {
    await this.client.flushdb()
  }

  // Batch operations for efficiency
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    const results = await this.client.mget(keys)
    return results.map((data: string | null) => 
      data ? JSON.parse(data) : null
    )
  }

  async mset(entries: Array<[string, any, number]>): Promise<void> {
    const pipeline = this.client.pipeline()
    for (const [key, data, ttl] of entries) {
      pipeline.setex(key, ttl, JSON.stringify(data))
    }
    await pipeline.exec()
  }

  // Cache warming for popular content
  async warmCache(userId: string): Promise<void> {
    // Pre-load frequently accessed data
    // This would be called after user login
  }
}

// Cache warming strategies for scale
export async function warmUserCache(userId: string): Promise<void> {
  // Pre-load user's most likely needed data
  const promises = [
    // User profile
    getCachedOrFetch(
      cacheKeys.userProfile(userId),
      () => fetchUserProfile(userId),
      600 // 10 minutes
    ),
    
    // Recent notifications
    getCachedOrFetch(
      cacheKeys.notifications(userId),
      () => fetchUserNotifications(userId),
      300 // 5 minutes
    ),
    
    // User's OnlyDuos
    getCachedOrFetch(
      cacheKeys.userDuos(userId),
      () => fetchUserDuos(userId),
      300 // 5 minutes
    )
  ]

  await Promise.allSettled(promises)
}

// Placeholder functions - implement with actual data fetching
async function fetchUserProfile(userId: string): Promise<any> {
  // Implement actual user profile fetching
  return null
}

async function fetchUserNotifications(userId: string): Promise<any> {
  // Implement actual notifications fetching
  return null
}

async function fetchUserDuos(userId: string): Promise<any> {
  // Implement actual user duos fetching
  return null
}

// Cache middleware for API routes
export function withCache<T>(
  key: string,
  ttlSeconds: number = 300
) {
  return function(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function(...args: any[]) {
      const cached = cache.get<T>(key)
      if (cached !== null) {
        return cached
      }

      const result = await method.apply(this, args)
      cache.set(key, result, ttlSeconds)
      return result
    }

    return descriptor
  }
}

// Performance monitoring for cache
export function getCacheMetrics() {
  return {
    ...cache.getStats(),
    hitRate: 0, // Would track in production
    missRate: 0, // Would track in production
    avgResponseTime: 0 // Would track in production
  }
}
