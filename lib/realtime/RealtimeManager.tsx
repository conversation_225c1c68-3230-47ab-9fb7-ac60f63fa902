'use client'

import { createContext, useContext, useEffect, useRef, useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { RealtimeChannel } from '@supabase/supabase-js'
import { usePathname } from 'next/navigation'

// Event types for type safety
export interface RealtimeEvent {
  type: 'payment' | 'subscription' | 'message' | 'comment'
  data: any
  userId: string
  timestamp: string
}

// Event handlers
export type EventHandler = (event: RealtimeEvent) => void

interface RealtimeContextType {
  subscribe: (eventType: string, handler: EventHandler) => () => void
  isConnected: boolean
  userId: string | null
}

const RealtimeContext = createContext<RealtimeContextType | null>(null)

interface RealtimeProviderProps {
  children: React.ReactNode
}

export function RealtimeProvider({ children }: RealtimeProviderProps) {
  const [userId, setUserId] = useState<string | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const pathname = usePathname()
  const supabase = createSupabaseClient()
  
  // Store event handlers
  const handlersRef = useRef<Map<string, Set<EventHandler>>>(new Map())
  const channelRef = useRef<RealtimeChannel | null>(null)
  
  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUserId(user?.id || null)
    }

    getCurrentUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUserId(session?.user?.id || null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  // REAL-TIME COMPLETELY DISABLED FOR PERFORMANCE
  useEffect(() => {
    console.log('🚫 Real-time completely disabled - using post-action refreshes instead')
    setIsConnected(false)
    return

    console.log('🔔 Setting up centralized real-time for user:', userId)

    // Create single channel for all user events
    const channel = supabase
      .channel(`user-events-${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'payments',
          filter: `writer_id=eq.${userId}`
        },
        (payload) => {
          const event: RealtimeEvent = {
            type: 'payment',
            data: payload.new,
            userId,
            timestamp: new Date().toISOString()
          }
          notifyHandlers('payment', event)
          notifyHandlers('*', event) // Global handlers
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'subscriptions',
          filter: `writer_id=eq.${userId}`
        },
        (payload) => {
          const event: RealtimeEvent = {
            type: 'subscription',
            data: payload.new,
            userId,
            timestamp: new Date().toISOString()
          }
          notifyHandlers('subscription', event)
          notifyHandlers('*', event)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'direct_messages',
          filter: `recipient_id=eq.${userId}`
        },
        (payload) => {
          const event: RealtimeEvent = {
            type: 'message',
            data: payload.new,
            userId,
            timestamp: new Date().toISOString()
          }
          notifyHandlers('message', event)
          notifyHandlers('*', event)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'comments',
          filter: `user_id=eq.${userId}` // Comments on user's content
        },
        (payload) => {
          const event: RealtimeEvent = {
            type: 'comment',
            data: payload.new,
            userId,
            timestamp: new Date().toISOString()
          }
          notifyHandlers('comment', event)
          notifyHandlers('*', event)
        }
      )
      .subscribe((status) => {
        console.log('Real-time connection status:', status)
        setIsConnected(status === 'SUBSCRIBED')
      })

    channelRef.current = channel

    // Cleanup on unmount or user change
    return () => {
      console.log('🧹 Cleaning up real-time connection for user:', userId)
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current)
        channelRef.current = null
        setIsConnected(false)
      }
    }
  }, [userId, pathname, supabase])

  // Notify all handlers for an event type
  const notifyHandlers = (eventType: string, event: RealtimeEvent) => {
    const handlers = handlersRef.current.get(eventType)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event)
        } catch (error) {
          console.error('Error in real-time handler:', error)
        }
      })
    }
  }

  // Subscribe to events
  const subscribe = (eventType: string, handler: EventHandler): (() => void) => {
    if (!handlersRef.current.has(eventType)) {
      handlersRef.current.set(eventType, new Set())
    }
    
    const handlers = handlersRef.current.get(eventType)!
    handlers.add(handler)

    // Return unsubscribe function
    return () => {
      handlers.delete(handler)
      if (handlers.size === 0) {
        handlersRef.current.delete(eventType)
      }
    }
  }

  const contextValue: RealtimeContextType = {
    subscribe,
    isConnected,
    userId
  }

  return (
    <RealtimeContext.Provider value={contextValue}>
      {children}
    </RealtimeContext.Provider>
  )
}

// Hook to use real-time events
export function useRealtime() {
  const context = useContext(RealtimeContext)
  if (!context) {
    throw new Error('useRealtime must be used within a RealtimeProvider')
  }
  return context
}

// Convenience hooks for specific events
export function usePaymentEvents(handler: (event: RealtimeEvent) => void) {
  const { subscribe } = useRealtime()
  
  useEffect(() => {
    const unsubscribe = subscribe('payment', handler)
    return unsubscribe
  }, [subscribe, handler])
}

export function useSubscriptionEvents(handler: (event: RealtimeEvent) => void) {
  const { subscribe } = useRealtime()
  
  useEffect(() => {
    const unsubscribe = subscribe('subscription', handler)
    return unsubscribe
  }, [subscribe, handler])
}

export function useMessageEvents(handler: (event: RealtimeEvent) => void) {
  const { subscribe } = useRealtime()
  
  useEffect(() => {
    const unsubscribe = subscribe('message', handler)
    return unsubscribe
  }, [subscribe, handler])
}

export function useCommentEvents(handler: (event: RealtimeEvent) => void) {
  const { subscribe } = useRealtime()
  
  useEffect(() => {
    const unsubscribe = subscribe('comment', handler)
    return unsubscribe
  }, [subscribe, handler])
}
