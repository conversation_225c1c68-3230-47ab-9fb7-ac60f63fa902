import { S3Client } from '@aws-sdk/client-s3';

// Dedicated R2 S3 client for Duo video uploads.
// Prefers video-scoped keys, falls back to generic R2 keys if needed.
const r2VideoClient = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_S3_ENDPOINT,
  credentials: {
    accessKeyId:
      process.env.CLOUDFLARE_VIDEO_ACCESS_KEY_ID ||
      process.env.CLOUDFLARE_R2_ACCESS_KEY_ID ||
      '',
    secretAccessKey:
      process.env.CLOUDFLARE_VIDEO_SECRET_ACCESS_KEY ||
      process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY ||
      '',
  },
});

export default r2VideoClient;
