/**
 * Paywall utility functions for OnlyDiary
 * Handles access control, content truncation, and subscription checking
 */

export interface PaywallContent {
  truncatedContent: string
  remainingWords: number
  hasAccess: boolean
  needsSubscription: boolean
}

/**
 * Check if user has access to paid content
 */
export function checkContentAccess(
  isFree: boolean,
  isOwner: boolean,
  hasSubscription: boolean
): boolean {
  // Free content is always accessible
  if (isFree) return true
  
  // Owner can always see their own content
  if (isOwner) return true
  
  // Paid content requires subscription
  return hasSubscription
}

/**
 * Process content for paywall display
 */
export function processPaywallContent(
  content: string,
  hasAccess: boolean
): PaywallContent {
  if (hasAccess) {
    return {
      truncatedContent: content,
      remainingWords: 0,
      hasAccess: true,
      needsSubscription: false
    }
  }

  // Split content into sentences
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
  
  if (sentences.length === 0) {
    return {
      truncatedContent: "",
      remainingWords: 0,
      hasAccess: false,
      needsSubscription: true
    }
  }

  // Show only first sentence
  const firstSentence = sentences[0].trim() + "."
  
  // Calculate remaining words
  const allWords = content.trim().split(/\s+/).filter(word => word.length > 0)
  const shownWords = firstSentence.trim().split(/\s+/).filter(word => word.length > 0)
  const remainingWords = Math.max(0, allWords.length - shownWords.length)

  return {
    truncatedContent: firstSentence,
    remainingWords,
    hasAccess: false,
    needsSubscription: true
  }
}

/**
 * Get subscription status between two users
 * @deprecated Use getBatchSubscriptionStatus for better performance when checking multiple writers
 */
export async function getSubscriptionStatus(
  supabase: any,
  subscriberId: string | null,
  writerId: string
): Promise<boolean> {
  if (!subscriberId || subscriberId === writerId) {
    return subscriberId === writerId // Owner has access
  }

  const { data: subscription } = await supabase
    .from('subscriptions')
    .select('status')
    .eq('reader_id', subscriberId)
    .eq('writer_id', writerId)
    .eq('status', 'active')
    .gte('current_period_end', new Date().toISOString())
    .single()

  return !!subscription
}

/**
 * Get subscription status for multiple writers at once (much more efficient)
 */
export async function getBatchSubscriptionStatus(
  supabase: any,
  subscriberId: string | null,
  writerIds: string[]
): Promise<Map<string, boolean>> {
  const result = new Map<string, boolean>()

  if (!subscriberId) {
    // No subscriber, all false
    writerIds.forEach(id => result.set(id, false))
    return result
  }

  // Owner has access to their own content
  writerIds.forEach(id => result.set(id, id === subscriberId))

  // Get all active subscriptions at once
  const { data: subscriptions } = await supabase
    .from('subscriptions')
    .select('writer_id')
    .eq('reader_id', subscriberId)
    .in('writer_id', writerIds)
    .eq('status', 'active')
    .gte('current_period_end', new Date().toISOString())

  // Update result map with subscription data
  subscriptions?.forEach(sub => {
    result.set(sub.writer_id, true)
  })

  return result
}

/**
 * Check if user has purchased a specific book project
 */
export async function getProjectPurchaseStatus(
  supabase: any,
  userId: string | null,
  projectId: string,
  writerId: string
): Promise<boolean> {
  if (!userId || userId === writerId) {
    return userId === writerId // Owner has access
  }

  const { data: purchase } = await supabase
    .from('project_purchases')
    .select('id')
    .eq('buyer_id', userId)
    .eq('project_id', projectId)
    .single()

  return !!purchase
}
