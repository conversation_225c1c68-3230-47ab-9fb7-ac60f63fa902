// Analytics and monitoring for scaling to millions of users

export interface AnalyticsEvent {
  event: string
  userId?: string
  sessionId?: string
  properties?: Record<string, any>
  timestamp?: number
}

export interface PerformanceMetric {
  metric: string
  value: number
  unit: string
  timestamp?: number
  tags?: Record<string, string>
}

class Analytics {
  private events: AnalyticsEvent[] = []
  private metrics: PerformanceMetric[] = []
  private batchSize = 100
  private flushInterval = 30000 // 30 seconds

  constructor() {
    // Auto-flush events periodically
    if (typeof window === 'undefined') {
      setInterval(() => this.flush(), this.flushInterval)
    }
  }

  // Track user events for product analytics
  track(event: string, properties?: Record<string, any>, userId?: string) {
    this.events.push({
      event,
      userId,
      properties,
      timestamp: Date.now(),
      sessionId: this.getSessionId()
    })

    if (this.events.length >= this.batchSize) {
      this.flush()
    }
  }

  // Track performance metrics for monitoring
  metric(metric: string, value: number, unit: string = 'count', tags?: Record<string, string>) {
    this.metrics.push({
      metric,
      value,
      unit,
      timestamp: Date.now(),
      tags
    })

    if (this.metrics.length >= this.batchSize) {
      this.flushMetrics()
    }
  }

  // Track API response times
  trackApiCall(endpoint: string, method: string, duration: number, status: number) {
    this.metric('api.response_time', duration, 'ms', {
      endpoint,
      method,
      status: status.toString()
    })

    this.metric('api.requests', 1, 'count', {
      endpoint,
      method,
      status: status.toString()
    })
  }

  // Track OnlyDuos specific events
  trackOnlyDuo(action: string, duoId: string, userId?: string, properties?: Record<string, any>) {
    this.track(`onlyduo.${action}`, {
      duoId,
      ...properties
    }, userId)
  }

  // Track user engagement
  trackEngagement(action: string, userId?: string, properties?: Record<string, any>) {
    this.track(`engagement.${action}`, properties, userId)
  }

  // Track errors for monitoring
  trackError(error: Error, context?: Record<string, any>) {
    this.track('error', {
      message: error.message,
      stack: error.stack,
      ...context
    })

    this.metric('errors', 1, 'count', {
      type: error.name,
      message: error.message.slice(0, 100)
    })
  }

  private async flush() {
    if (this.events.length === 0) return

    const eventsToSend = [...this.events]
    this.events = []

    try {
      // In production, send to analytics service (Mixpanel, Amplitude, etc.)
      await this.sendEvents(eventsToSend)
    } catch (error) {
      console.error('Failed to send analytics events:', error)
      // Re-queue events on failure
      this.events.unshift(...eventsToSend)
    }
  }

  private async flushMetrics() {
    if (this.metrics.length === 0) return

    const metricsToSend = [...this.metrics]
    this.metrics = []

    try {
      // In production, send to monitoring service (DataDog, New Relic, etc.)
      await this.sendMetrics(metricsToSend)
    } catch (error) {
      console.error('Failed to send metrics:', error)
      // Re-queue metrics on failure
      this.metrics.unshift(...metricsToSend)
    }
  }

  private async sendEvents(events: AnalyticsEvent[]) {
    // TODO: Implement actual analytics service integration
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Events:', events)
    }

    // Example integration with Mixpanel:
    // await fetch('https://api.mixpanel.com/track', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(events.map(event => ({
    //     event: event.event,
    //     properties: {
    //       ...event.properties,
    //       distinct_id: event.userId,
    //       time: event.timestamp
    //     }
    //   })))
    // })
  }

  private async sendMetrics(metrics: PerformanceMetric[]) {
    // TODO: Implement actual monitoring service integration
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance Metrics:', metrics)
    }

    // Example integration with DataDog:
    // await fetch('https://api.datadoghq.com/api/v1/series', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'DD-API-KEY': process.env.DATADOG_API_KEY
    //   },
    //   body: JSON.stringify({
    //     series: metrics.map(metric => ({
    //       metric: metric.metric,
    //       points: [[metric.timestamp! / 1000, metric.value]],
    //       tags: Object.entries(metric.tags || {}).map(([k, v]) => `${k}:${v}`)
    //     }))
    //   })
    // })
  }

  private getSessionId(): string {
    // Simple session ID generation
    if (typeof window !== 'undefined') {
      let sessionId = sessionStorage.getItem('analytics_session_id')
      if (!sessionId) {
        sessionId = Math.random().toString(36).substring(2, 15)
        sessionStorage.setItem('analytics_session_id', sessionId)
      }
      return sessionId
    }
    return 'server'
  }
}

// Singleton instance
export const analytics = new Analytics()

// Convenience functions for common tracking
export const trackOnlyDuoCreated = (duoId: string, userId: string) => {
  analytics.trackOnlyDuo('created', duoId, userId)
}

export const trackOnlyDuoCompleted = (duoId: string, userId: string) => {
  analytics.trackOnlyDuo('completed', duoId, userId)
}

export const trackOnlyDuoShared = (duoId: string, userId: string, platform?: string) => {
  analytics.trackOnlyDuo('shared', duoId, userId, { platform })
}

export const trackOnlyDuoDownloaded = (duoId: string, userId: string) => {
  analytics.trackOnlyDuo('downloaded', duoId, userId)
}

export const trackOnlyDuoRedoRequested = (duoId: string, userId: string) => {
  analytics.trackOnlyDuo('redo_requested', duoId, userId)
}

// Performance monitoring helpers
export const measureApiCall = async <T>(
  endpoint: string,
  method: string,
  fn: () => Promise<T>
): Promise<T> => {
  const start = Date.now()
  let status = 200
  
  try {
    const result = await fn()
    return result
  } catch (error) {
    status = error instanceof Error && 'status' in error ? (error as any).status : 500
    throw error
  } finally {
    const duration = Date.now() - start
    analytics.trackApiCall(endpoint, method, duration, status)
  }
}

// Error boundary helper
export const withErrorTracking = <T extends (...args: any[]) => any>(
  fn: T,
  context?: Record<string, any>
): T => {
  return ((...args: any[]) => {
    try {
      const result = fn(...args)
      if (result instanceof Promise) {
        return result.catch((error) => {
          analytics.trackError(error, context)
          throw error
        })
      }
      return result
    } catch (error) {
      analytics.trackError(error as Error, context)
      throw error
    }
  }) as T
}
