/**
 * Rate Limiting Implementation
 * Prevents abuse, DoS attacks, and brute force attempts
 */

import { NextRequest } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

// Rate limit configurations for different endpoint types
export const RATE_LIMITS = {
  // Authentication endpoints (stricter limits)
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,            // 5 attempts per window
    message: 'Too many authentication attempts. Please try again in 15 minutes.'
  },
  
  // API endpoints (moderate limits)
  API: {
    windowMs: 1 * 60 * 1000,   // 1 minute
    maxRequests: 60,           // 60 requests per minute
    message: 'Too many API requests. Please slow down.'
  },

  // Write/mutation endpoints
  WRITE: {
    windowMs: 1 * 60 * 1000,   // 1 minute
    maxRequests: 60,           // 60 writes per minute
    message: 'Too many write requests. Please slow down.'
  },
  
  // File upload endpoints (stricter limits)
  UPLOAD: {
    windowMs: 5 * 60 * 1000,   // 5 minutes
    maxRequests: 10,           // 10 uploads per 5 minutes
    message: 'Too many upload attempts. Please wait before uploading again.'
  },
  
  // Public endpoints (lenient limits)
  PUBLIC: {
    windowMs: 1 * 60 * 1000,   // 1 minute
    maxRequests: 100,          // 100 requests per minute
    message: 'Too many requests. Please slow down.'
  },
  
  // Admin endpoints (very strict)
  ADMIN: {
    windowMs: 5 * 60 * 1000,   // 5 minutes
    maxRequests: 20,           // 20 requests per 5 minutes
    message: 'Too many admin requests. Access temporarily restricted.'
  }
} as const

export type RateLimitType = keyof typeof RATE_LIMITS

// In-memory store for rate limiting (use Redis in production for multiple instances)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

/**
 * Gets the client identifier for rate limiting
 * @param request - NextRequest object
 * @returns Client identifier string
 */
function getClientId(request: NextRequest): string {
  // Try to get real IP from headers (for proxies/CDNs)
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip') // Cloudflare
  
  // Use the first available IP
  const ip = forwardedFor?.split(',')[0]?.trim() || 
            realIp || 
            cfConnectingIp || 
            'unknown'
  
  return ip
}

/**
 * Creates a rate limit key for storage
 * @param clientId - Client identifier
 * @param endpoint - Endpoint identifier
 * @param limitType - Type of rate limit
 * @returns Rate limit key
 */
function createRateLimitKey(clientId: string, endpoint: string, limitType: RateLimitType): string {
  return `${limitType}:${endpoint}:${clientId}`
}

/**
 * Cleans up expired rate limit entries
 */
function cleanupExpiredEntries(): void {
  const now = Date.now()
  
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}

/**
 * Checks if request is within rate limits
 * @param request - NextRequest object
 * @param limitType - Type of rate limit to apply
 * @param endpoint - Endpoint identifier (optional, defaults to pathname)
 * @returns Rate limit result
 */
export function checkRateLimit(
  request: NextRequest,
  limitType: RateLimitType,
  endpoint?: string
): {
  allowed: boolean
  limit: number
  remaining: number
  resetTime: number
  retryAfter?: number
} {
  // Clean up expired entries periodically
  if (Math.random() < 0.01) { // 1% chance to cleanup
    cleanupExpiredEntries()
  }
  
  const config = RATE_LIMITS[limitType]
  const clientId = getClientId(request)
  const endpointId = endpoint || new URL(request.url).pathname
  const key = createRateLimitKey(clientId, endpointId, limitType)
  
  const now = Date.now()
  const windowStart = now - config.windowMs
  
  // Get current rate limit data
  let rateLimitData = rateLimitStore.get(key)
  
  // Initialize or reset if window expired
  if (!rateLimitData || now > rateLimitData.resetTime) {
    rateLimitData = {
      count: 0,
      resetTime: now + config.windowMs
    }
  }
  
  // Check if limit exceeded
  if (rateLimitData.count >= config.maxRequests) {
    const retryAfter = Math.ceil((rateLimitData.resetTime - now) / 1000)

    // Log rate limit violation to database (async, don't wait)
    logRateLimitViolation(request, limitType, endpointId).catch(console.error)

    return {
      allowed: false,
      limit: config.maxRequests,
      remaining: 0,
      resetTime: rateLimitData.resetTime,
      retryAfter
    }
  }
  
  // Increment counter and update store
  rateLimitData.count++
  rateLimitStore.set(key, rateLimitData)
  
  return {
    allowed: true,
    limit: config.maxRequests,
    remaining: config.maxRequests - rateLimitData.count,
    resetTime: rateLimitData.resetTime
  }
}

/**
 * Creates rate limit headers for response
 * @param result - Rate limit check result
 * @returns Headers object
 */
export function createRateLimitHeaders(result: {
  limit: number
  remaining: number
  resetTime: number
  retryAfter?: number
}): Record<string, string> {
  const headers: Record<string, string> = {
    'X-RateLimit-Limit': result.limit.toString(),
    'X-RateLimit-Remaining': result.remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString()
  }
  
  if (result.retryAfter) {
    headers['Retry-After'] = result.retryAfter.toString()
  }
  
  return headers
}

/**
 * Creates a rate limit exceeded response
 * @param limitType - Type of rate limit that was exceeded
 * @param result - Rate limit check result
 * @returns Response object
 */
export function createRateLimitResponse(
  limitType: RateLimitType,
  result: { retryAfter?: number }
): Response {
  const config = RATE_LIMITS[limitType]
  
  return new Response(
    JSON.stringify({
      error: 'Rate limit exceeded',
      message: config.message,
      retryAfter: result.retryAfter
    }),
    {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        ...createRateLimitHeaders(result as any)
      }
    }
  )
}

/**
 * Rate limiting middleware for API routes
 * @param limitType - Type of rate limit to apply
 * @param endpoint - Optional endpoint identifier
 * @returns Middleware function
 */
export function withRateLimit(
  limitType: RateLimitType,
  endpoint?: string
) {
  return function rateLimitMiddleware(
    handler: (request: NextRequest, context: any) => Promise<Response>
  ) {
    return async (request: NextRequest, context: any): Promise<Response> => {
      // Check rate limit
      const rateLimitResult = checkRateLimit(request, limitType, endpoint)
      
      // If rate limit exceeded, return error response
      if (!rateLimitResult.allowed) {
        return createRateLimitResponse(limitType, rateLimitResult)
      }
      
      // Rate limit passed, proceed with original handler
      const response = await handler(request, context)
      
      // Add rate limit headers to successful responses
      const rateLimitHeaders = createRateLimitHeaders(rateLimitResult)
      
      // Clone response to add headers
      const newResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          ...Object.fromEntries(response.headers.entries()),
          ...rateLimitHeaders
        }
      })
      
      return newResponse
    }
  }
}

/**
 * Gets rate limit status for a client
 * @param request - NextRequest object
 * @param limitType - Type of rate limit to check
 * @param endpoint - Optional endpoint identifier
 * @returns Current rate limit status
 */
export function getRateLimitStatus(
  request: NextRequest,
  limitType: RateLimitType,
  endpoint?: string
): {
  limit: number
  remaining: number
  resetTime: number
  isNearLimit: boolean
} {
  const config = RATE_LIMITS[limitType]
  const clientId = getClientId(request)
  const endpointId = endpoint || new URL(request.url).pathname
  const key = createRateLimitKey(clientId, endpointId, limitType)
  
  const rateLimitData = rateLimitStore.get(key)
  const now = Date.now()
  
  if (!rateLimitData || now > rateLimitData.resetTime) {
    return {
      limit: config.maxRequests,
      remaining: config.maxRequests,
      resetTime: now + config.windowMs,
      isNearLimit: false
    }
  }
  
  const remaining = config.maxRequests - rateLimitData.count
  const isNearLimit = remaining <= Math.ceil(config.maxRequests * 0.1) // 10% remaining
  
  return {
    limit: config.maxRequests,
    remaining,
    resetTime: rateLimitData.resetTime,
    isNearLimit
  }
}

/**
 * Logs rate limit violations to database
 * @param request - NextRequest object
 * @param limitType - Type of rate limit violated
 * @param endpoint - Endpoint that was rate limited
 */
async function logRateLimitViolation(
  request: NextRequest,
  limitType: RateLimitType,
  endpoint: string
): Promise<void> {
  try {
    const supabase = await createSupabaseServerClient()
    const clientId = getClientId(request)
    const userAgent = request.headers.get('user-agent')

    // Try to get user ID if authenticated
    let userId: string | null = null
    try {
      const { data: { user } } = await supabase.auth.getUser()
      userId = user?.id || null
    } catch {
      // User not authenticated, that's fine
    }

    // Insert or update rate limit violation record
    const { error } = await supabase
      .from('rate_limit_violations')
      .upsert({
        ip_address: clientId,
        endpoint,
        limit_type: limitType,
        user_id: userId,
        user_agent: userAgent,
        violation_count: 1,
        last_violation_at: new Date().toISOString()
      }, {
        onConflict: 'ip_address,endpoint,limit_type',
        ignoreDuplicates: false
      })

    if (error) {
      console.error('Failed to log rate limit violation:', error)
    }
  } catch (error) {
    console.error('Error logging rate limit violation:', error)
  }
}
