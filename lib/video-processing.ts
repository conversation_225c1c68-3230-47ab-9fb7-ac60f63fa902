// Video processing service for OnlyDuos
// Handles concatenation, watermarking, and author attribution

import { spawn } from 'child_process'
import { promises as fs } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

export interface VideoProcessingOptions {
  duoId: string
  partAUrl: string
  partBUrl: string
  initiatorName: string
  responderName: string
  initiatorPhoto?: string
  responderPhoto?: string
  includeWatermark?: boolean
  includeAttribution?: boolean
}

export interface ProcessedVideo {
  buffer: Buffer
  filename: string
  duration: number
  size: number
}

export class VideoProcessor {
  private tempDir: string
  private ffmpegPath: string

  constructor() {
    this.tempDir = process.env.TEMP_DIR || '/tmp'
    this.ffmpegPath = process.env.FFMPEG_PATH || 'ffmpeg'
  }

  // Development fallback when FFmpeg is not available
  async processOnlyDuoFallback(options: VideoProcessingOptions): Promise<ProcessedVideo> {
    console.log('🎬 Video processing fallback - FFmpeg not available')
    console.log('📋 Would process:', {
      duoId: options.duoId,
      initiator: options.initiatorName,
      responder: options.responderName,
      watermark: options.includeWatermark,
      attribution: options.includeAttribution
    })

    // Download Part A as fallback
    const response = await fetch(options.partAUrl)
    if (!response.ok) {
      throw new Error('Failed to download video for fallback processing')
    }

    const buffer = Buffer.from(await response.arrayBuffer())

    return {
      buffer,
      filename: `onlyduo-${options.duoId}-fallback.mp4`,
      duration: 30, // Estimated
      size: buffer.length
    }
  }

  async processOnlyDuo(options: VideoProcessingOptions): Promise<ProcessedVideo> {
    const workingDir = path.join(this.tempDir, `onlyduo-${uuidv4()}`)
    await fs.mkdir(workingDir, { recursive: true })

    try {
      // Download both video parts
      const partAPath = await this.downloadVideo(options.partAUrl, path.join(workingDir, 'part-a.mp4'))
      const partBPath = await this.downloadVideo(options.partBUrl, path.join(workingDir, 'part-b.mp4'))

      // Create watermark overlay
      const watermarkPath = await this.createWatermarkOverlay(workingDir)

      // Create author attribution overlays
      const attributionOverlays = await this.createAttributionOverlays(
        workingDir,
        options.initiatorName,
        options.responderName,
        options.initiatorPhoto,
        options.responderPhoto
      )

      // Process the video
      const outputPath = path.join(workingDir, `onlyduo-${options.duoId}.mp4`)
      await this.concatenateWithOverlays(
        partAPath,
        partBPath,
        outputPath,
        watermarkPath,
        attributionOverlays,
        options
      )

      // Read the processed video
      const buffer = await fs.readFile(outputPath)
      const stats = await fs.stat(outputPath)

      // Get video duration
      const duration = await this.getVideoDuration(outputPath)

      return {
        buffer,
        filename: `onlyduo-${options.duoId}.mp4`,
        duration,
        size: stats.size
      }

    } finally {
      // Cleanup temporary files
      await this.cleanup(workingDir)
    }
  }

  private async downloadVideo(url: string, outputPath: string): Promise<string> {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Failed to download video: ${response.statusText}`)
    }

    const buffer = await response.arrayBuffer()
    await fs.writeFile(outputPath, Buffer.from(buffer))
    return outputPath
  }

  private async createWatermarkOverlay(workingDir: string): Promise<string> {
    // Use the official oduo.png watermark from public folder
    const publicWatermarkPath = path.join(process.cwd(), 'public', 'oduo.png')
    const watermarkPath = path.join(workingDir, 'watermark.png')
    const highResWatermarkPath = path.join(workingDir, 'watermark-hires.png')

    try {
      // Check if the official watermark exists locally
      await fs.access(publicWatermarkPath)

      // Create a high-resolution version using FFmpeg upscaling
      await this.runFFmpeg([
        '-i', publicWatermarkPath,
        '-vf', 'scale=800:-1:flags=lanczos', // Scale to 800px width with high-quality Lanczos
        '-y',
        highResWatermarkPath
      ])

      console.log('✅ Created high-resolution OnlyDuo watermark (800px)')
      return highResWatermarkPath
    } catch (error) {
      console.error('Failed to create high-res watermark, trying original:', error)

      try {
        // Fallback to original watermark
        await fs.copyFile(publicWatermarkPath, watermarkPath)
        console.log('✅ Using original OnlyDuo watermark')
        return watermarkPath
      } catch (fallbackError) {
        console.error('Failed to use original watermark, creating text fallback:', fallbackError)

        // Final fallback to generated watermark
        await this.runFFmpeg([
          '-f', 'lavfi',
          '-i', `color=c=purple@0.8:s=400x60:d=1`,
          '-vf', `drawtext=text='OnlyDuo by OnlyDiary':fontcolor=white:fontsize=24:x=16:y=18`,
          '-frames:v', '1',
          watermarkPath
        ])

        console.log('⚠️ Using high-res fallback watermark')
        return watermarkPath
      }
    }
  }

  private async downloadProfilePicture(url: string, outputPath: string): Promise<boolean> {
    try {
      const response = await fetch(url)
      if (!response.ok) return false

      const buffer = await response.arrayBuffer()
      await fs.writeFile(outputPath, Buffer.from(buffer))
      return true
    } catch {
      return false
    }
  }

  private async createAttributionOverlays(
    workingDir: string,
    initiatorName: string,
    responderName: string,
    initiatorPhoto?: string,
    responderPhoto?: string
  ): Promise<{ partA: string; partB: string }> {

    const partAOverlay = path.join(workingDir, 'attribution-a.png')
    const partBOverlay = path.join(workingDir, 'attribution-b.png')

    // Download profile pictures if available
    const initiatorPhotoPath = path.join(workingDir, 'initiator-photo.jpg')
    const responderPhotoPath = path.join(workingDir, 'responder-photo.jpg')

    const hasInitiatorPhoto = initiatorPhoto ? await this.downloadProfilePicture(initiatorPhoto, initiatorPhotoPath) : false
    const hasResponderPhoto = responderPhoto ? await this.downloadProfilePicture(responderPhoto, responderPhotoPath) : false

    // Create Part A attribution with profile picture and better styling
    if (hasInitiatorPhoto) {
      await this.runFFmpeg([
        '-f', 'lavfi', '-i', `color=c=black@0.5:s=500x60:d=1`,
        '-i', initiatorPhotoPath,
        '-filter_complex', `
          [1:v]scale=48:48,format=rgba[photo_scaled];
          [photo_scaled]geq=lum='if(gt(sqrt((X-24)*(X-24)+(Y-24)*(Y-24)),24),0,lum(X,Y))':cb='if(gt(sqrt((X-24)*(X-24)+(Y-24)*(Y-24)),24),128,cb(X,Y))':cr='if(gt(sqrt((X-24)*(X-24)+(Y-24)*(Y-24)),24),128,cr(X,Y))':a='if(gt(sqrt((X-24)*(X-24)+(Y-24)*(Y-24)),24),0,alpha(X,Y))'[photo_circle];
          [0:v][photo_circle]overlay=12:6[bg];
          [bg]drawtext=text='Video 1 created by ${initiatorName}':fontcolor=white:fontsize=20:x=70:y=20:fontfile=/usr/share/fonts/truetype/dejavu/DejaVuSerif-Bold.ttf
        `,
        '-frames:v', '1',
        partAOverlay
      ])
    } else {
      // Fallback without photo but with better text
      await this.runFFmpeg([
        '-f', 'lavfi',
        '-i', `color=c=black@0.5:s=500x60:d=1`,
        '-vf', `
          drawbox=x=12:y=6:w=48:h=48:color=purple@0.8:t=fill,
          drawtext=text='${initiatorName.charAt(0).toUpperCase()}':fontcolor=white:fontsize=24:x=30:y=22:fontfile=/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf,
          drawtext=text='Video 1 created by ${initiatorName}':fontcolor=white:fontsize=20:x=70:y=20:fontfile=/usr/share/fonts/truetype/dejavu/DejaVuSerif-Bold.ttf
        `,
        '-frames:v', '1',
        partAOverlay
      ])
    }

    // Create Part B attribution with profile picture and better styling
    if (hasResponderPhoto) {
      await this.runFFmpeg([
        '-f', 'lavfi', '-i', `color=c=black@0.5:s=500x60:d=1`,
        '-i', responderPhotoPath,
        '-filter_complex', `
          [1:v]scale=48:48,format=rgba[photo_scaled];
          [photo_scaled]geq=lum='if(gt(sqrt((X-24)*(X-24)+(Y-24)*(Y-24)),24),0,lum(X,Y))':cb='if(gt(sqrt((X-24)*(X-24)+(Y-24)*(Y-24)),24),128,cb(X,Y))':cr='if(gt(sqrt((X-24)*(X-24)+(Y-24)*(Y-24)),24),128,cr(X,Y))':a='if(gt(sqrt((X-24)*(X-24)+(Y-24)*(Y-24)),24),0,alpha(X,Y))'[photo_circle];
          [0:v][photo_circle]overlay=12:6[bg];
          [bg]drawtext=text='Video 2 created by ${responderName}':fontcolor=white:fontsize=20:x=70:y=20:fontfile=/usr/share/fonts/truetype/dejavu/DejaVuSerif-Bold.ttf
        `,
        '-frames:v', '1',
        partBOverlay
      ])
    } else {
      // Fallback without photo but with better text
      await this.runFFmpeg([
        '-f', 'lavfi',
        '-i', `color=c=black@0.5:s=500x60:d=1`,
        '-vf', `
          drawbox=x=12:y=6:w=48:h=48:color=blue@0.8:t=fill,
          drawtext=text='${responderName.charAt(0).toUpperCase()}':fontcolor=white:fontsize=24:x=30:y=22:fontfile=/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf,
          drawtext=text='Video 2 created by ${responderName}':fontcolor=white:fontsize=20:x=70:y=20:fontfile=/usr/share/fonts/truetype/liberation/LiberationSerif-Bold.ttf
        `,
        '-frames:v', '1',
        partBOverlay
      ])
    }

    return { partA: partAOverlay, partB: partBOverlay }
  }

  private async getVideoInfo(videoPath: string): Promise<{ width: number; height: number; aspectRatio: number }> {
    return new Promise((resolve, reject) => {
      const ffprobe = spawn('ffprobe', [
        '-v', 'quiet',
        '-print_format', 'json',
        '-show_streams',
        '-select_streams', 'v:0',
        videoPath
      ])

      let output = ''
      ffprobe.stdout.on('data', (data) => {
        output += data.toString()
      })

      ffprobe.on('close', (code) => {
        if (code === 0) {
          try {
            const info = JSON.parse(output)
            const videoStream = info.streams[0]
            const width = videoStream.width
            const height = videoStream.height
            const aspectRatio = width / height
            resolve({ width, height, aspectRatio })
          } catch (error) {
            reject(new Error('Failed to parse video info'))
          }
        } else {
          reject(new Error(`ffprobe failed with code ${code}`))
        }
      })
    })
  }

  private async concatenateWithOverlays(
    partAPath: string,
    partBPath: string,
    outputPath: string,
    watermarkPath: string,
    attributionOverlays: { partA: string; partB: string },
    options: VideoProcessingOptions
  ): Promise<void> {

    // Get durations of both parts
    const partADuration = await this.getVideoDuration(partAPath)
    const partBDuration = await this.getVideoDuration(partBPath)

    // Simplified, working FFmpeg command for 9:16 OnlyDuo videos
    const ffmpegArgs = [
      // Input files
      '-i', partAPath,
      '-i', partBPath,
      '-i', watermarkPath,
      '-i', attributionOverlays.partA,
      '-i', attributionOverlays.partB,

      // Filter complex with proper watermark positioning
      '-filter_complex', `
        [0:v]scale=720:1280:force_original_aspect_ratio=decrease,pad=720:1280:(ow-iw)/2:(oh-ih)/2:black[v0_sized];
        [1:v]scale=720:1280:force_original_aspect_ratio=decrease,pad=720:1280:(ow-iw)/2:(oh-ih)/2:black[v1_sized];
        [2:v]scale=240:-1:flags=lanczos,format=rgba,colorchannelmixer=aa=0.55[watermark_sized];
        [watermark_sized]split=2[wm1][wm2];
        [v0_sized][wm1]overlay=8:8[v0_watermarked];
        [v0_watermarked][3:v]overlay=(W-w)/2:H-90[v0_final];
        [v1_sized][wm2]overlay=8:8[v1_watermarked];
        [v1_watermarked][4:v]overlay=(W-w)/2:H-90[v1_final];
        [v0_final][0:a][v1_final][1:a]concat=n=2:v=1:a=1[outv][outa]
      `,

      // Map outputs
      '-map', '[outv]',
      '-map', '[outa]',

      // Output settings optimized for 9:16 social media
      '-c:v', 'libx264',
      '-preset', 'medium',
      '-crf', '23',
      '-c:a', 'aac',
      '-b:a', '128k',
      '-movflags', '+faststart',
      '-pix_fmt', 'yuv420p',
      '-s', '720x1280',

      // Overwrite output
      '-y',
      outputPath
    ]

    await this.runFFmpeg(ffmpegArgs)
  }

  private async getVideoDuration(videoPath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      const ffprobe = spawn('ffprobe', [
        '-v', 'quiet',
        '-show_entries', 'format=duration',
        '-of', 'csv=p=0',
        videoPath
      ])

      let output = ''
      ffprobe.stdout.on('data', (data) => {
        output += data.toString()
      })

      ffprobe.on('close', (code) => {
        if (code === 0) {
          const duration = parseFloat(output.trim())
          resolve(duration)
        } else {
          reject(new Error(`ffprobe failed with code ${code}`))
        }
      })
    })
  }

  private async runFFmpeg(args: string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      const ffmpeg = spawn(this.ffmpegPath, args)
      
      let stderr = ''
      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString()
      })

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          resolve()
        } else {
          reject(new Error(`FFmpeg failed with code ${code}: ${stderr}`))
        }
      })
    })
  }

  private async cleanup(workingDir: string): Promise<void> {
    try {
      await fs.rm(workingDir, { recursive: true, force: true })
    } catch (error) {
      console.error('Failed to cleanup working directory:', error)
    }
  }
}

// Background job processing for heavy video operations
export class VideoProcessingQueue {
  private queue: Array<{
    id: string
    options: VideoProcessingOptions
    resolve: (result: ProcessedVideo) => void
    reject: (error: Error) => void
  }> = []
  
  private processing = false

  async addJob(options: VideoProcessingOptions): Promise<ProcessedVideo> {
    return new Promise((resolve, reject) => {
      const id = uuidv4()
      this.queue.push({ id, options, resolve, reject })
      this.processQueue()
    })
  }

  private async processQueue(): Promise<void> {
    if (this.processing || this.queue.length === 0) return

    this.processing = true
    const processor = new VideoProcessor()

    while (this.queue.length > 0) {
      const job = this.queue.shift()!
      
      try {
        const result = await processor.processOnlyDuo(job.options)
        job.resolve(result)
      } catch (error) {
        job.reject(error as Error)
      }
    }

    this.processing = false
  }
}

// Singleton queue instance
export const videoQueue = new VideoProcessingQueue()
