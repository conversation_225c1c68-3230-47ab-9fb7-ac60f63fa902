// Import Resend with proper error handling
let resendInstance: any = null

function initializeResend() {
  if (resendInstance) return resendInstance

  try {
    const { Resend } = require('resend')

    if (!process.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY environment variable is not set')
    }

    resendInstance = new Resend(process.env.RESEND_API_KEY)
    return resendInstance
  } catch (error) {
    console.error('Failed to initialize Resend:', error.message)
    throw new Error(`Resend initialization failed: ${error.message}`)
  }
}

// Export a function that ensures Resend is initialized
export function getResend() {
  return initializeResend()
}

export interface EmailOptions {
  to: string
  subject: string
  html?: string
  text?: string
  from?: string
  tag?: string
}

// Send a simple email
export async function sendEmail(options: EmailOptions) {
  try {
    const resendClient = initializeResend()
    const fromEmail = options.from || process.env.RESEND_FROM_EMAIL!

    const result = await resendClient.emails.send({
      from: fromEmail,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
      tags: options.tag ? [{ name: 'type', value: options.tag }] : undefined,
    })
    
    console.log('✅ Email sent successfully:', {
      to: options.to,
      subject: options.subject,
      id: result.data?.id
    })
    
    return { success: true, id: result.data?.id }
  } catch (error) {
    console.error('❌ Failed to send email:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Test email connection
export async function testEmailConnection() {
  try {
    const resendClient = initializeResend()
    // Send a test email to verify connection
    const testResult = await resendClient.emails.send({
      from: process.env.RESEND_FROM_EMAIL!,
      to: '<EMAIL>', // Resend's test email
      subject: 'Connection Test',
      html: '<p>Testing Resend connection</p>',
    })
    
    console.log('✅ Resend connection successful')
    return { success: true, id: testResult.data?.id }
  } catch (error) {
    console.error('❌ Resend connection failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Email templates for common use cases
export const EmailTemplates = {
  // Welcome email for new users
  welcome: (userName: string, userRole: 'subscriber' | 'writer') => ({
    subject: `Welcome to OnlyDiary, ${userName}! 🌟`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to OnlyDiary</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="text-align: center; padding: 40px 20px 30px; background-color: #ffffff;">
            <h1 style="color: #1f2937; font-size: 28px; margin: 0; font-weight: normal;">Welcome to OnlyDiary</h1>
            <p style="color: #6b7280; font-size: 16px; margin: 10px 0 0 0; font-style: italic;">Where Life Becomes Literature</p>
          </div>
          
          <!-- Main Content -->
          <div style="padding: 0 20px 40px;">
            <div style="background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;">
              <h2 style="color: #1f2937; font-size: 20px; margin: 0 0 15px 0;">Hi ${userName}! 👋</h2>
              <p style="color: #374151; line-height: 1.6; margin: 0 0 15px 0; font-size: 16px;">
                Thank you for joining OnlyDiary! You're now part of a community where creators share their most intimate thoughts and stories.
              </p>
              ${userRole === 'writer' ? `
                <p style="color: #374151; line-height: 1.6; margin: 0; font-size: 16px;">
                  As a <strong>Creator</strong>, you can start sharing your stories and building your audience. Your voice matters, and we can't wait to see what you create!
                </p>
              ` : `
                <p style="color: #374151; line-height: 1.6; margin: 0; font-size: 16px;">
                  As a <strong>Reader</strong>, you can discover amazing creators and support them by subscribing to their content. Get ready for some incredible stories!
                </p>
              `}
            </div>
            
            <!-- CTA Buttons -->
            <div style="text-align: center; margin-bottom: 30px;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/dashboard"
                 style="background-color: #1f2937; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block; font-size: 16px; margin-bottom: 15px;">
                ${userRole === 'writer' ? 'Start Creating' : 'Discover Creators'}
              </a>
            </div>

            <!-- Viral Invite Section -->
            <div style="background-color: #fef3c7; border: 2px solid #f59e0b; padding: 25px; border-radius: 8px; margin-bottom: 25px; text-align: center;">
              <h3 style="color: #92400e; font-size: 18px; margin: 0 0 10px 0;">🌟 OnlyDiary is Better with Friends!</h3>
              <p style="color: #78350f; line-height: 1.5; margin: 0 0 15px 0; font-size: 15px;">
                Help us grow our intimate community! Invite your closest friends to discover amazing creators and authentic stories.
              </p>
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/invite-friends?ref=${userName.replace(/\s+/g, '')}"
                 style="background-color: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block; font-size: 15px;">
                📱 Invite Friends via Text
              </a>
            </div>
            
            <!-- Footer -->
            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
              <p style="color: #6b7280; font-size: 14px; margin: 0; line-height: 1.5;">
                Questions? Reply to this email - we'd love to help!<br>
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #1f2937; text-decoration: none;">OnlyDiary.app</a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Welcome to OnlyDiary, ${userName}!

Thank you for joining OnlyDiary! You're now part of a community where creators share their most intimate thoughts and stories.

${userRole === 'writer' 
  ? 'As a Creator, you can start sharing your stories and building your audience. Your voice matters!'
  : 'As a Reader, you can discover amazing creators and support them by subscribing to their content.'
}

Get started: ${process.env.NEXT_PUBLIC_SITE_URL}/dashboard

🌟 OnlyDiary is Better with Friends!
Help us grow our intimate community! Invite your closest friends to discover amazing creators and authentic stories.

Invite friends via text: ${process.env.NEXT_PUBLIC_SITE_URL}/invite-friends?ref=${userName.replace(/\s+/g, '')}

Questions? Reply to this email - we'd love to help!
OnlyDiary.app
    `,
    tag: 'welcome'
  }),

  // Password reset email
  passwordReset: (resetLink: string) => ({
    subject: 'Reset your OnlyDiary password',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="text-align: center; padding: 40px 20px 30px;">
            <h1 style="color: #1f2937; font-size: 24px; margin: 0;">Password Reset</h1>
          </div>
          
          <!-- Main Content -->
          <div style="padding: 0 20px 40px;">
            <div style="background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;">
              <p style="color: #374151; line-height: 1.6; margin: 0 0 20px 0; font-size: 16px;">
                We received a request to reset your OnlyDiary password. Click the button below to create a new password:
              </p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetLink}" 
                   style="background-color: #dc2626; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block; font-size: 16px;">
                  Reset Password
                </a>
              </div>
              
              <p style="color: #6b7280; font-size: 14px; margin: 0; line-height: 1.5;">
                This link will expire in 1 hour. If you didn't request this reset, you can safely ignore this email.
              </p>
            </div>
            
            <!-- Footer -->
            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
              <p style="color: #6b7280; font-size: 14px; margin: 0;">
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #1f2937; text-decoration: none;">OnlyDiary.app</a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Password Reset - OnlyDiary

We received a request to reset your OnlyDiary password. 

Reset your password: ${resetLink}

This link will expire in 1 hour. If you didn't request this reset, you can safely ignore this email.

OnlyDiary.app
    `,
    tag: 'password-reset'
  }),

  // Confirmation reminder email
  confirmationReminder: (userName: string) => ({
    subject: `${userName}, please confirm your OnlyDiary account 📧`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Confirm Your OnlyDiary Account</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="text-align: center; padding: 40px 20px 30px; background-color: #ffffff;">
            <h1 style="color: #1f2937; font-size: 28px; margin: 0; font-weight: normal;">Almost there! 📧</h1>
            <p style="color: #6b7280; font-size: 16px; margin: 10px 0 0 0; font-style: italic;">Just one more step to join OnlyDiary</p>
          </div>

          <!-- Main Content -->
          <div style="padding: 0 20px 40px;">
            <div style="background-color: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 30px;">
              <h2 style="color: #1f2937; font-size: 20px; margin: 0 0 15px 0;">Hi ${userName}! 👋</h2>
              <p style="color: #374151; line-height: 1.6; margin: 0 0 15px 0; font-size: 16px;">
                We noticed you signed up for OnlyDiary but haven't confirmed your email address yet. We're excited to have you join our community of authentic storytellers!
              </p>

              <p style="color: #374151; line-height: 1.6; margin: 0 0 10px 0; font-size: 16px; font-weight: 500;">
                Why confirm your email?
              </p>

              <ul style="color: #374151; line-height: 1.6; margin: 0 0 15px 0; padding-left: 20px;">
                <li>Access your personalized dashboard</li>
                <li>Start writing and sharing your stories</li>
                <li>Follow and support amazing creators</li>
                <li>Receive important updates about your account</li>
              </ul>

              <div style="background-color: #fef3c7; border: 1px solid #f59e0b; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p style="color: #92400e; font-weight: 500; margin: 0 0 5px 0;">📧 Check your email</p>
                <p style="color: #78350f; line-height: 1.5; margin: 0; font-size: 14px;">
                  Look for our confirmation email in your inbox (and spam folder, just in case). Click the confirmation link to activate your account.
                </p>
              </div>
            </div>

            <!-- CTA Button -->
            <div style="text-align: center; margin-bottom: 30px;">
              <p style="color: #6b7280; margin: 0 0 15px 0; font-size: 14px;">
                Didn't receive the confirmation email?
              </p>
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/login"
                 style="background-color: #1f2937; color: white; padding: 14px 28px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block; font-size: 16px;">
                Resend Confirmation →
              </a>
            </div>

            <!-- What makes OnlyDiary special -->
            <div style="background-color: #f0fdf4; border: 1px solid #16a34a; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
              <h3 style="color: #166534; font-size: 18px; margin: 0 0 10px 0;">✨ What makes OnlyDiary special?</h3>
              <p style="color: #15803d; line-height: 1.5; margin: 0; font-size: 15px;">
                Unlike other social networks, OnlyDiary is built on diary entries, not posts. Share your authentic self and connect with others through genuine storytelling.
              </p>
            </div>

            <!-- Footer -->
            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; text-align: center;">
              <p style="color: #6b7280; font-size: 14px; margin: 0; line-height: 1.5;">
                Need help? Reply to this email - we're here to help!<br>
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #1f2937; text-decoration: none;">OnlyDiary.app</a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Almost there, ${userName}! 📧

We noticed you signed up for OnlyDiary but haven't confirmed your email address yet. We're excited to have you join our community of authentic storytellers!

Why confirm your email?
• Access your personalized dashboard
• Start writing and sharing your stories
• Follow and support amazing creators
• Receive important updates about your account

📧 Check your email
Look for our confirmation email in your inbox (and spam folder, just in case). Click the confirmation link to activate your account.

Didn't receive the confirmation email?
Visit ${process.env.NEXT_PUBLIC_SITE_URL}/login to resend it.

✨ What makes OnlyDiary special?
Unlike other social networks, OnlyDiary is built on diary entries, not posts. Share your authentic self and connect with others through genuine storytelling.

Need help? Reply to this email - we're here to help!
OnlyDiary.app
    `,
    tag: 'confirmation-reminder'
  }),

  // Test email
  test: () => ({
    subject: 'OnlyDiary Email Test ✅',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Test</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <div style="text-align: center; padding: 40px 20px;">
            <div style="background-color: #f0fdf4; padding: 30px; border-radius: 8px; border: 2px solid #16a34a;">
              <h1 style="color: #16a34a; font-size: 24px; margin: 0 0 15px 0;">✅ Email Test Successful!</h1>
              <p style="color: #374151; line-height: 1.6; margin: 0; font-size: 16px;">
                Your OnlyDiary email system is working perfectly with Resend. Welcome emails, notifications, and digests are ready to go!
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
✅ Email Test Successful!

Your OnlyDiary email system is working perfectly with Resend. Welcome emails, notifications, and digests are ready to go!
    `,
    tag: 'test'
  }),

  // Engagement milestone emails
  first_followers: (userName: string, followerCount: number, followers: Array<{name: string, avatar?: string}>) => ({
    subject: `🚀 ${userName}, your creative journey is taking off! ${followerCount} people believe in you`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Creative Journey is Taking Off!</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="text-align: center; padding: 40px 20px 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <h1 style="color: white; font-size: 32px; margin: 0; font-weight: bold;">🚀 Your Dreams Are Taking Flight!</h1>
            <p style="color: #e0e7ff; font-size: 18px; margin: 15px 0 0 0; font-style: italic;">The world is discovering your unique voice</p>
          </div>

          <!-- Main Content -->
          <div style="padding: 0 20px 40px;">
            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%); padding: 35px; border-radius: 12px; margin: 30px 0; border: 3px solid #16a34a; box-shadow: 0 4px 20px rgba(22, 163, 74, 0.1);">
              <h2 style="color: #166534; font-size: 24px; margin: 0 0 20px 0; text-align: center;">✨ ${followerCount} Amazing People Are Following Your Journey! ✨</h2>
              <p style="color: #15803d; line-height: 1.8; margin: 0 0 25px 0; font-size: 17px; text-align: center;">
                <strong>This is your moment, ${userName}!</strong> Real people are choosing to follow your authentic stories because your voice matters. You're not just writing—you're building a community that believes in your creative vision.
              </p>

              <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin: 25px 0; border: 2px solid #d1fae5;">
                <h3 style="color: #166534; font-size: 18px; margin: 0 0 15px 0; text-align: center;">🌟 Your Incredible Supporters:</h3>
                <div style="text-align: center;">
                  ${followers.map(follower => `
                    <div style="display: inline-block; margin: 8px 12px; padding: 10px 16px; background: linear-gradient(135deg, #16a34a, #22c55e); color: white; border-radius: 25px; font-weight: 600; box-shadow: 0 2px 8px rgba(22, 163, 74, 0.3);">
                      ${follower.name}
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>

            <!-- Monetization Opportunity -->
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 30px; border-radius: 12px; margin: 30px 0; border: 3px solid #f59e0b; box-shadow: 0 4px 20px rgba(245, 158, 11, 0.1);">
              <h3 style="color: #92400e; font-size: 20px; margin: 0 0 15px 0; text-align: center;">💰 Ready to Turn Your Passion Into Profit?</h3>
              <p style="color: #78350f; line-height: 1.7; margin: 0 0 20px 0; font-size: 16px; text-align: center;">
                <strong>You're already creating magic—now let's make it profitable!</strong> OnlyDiary offers monetization opportunities that exist nowhere else:
              </p>
              <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin: 15px 0;">
                <ul style="color: #78350f; line-height: 1.6; margin: 0; padding-left: 0; font-size: 15px; list-style: none;">
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #f59e0b; font-weight: bold;">📚</span>
                    <strong>Publish & Sell Your Books:</strong> Turn your stories into published ebooks with our seamless publishing platform
                  </li>
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #f59e0b; font-weight: bold;">💝</span>
                    <strong>Subscription Income:</strong> Loyal readers pay monthly to access your premium diary entries
                  </li>
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #f59e0b; font-weight: bold;">🎙️</span>
                    <strong>Audio Storytelling:</strong> Share voice recordings and build deeper connections with your audience
                  </li>
                  <li style="margin-bottom: 0; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #f59e0b; font-weight: bold;">🍳</span>
                    <strong>Recipe Monetization:</strong> Share your culinary creations and cooking videos for profit
                  </li>
                </ul>
              </div>
            </div>

            <!-- CTA Buttons -->
            <div style="text-align: center; margin: 40px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/write/diary"
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 18px; margin: 10px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); transition: all 0.3s ease;">
                ✍️ Write Your Next Masterpiece
              </a>
              <br>
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/setup-subscriptions"
                 style="background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 18px; margin: 10px; box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);">
                💰 Start Earning From Your Stories
              </a>
            </div>

            <!-- Success Path -->
            <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); padding: 30px; border-radius: 12px; margin: 30px 0; border: 3px solid #3b82f6;">
              <h3 style="color: #1e40af; font-size: 20px; margin: 0 0 15px 0; text-align: center;">🎯 Your Path to Creative Success</h3>
              <div style="color: #1e3a8a; line-height: 1.7; font-size: 16px;">
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #3b82f6;">
                  <strong>📈 Next Milestone: 25 Followers</strong><br>
                  Keep sharing your authentic voice. Each story brings you closer to building a sustainable creative income.
                </div>
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #3b82f6;">
                  <strong>💡 Pro Tip:</strong> Engage with your followers' comments—every reply builds deeper loyalty and increases your earning potential.
                </div>
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #3b82f6;">
                  <strong>🚀 Success Strategy:</strong> Post consistently, experiment with different story formats, and watch your community (and income) grow!
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div style="border-top: 2px solid #e5e7eb; padding-top: 30px; text-align: center;">
              <p style="color: #6b7280; font-size: 16px; margin: 0; line-height: 1.6;">
                <strong>Your creative dreams deserve to be profitable.</strong><br>
                We're here to help you turn your passion into your livelihood.<br><br>
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #667eea; text-decoration: none; font-weight: 600;">OnlyDiary.app - Where Creativity Meets Opportunity</a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
🚀 Your Dreams Are Taking Flight, ${userName}!

${followerCount} amazing people are following your journey! This is your moment - real people are choosing to follow your authentic stories because your voice matters.

Your incredible supporters: ${followers.map(f => f.name).join(', ')}

💰 READY TO TURN YOUR PASSION INTO PROFIT?

OnlyDiary offers monetization opportunities that exist nowhere else:
📚 Publish & Sell Your Books - Turn stories into published ebooks
💝 Subscription Income - Loyal readers pay monthly for premium content
🎙️ Audio Storytelling - Share voice recordings for deeper connections
🍳 Recipe Monetization - Share culinary creations and cooking videos

🎯 YOUR PATH TO CREATIVE SUCCESS:
📈 Next Milestone: 25 Followers
💡 Pro Tip: Engage with comments to build deeper loyalty
🚀 Success Strategy: Post consistently and experiment with formats

Your creative dreams deserve to be profitable. We're here to help you turn your passion into your livelihood.

Write your next masterpiece: ${process.env.NEXT_PUBLIC_SITE_URL}/write/diary
Start earning from your stories: ${process.env.NEXT_PUBLIC_SITE_URL}/setup-subscriptions

OnlyDiary.app - Where Creativity Meets Opportunity
    `,
    tag: 'engagement-followers'
  }),

  first_comments: (userName: string, commentCount: number) => ({
    subject: `🔥 ${userName}, your stories are igniting conversations! ${commentCount} people can't stop talking about your work`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Your Stories Are Igniting Conversations!</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="text-align: center; padding: 40px 20px 30px; background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); color: white;">
            <h1 style="color: white; font-size: 32px; margin: 0; font-weight: bold;">🔥 You're Creating Magic!</h1>
            <p style="color: #bfdbfe; font-size: 18px; margin: 15px 0 0 0; font-style: italic;">Your words are sparking real conversations</p>
          </div>

          <!-- Main Content -->
          <div style="padding: 0 20px 40px;">
            <div style="background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%); padding: 35px; border-radius: 12px; margin: 30px 0; border: 3px solid #3b82f6; box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);">
              <h2 style="color: #1e40af; font-size: 26px; margin: 0 0 20px 0; text-align: center;">💬 ${commentCount} People Are Talking About Your Stories! 💬</h2>
              <p style="color: #1e3a8a; line-height: 1.8; margin: 0 0 25px 0; font-size: 18px; text-align: center;">
                <strong>This is incredible, ${userName}!</strong> Your authentic voice isn't just being heard—it's creating genuine connections. People are so moved by your stories that they're taking time to share their thoughts, feelings, and experiences with you.
              </p>
              <div style="background-color: #ffffff; padding: 25px; border-radius: 10px; border: 2px solid #93c5fd; text-align: center;">
                <p style="color: #1e40af; font-size: 17px; margin: 0; font-weight: 600;">
                  🌟 You're not just a writer—you're a conversation starter, a community builder, a voice that matters! 🌟
                </p>
              </div>
            </div>

            <!-- Engagement Power -->
            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 30px; border-radius: 12px; margin: 30px 0; border: 3px solid #16a34a;">
              <h3 style="color: #166534; font-size: 22px; margin: 0 0 20px 0; text-align: center;">💰 Comments = Community = Cash Flow!</h3>
              <p style="color: #15803d; line-height: 1.7; margin: 0 0 20px 0; font-size: 16px; text-align: center;">
                <strong>Every comment is a sign that you're building something valuable!</strong> Engaged readers become loyal subscribers, book buyers, and lifelong supporters of your creative journey.
              </p>
              <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin: 15px 0;">
                <h4 style="color: #166534; font-size: 18px; margin: 0 0 15px 0; text-align: center;">🚀 Turn This Engagement Into Income:</h4>
                <ul style="color: #15803d; line-height: 1.6; margin: 0; padding-left: 0; font-size: 15px; list-style: none;">
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #16a34a; font-weight: bold;">💝</span>
                    <strong>Launch Paid Subscriptions:</strong> Your engaged readers are perfect candidates for premium content
                  </li>
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #16a34a; font-weight: bold;">📖</span>
                    <strong>Compile Your Best Stories:</strong> Turn your most-commented posts into a bestselling ebook
                  </li>
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #16a34a; font-weight: bold;">🎙️</span>
                    <strong>Add Audio Storytelling:</strong> Your voice creates even deeper connections (and higher earnings)
                  </li>
                  <li style="margin-bottom: 0; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #16a34a; font-weight: bold;">🌟</span>
                    <strong>Build Your Brand:</strong> Consistent engagement leads to sponsorship and collaboration opportunities
                  </li>
                </ul>
              </div>
            </div>

            <!-- CTA Buttons -->
            <div style="text-align: center; margin: 40px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/dashboard"
                 style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 18px; margin: 10px; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);">
                💬 Reply to Your Fans
              </a>
              <br>
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/setup-subscriptions"
                 style="background: linear-gradient(135deg, #16a34a 0%, #15803d 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 18px; margin: 10px; box-shadow: 0 4px 15px rgba(22, 163, 74, 0.4);">
                💰 Monetize This Engagement
              </a>
            </div>

            <!-- Success Momentum -->
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 30px; border-radius: 12px; margin: 30px 0; border: 3px solid #f59e0b;">
              <h3 style="color: #92400e; font-size: 20px; margin: 0 0 15px 0; text-align: center;">⚡ Ride This Wave of Success!</h3>
              <div style="color: #78350f; line-height: 1.7; font-size: 16px;">
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #f59e0b;">
                  <strong>🎯 Next Goal: 50 Comments</strong><br>
                  You're building serious momentum! Keep engaging with every comment to fuel your growth.
                </div>
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #f59e0b;">
                  <strong>💡 Pro Strategy:</strong> Ask questions in your stories to encourage more comments and deeper engagement.
                </div>
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #f59e0b;">
                  <strong>🚀 Success Tip:</strong> Reply to every comment personally—this builds the loyal community that pays for premium content!
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div style="border-top: 2px solid #e5e7eb; padding-top: 30px; text-align: center;">
              <p style="color: #6b7280; font-size: 16px; margin: 0; line-height: 1.6;">
                <strong>Your voice is creating real impact.</strong><br>
                Every comment proves you have what it takes to build a profitable creative career.<br><br>
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #3b82f6; text-decoration: none; font-weight: 600;">OnlyDiary.app - Where Conversations Become Cash</a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
🔥 You're Creating Magic, ${userName}!

${commentCount} people are talking about your stories! Your authentic voice isn't just being heard—it's creating genuine connections. People are so moved by your stories that they're taking time to share their thoughts with you.

💰 COMMENTS = COMMUNITY = CASH FLOW!

Every comment is a sign that you're building something valuable! Engaged readers become loyal subscribers, book buyers, and lifelong supporters.

🚀 TURN THIS ENGAGEMENT INTO INCOME:
💝 Launch Paid Subscriptions - Your engaged readers are perfect candidates
📖 Compile Your Best Stories - Turn most-commented posts into bestselling ebooks
🎙️ Add Audio Storytelling - Your voice creates deeper connections and higher earnings
🌟 Build Your Brand - Consistent engagement leads to sponsorship opportunities

⚡ RIDE THIS WAVE OF SUCCESS!
🎯 Next Goal: 50 Comments
💡 Pro Strategy: Ask questions in stories to encourage more comments
🚀 Success Tip: Reply personally to build the loyal community that pays for premium content

Your voice is creating real impact. Every comment proves you have what it takes to build a profitable creative career.

Reply to your fans: ${process.env.NEXT_PUBLIC_SITE_URL}/dashboard
Monetize this engagement: ${process.env.NEXT_PUBLIC_SITE_URL}/setup-subscriptions

OnlyDiary.app - Where Conversations Become Cash
    `,
    tag: 'engagement-comments'
  }),

  first_likes: (userName: string, likeCount: number) => ({
    subject: `💖 ${userName}, you're touching hearts! ${likeCount} people LOVE your authentic voice`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>You're Touching Hearts!</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Georgia, serif; background-color: #f9fafb;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          <!-- Header -->
          <div style="text-align: center; padding: 40px 20px 30px; background: linear-gradient(135deg, #ec4899 0%, #be185d 100%); color: white;">
            <h1 style="color: white; font-size: 32px; margin: 0; font-weight: bold;">💖 You're Touching Hearts!</h1>
            <p style="color: #fce7f3; font-size: 18px; margin: 15px 0 0 0; font-style: italic;">Your authentic voice is creating real emotional connections</p>
          </div>

          <!-- Main Content -->
          <div style="padding: 0 20px 40px;">
            <div style="background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%); padding: 35px; border-radius: 12px; margin: 30px 0; border: 3px solid #ef4444; box-shadow: 0 4px 20px rgba(239, 68, 68, 0.1);">
              <h2 style="color: #dc2626; font-size: 26px; margin: 0 0 20px 0; text-align: center;">❤️ ${likeCount} Hearts Touched by Your Stories! ❤️</h2>
              <p style="color: #b91c1c; line-height: 1.8; margin: 0 0 25px 0; font-size: 18px; text-align: center;">
                <strong>This is beautiful, ${userName}!</strong> Every single like represents a real person who stopped scrolling, read your words, and felt something deep enough to hit that heart button. You're not just writing—you're healing, inspiring, and connecting with souls around the world.
              </p>
              <div style="background-color: #ffffff; padding: 25px; border-radius: 10px; border: 2px solid #fca5a5; text-align: center;">
                <p style="color: #dc2626; font-size: 17px; margin: 0; font-weight: 600;">
                  🌟 Your vulnerability is your superpower—and it's about to become your livelihood! 🌟
                </p>
              </div>
            </div>

            <!-- Love = Money -->
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 30px; border-radius: 12px; margin: 30px 0; border: 3px solid #f59e0b;">
              <h3 style="color: #92400e; font-size: 22px; margin: 0 0 20px 0; text-align: center;">💰 Love Translates to Loyalty (and Profit!)</h3>
              <p style="color: #78350f; line-height: 1.7; margin: 0 0 20px 0; font-size: 16px; text-align: center;">
                <strong>Those hearts aren't just validation—they're your future income!</strong> People who love your free content become the subscribers, book buyers, and superfans who support your creative dreams.
              </p>
              <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; margin: 15px 0;">
                <h4 style="color: #92400e; font-size: 18px; margin: 0 0 15px 0; text-align: center;">🚀 Transform Love Into Livelihood:</h4>
                <ul style="color: #78350f; line-height: 1.6; margin: 0; padding-left: 0; font-size: 15px; list-style: none;">
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #f59e0b; font-weight: bold;">💝</span>
                    <strong>Premium Diary Access:</strong> Your most-liked stories prove people will pay for more intimate content
                  </li>
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #f59e0b; font-weight: bold;">📚</span>
                    <strong>Bestselling Books:</strong> Compile your heart-touching stories into profitable ebooks
                  </li>
                  <li style="margin-bottom: 12px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #f59e0b; font-weight: bold;">🎙️</span>
                    <strong>Audio Intimacy:</strong> Your voice reading these beloved stories creates premium experiences
                  </li>
                  <li style="margin-bottom: 0; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #f59e0b; font-weight: bold;">🌟</span>
                    <strong>Brand Partnerships:</strong> Authentic voices like yours attract lucrative collaboration offers
                  </li>
                </ul>
              </div>
            </div>

            <!-- CTA Buttons -->
            <div style="text-align: center; margin: 40px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/write/diary"
                 style="background: linear-gradient(135deg, #ec4899 0%, #be185d 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 18px; margin: 10px; box-shadow: 0 4px 15px rgba(236, 72, 153, 0.4);">
                💖 Write Another Heart-Toucher
              </a>
              <br>
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/publishing"
                 style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; display: inline-block; font-size: 18px; margin: 10px; box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);">
                📚 Turn Stories Into Sales
              </a>
            </div>

            <!-- Viral Potential -->
            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); padding: 30px; border-radius: 12px; margin: 30px 0; border: 3px solid #16a34a;">
              <h3 style="color: #166534; font-size: 20px; margin: 0 0 15px 0; text-align: center;">🚀 You're Building Viral Momentum!</h3>
              <div style="color: #15803d; line-height: 1.7; font-size: 16px;">
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #16a34a;">
                  <strong>🎯 Next Milestone: 100 Likes</strong><br>
                  You're on fire! Keep sharing your authentic truth—viral success is within reach.
                </div>
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #16a34a;">
                  <strong>💡 Viral Strategy:</strong> Your most-liked posts reveal what resonates—create more content in that style!
                </div>
                <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #16a34a;">
                  <strong>🚀 Success Secret:</strong> Emotional authenticity = viral potential = sustainable income. You've got all three!
                </div>
              </div>
            </div>

            <!-- Footer -->
            <div style="border-top: 2px solid #e5e7eb; padding-top: 30px; text-align: center;">
              <p style="color: #6b7280; font-size: 16px; margin: 0; line-height: 1.6;">
                <strong>Your heart-centered stories are changing lives.</strong><br>
                Every like proves you have the emotional intelligence to build a thriving creative business.<br><br>
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #ec4899; text-decoration: none; font-weight: 600;">OnlyDiary.app - Where Hearts Connect and Creators Profit</a>
              </p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
💖 You're Touching Hearts, ${userName}!

${likeCount} hearts touched by your stories! Every single like represents a real person who stopped scrolling, read your words, and felt something deep enough to hit that heart button. You're not just writing—you're healing, inspiring, and connecting with souls around the world.

💰 LOVE TRANSLATES TO LOYALTY (AND PROFIT!)

Those hearts aren't just validation—they're your future income! People who love your free content become the subscribers, book buyers, and superfans who support your creative dreams.

🚀 TRANSFORM LOVE INTO LIVELIHOOD:
💝 Premium Diary Access - Your most-liked stories prove people will pay for more
📚 Bestselling Books - Compile heart-touching stories into profitable ebooks
🎙️ Audio Intimacy - Your voice reading beloved stories creates premium experiences
🌟 Brand Partnerships - Authentic voices like yours attract lucrative collaborations

🚀 YOU'RE BUILDING VIRAL MOMENTUM!
🎯 Next Milestone: 100 Likes
💡 Viral Strategy: Your most-liked posts reveal what resonates—create more in that style
🚀 Success Secret: Emotional authenticity = viral potential = sustainable income

Your heart-centered stories are changing lives. Every like proves you have the emotional intelligence to build a thriving creative business.

Write another heart-toucher: ${process.env.NEXT_PUBLIC_SITE_URL}/write/diary
Turn stories into sales: ${process.env.NEXT_PUBLIC_SITE_URL}/publishing

OnlyDiary.app - Where Hearts Connect and Creators Profit
    `,
    tag: 'engagement-likes'
  })
}
