// Monetization Guard - Ensures users have Stripe Connect before monetizing
import { createSupabaseClient } from '@/lib/supabase/client'

export interface MonetizationStatus {
  canMonetize: boolean
  hasStripeAccount: boolean
  onboardingComplete: boolean
  reason?: string
  actionRequired?: 'connect_stripe' | 'complete_onboarding' | 'none'
}

export async function checkMonetizationEligibility(userId?: string): Promise<MonetizationStatus> {
  try {
    const supabase = createSupabaseClient()
    
    // Get current user if not provided
    let targetUserId = userId
    if (!targetUserId) {
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError || !user) {
        return {
          canMonetize: false,
          hasStripeAccount: false,
          onboardingComplete: false,
          reason: 'User not authenticated',
          actionRequired: 'none'
        }
      }
      targetUserId = user.id
    }

    // Check user's Stripe status
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('stripe_account_id, stripe_onboarding_complete')
      .eq('id', targetUserId)
      .single()

    if (profileError || !profile) {
      return {
        canMonetize: false,
        hasStripeAccount: false,
        onboardingComplete: false,
        reason: 'User profile not found',
        actionRequired: 'none'
      }
    }

    const hasStripeAccount = !!profile.stripe_account_id
    const onboardingComplete = profile.stripe_onboarding_complete || false

    // Determine monetization status
    if (!hasStripeAccount) {
      return {
        canMonetize: false,
        hasStripeAccount: false,
        onboardingComplete: false,
        reason: 'No Stripe account connected',
        actionRequired: 'connect_stripe'
      }
    }

    if (!onboardingComplete) {
      return {
        canMonetize: false,
        hasStripeAccount: true,
        onboardingComplete: false,
        reason: 'Stripe onboarding not complete',
        actionRequired: 'complete_onboarding'
      }
    }

    return {
      canMonetize: true,
      hasStripeAccount: true,
      onboardingComplete: true,
      reason: 'Ready to monetize',
      actionRequired: 'none'
    }

  } catch (error) {
    console.error('Error checking monetization eligibility:', error)
    return {
      canMonetize: false,
      hasStripeAccount: false,
      onboardingComplete: false,
      reason: 'Error checking status',
      actionRequired: 'none'
    }
  }
}

export const MONETIZATION_FEATURES = {
  SUBSCRIPTIONS: 'subscriptions',
  BOOK_SALES: 'book_sales',
  RECIPE_SALES: 'recipe_sales',
  DONATIONS: 'donations',
  AUDIO_POSTS: 'audio_posts'
} as const

export type MonetizationFeature = typeof MONETIZATION_FEATURES[keyof typeof MONETIZATION_FEATURES]

export function getMonetizationBlockedMessage(feature: MonetizationFeature): {
  title: string
  message: string
  ctaText: string
} {
  const messages = {
    [MONETIZATION_FEATURES.SUBSCRIPTIONS]: {
      title: 'Connect Stripe to Enable Subscriptions',
      message: 'To start earning from subscriptions, you need to connect your Stripe account for secure payments.',
      ctaText: 'Connect Stripe Account'
    },
    [MONETIZATION_FEATURES.BOOK_SALES]: {
      title: 'Connect Stripe to Sell Books',
      message: 'To publish and sell your books, you need to connect your Stripe account to receive payments.',
      ctaText: 'Connect Stripe Account'
    },
    [MONETIZATION_FEATURES.RECIPE_SALES]: {
      title: 'Connect Stripe to Sell Recipes',
      message: 'To monetize your recipes, you need to connect your Stripe account for secure transactions.',
      ctaText: 'Connect Stripe Account'
    },
    [MONETIZATION_FEATURES.DONATIONS]: {
      title: 'Connect Stripe to Receive Donations',
      message: 'To receive donations from your readers, you need to connect your Stripe account.',
      ctaText: 'Connect Stripe Account'
    },
    [MONETIZATION_FEATURES.AUDIO_POSTS]: {
      title: 'Connect Stripe to Monetize Audio',
      message: 'To earn from premium audio content, you need to connect your Stripe account.',
      ctaText: 'Connect Stripe Account'
    }
  }

  return messages[feature]
}

// Hook for React components
import { useState, useEffect } from 'react'

export function useMonetizationStatus(userId?: string) {
  const [status, setStatus] = useState<MonetizationStatus>({
    canMonetize: false,
    hasStripeAccount: false,
    onboardingComplete: false,
    reason: 'Loading...',
    actionRequired: 'none'
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkMonetizationEligibility(userId)
      .then(setStatus)
      .finally(() => setLoading(false))
  }, [userId])

  return { status, loading, refresh: () => checkMonetizationEligibility(userId).then(setStatus) }
}
