// API Monetization Guard - Server-side protection for monetization endpoints
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Use service role for server-side checks
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface ApiMonetizationCheck {
  allowed: boolean
  userId?: string
  reason?: string
  statusCode?: number
}

export async function checkApiMonetizationAccess(request: NextRequest): Promise<ApiMonetizationCheck> {
  try {
    // Get user from request (you might need to adjust this based on your auth setup)
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return {
        allowed: false,
        reason: 'No authorization header',
        statusCode: 401
      }
    }

    // Extract JWT token (adjust based on your auth format)
    const token = authHeader.replace('Bearer ', '')
    
    // Verify the JWT and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return {
        allowed: false,
        reason: 'Invalid or expired token',
        statusCode: 401
      }
    }

    // Check user's Stripe status
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('stripe_account_id, stripe_onboarding_complete')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return {
        allowed: false,
        userId: user.id,
        reason: 'User profile not found',
        statusCode: 404
      }
    }

    // Check if user can monetize
    if (!profile.stripe_account_id) {
      return {
        allowed: false,
        userId: user.id,
        reason: 'No Stripe account connected. Please connect your Stripe account to enable monetization.',
        statusCode: 403
      }
    }

    if (!profile.stripe_onboarding_complete) {
      return {
        allowed: false,
        userId: user.id,
        reason: 'Stripe onboarding not complete. Please complete your Stripe setup to enable monetization.',
        statusCode: 403
      }
    }

    return {
      allowed: true,
      userId: user.id
    }

  } catch (error) {
    console.error('Error checking API monetization access:', error)
    return {
      allowed: false,
      reason: 'Internal server error',
      statusCode: 500
    }
  }
}

// Middleware wrapper for API routes that require monetization
export function withMonetizationGuard(handler: (request: NextRequest, context: any) => Promise<NextResponse>) {
  return async (request: NextRequest, context: any) => {
    const check = await checkApiMonetizationAccess(request)
    
    if (!check.allowed) {
      return NextResponse.json(
        { 
          error: 'Monetization not enabled',
          reason: check.reason,
          action_required: !check.userId ? 'login' : 'connect_stripe'
        },
        { status: check.statusCode || 403 }
      )
    }

    // Add userId to context for convenience
    context.userId = check.userId
    
    return handler(request, context)
  }
}

// Simple check for server components
export async function checkUserMonetization(userId: string): Promise<{
  canMonetize: boolean
  hasStripeAccount: boolean
  onboardingComplete: boolean
}> {
  try {
    const { data: profile, error } = await supabase
      .from('users')
      .select('stripe_account_id, stripe_onboarding_complete')
      .eq('id', userId)
      .single()

    if (error || !profile) {
      return {
        canMonetize: false,
        hasStripeAccount: false,
        onboardingComplete: false
      }
    }

    const hasStripeAccount = !!profile.stripe_account_id
    const onboardingComplete = profile.stripe_onboarding_complete || false

    return {
      canMonetize: hasStripeAccount && onboardingComplete,
      hasStripeAccount,
      onboardingComplete
    }
  } catch (error) {
    console.error('Error checking user monetization:', error)
    return {
      canMonetize: false,
      hasStripeAccount: false,
      onboardingComplete: false
    }
  }
}

// List of API endpoints that require monetization
export const MONETIZATION_PROTECTED_ENDPOINTS = [
  '/api/stripe/checkout',
  '/api/subscribe',
  '/api/books/purchase',
  '/api/recipes/purchase',
  '/api/setup-subscriptions',
  '/api/profile/pricing',
  '/api/withdraw'
] as const

// Check if an endpoint requires monetization
export function isMonetizationProtectedEndpoint(pathname: string): boolean {
  return MONETIZATION_PROTECTED_ENDPOINTS.some(endpoint => 
    pathname.startsWith(endpoint)
  )
}
