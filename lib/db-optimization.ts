// Database optimization strategies for scaling to millions of users

import { createSupabaseServerClient } from '@/lib/supabase/server'
import { cache, cacheKeys, getCachedOrFetch } from '@/lib/cache'
import { analytics } from '@/lib/analytics'

// Connection pooling and query optimization
export class DatabaseOptimizer {
  private queryCache = new Map<string, any>()
  
  // Optimized user queries with proper indexing
  static async getUserWithCache(userId: string) {
    return getCachedOrFetch(
      cacheKeys.user(userId),
      async () => {
        const supabase = await createSupabaseServerClient()
        const { data, error } = await supabase
          .from('users')
          .select('id, name, email, profile_picture_url, avatar, created_at')
          .eq('id', userId)
          .single()
        
        if (error) throw error
        return data
      },
      600 // 10 minutes cache
    )
  }

  // Optimized OnlyDuos queries with pagination and proper joins
  static async getUserDuosOptimized(userId: string, page: number = 0, limit: number = 20) {
    const cacheKey = `${cacheKeys.userDuos(userId)}:page:${page}`
    
    return getCachedOrFetch(
      cacheKey,
      async () => {
        const supabase = await createSupabaseServerClient()
        
        // Use proper indexing and limit results
        const { data: duos, error } = await supabase
          .from('duo_posts')
          .select(`
            id, status, created_at, responder_user_id,
            responder:users!duo_posts_responder_user_id_fkey(name, profile_picture_url, avatar)
          `)
          .eq('initiator_user_id', userId)
          .in('status', ['pending', 'awaiting_approval'])
          .not('responder_user_id', 'is', null)
          .order('created_at', { ascending: false })
          .range(page * limit, (page + 1) * limit - 1)

        if (error) throw error

        // Batch fetch parts for all duos to avoid N+1 queries
        if (duos && duos.length > 0) {
          const duoIds = duos.map(d => d.id)
          const { data: parts } = await supabase
            .from('duo_parts')
            .select('duo_post_id, part_number, author_user_id, media, created_at')
            .in('duo_post_id', duoIds)
            .order('part_number')

          return { duos, parts: parts || [] }
        }

        return { duos: [], parts: [] }
      },
      300 // 5 minutes cache
    )
  }

  // Optimized timeline queries with cursor-based pagination
  static async getTimelineOptimized(userId: string, cursor?: string, limit: number = 20) {
    const cacheKey = `timeline:${userId}:${cursor || 'latest'}`
    
    return getCachedOrFetch(
      cacheKey,
      async () => {
        const supabase = await createSupabaseServerClient()
        
        let query = supabase
          .from('posts')
          .select(`
            id, content, media, created_at, user_id,
            user:users!posts_user_id_fkey(name, profile_picture_url, avatar),
            _count:post_reactions(count)
          `)
          .order('created_at', { ascending: false })
          .limit(limit)

        if (cursor) {
          query = query.lt('created_at', cursor)
        }

        const { data, error } = await query

        if (error) throw error
        return data
      },
      180 // 3 minutes cache for timeline
    )
  }

  // Batch operations for efficiency
  static async batchCreateNotifications(notifications: Array<{
    user_id: string
    type: string
    title: string
    body: string
    data?: any
  }>) {
    const supabase = await createSupabaseServerClient()
    
    // Use batch insert for efficiency
    const { data, error } = await supabase
      .from('notifications')
      .insert(notifications)
      .select()

    if (error) throw error

    // Invalidate notification caches for affected users
    const userIds = [...new Set(notifications.map(n => n.user_id))]
    userIds.forEach(userId => {
      cache.delete(cacheKeys.notifications(userId))
    })

    return data
  }

  // Optimized search with full-text search
  static async searchUsersOptimized(query: string, limit: number = 10) {
    const cacheKey = `search:users:${query}:${limit}`
    
    return getCachedOrFetch(
      cacheKey,
      async () => {
        const supabase = await createSupabaseServerClient()
        
        // Use full-text search if available, otherwise ILIKE
        const { data, error } = await supabase
          .from('users')
          .select('id, name, profile_picture_url, avatar')
          .or(`name.ilike.%${query}%, email.ilike.%${query}%`)
          .limit(limit)

        if (error) throw error
        return data
      },
      300 // 5 minutes cache for search results
    )
  }

  // Database health monitoring
  static async getDbMetrics() {
    const supabase = await createSupabaseServerClient()
    
    try {
      // Monitor key metrics
      const metrics = await Promise.allSettled([
        // Active connections
        supabase.rpc('get_active_connections'),
        
        // Table sizes
        supabase.rpc('get_table_sizes'),
        
        // Slow queries
        supabase.rpc('get_slow_queries'),
        
        // Index usage
        supabase.rpc('get_index_usage')
      ])

      return {
        activeConnections: metrics[0].status === 'fulfilled' ? metrics[0].value : null,
        tableSizes: metrics[1].status === 'fulfilled' ? metrics[1].value : null,
        slowQueries: metrics[2].status === 'fulfilled' ? metrics[2].value : null,
        indexUsage: metrics[3].status === 'fulfilled' ? metrics[3].value : null
      }
    } catch (error) {
      analytics.trackError(error as Error, { context: 'db_metrics' })
      return null
    }
  }
}

// Database migration helpers for scaling
export class DatabaseScaling {
  // Partition large tables by date
  static async createPartitions(tableName: string, partitionColumn: string = 'created_at') {
    const supabase = await createSupabaseServerClient()
    
    // Example: Partition posts table by month
    const sql = `
      -- Create partitioned table
      CREATE TABLE IF NOT EXISTS ${tableName}_partitioned (
        LIKE ${tableName} INCLUDING ALL
      ) PARTITION BY RANGE (${partitionColumn});
      
      -- Create monthly partitions
      CREATE TABLE IF NOT EXISTS ${tableName}_y2024m01 
        PARTITION OF ${tableName}_partitioned 
        FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
      
      CREATE TABLE IF NOT EXISTS ${tableName}_y2024m02 
        PARTITION OF ${tableName}_partitioned 
        FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
    `
    
    // Execute partitioning (would need proper migration system)
    console.log('Partitioning SQL:', sql)
  }

  // Create optimized indexes for scale
  static async createOptimizedIndexes() {
    const indexes = [
      // User lookups
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_name_trgm ON users USING gin(name gin_trgm_ops)',
      
      // OnlyDuos queries
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_duo_posts_initiator_status ON duo_posts(initiator_user_id, status, created_at DESC)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_duo_posts_responder_status ON duo_posts(responder_user_id, status, created_at DESC)',
      
      // Parts queries
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_duo_parts_post_part ON duo_parts(duo_post_id, part_number)',
      
      // Timeline queries
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_created_at ON posts(created_at DESC)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_user_created ON posts(user_id, created_at DESC)',
      
      // Notifications
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_created ON notifications(user_id, created_at DESC)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_read ON notifications(user_id, read_at) WHERE read_at IS NULL'
    ]

    console.log('Recommended indexes for scale:', indexes)
  }

  // Read replica configuration
  static async configureReadReplicas() {
    // In production, configure read replicas for:
    // - Timeline queries
    // - Search queries  
    // - Analytics queries
    // - Reporting queries
    
    console.log('Configure read replicas for scaling reads')
  }
}

// Query performance monitoring
export function trackQueryPerformance(queryName: string, duration: number, rowCount?: number) {
  analytics.metric('db.query.duration', duration, 'ms', {
    query: queryName,
    rows: rowCount?.toString() || 'unknown'
  })

  // Alert on slow queries
  if (duration > 1000) { // 1 second
    analytics.track('slow_query', {
      query: queryName,
      duration,
      rowCount
    })
  }
}

// Connection pool monitoring
export function trackConnectionPool(activeConnections: number, maxConnections: number) {
  analytics.metric('db.connections.active', activeConnections, 'count')
  analytics.metric('db.connections.utilization', activeConnections / maxConnections, 'percentage')

  // Alert on high connection usage
  if (activeConnections / maxConnections > 0.8) {
    analytics.track('high_connection_usage', {
      active: activeConnections,
      max: maxConnections,
      utilization: activeConnections / maxConnections
    })
  }
}
