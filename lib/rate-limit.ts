import { NextRequest, NextResponse } from 'next/server'

// Rate limiting configuration for scaling to millions of users
interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Max requests per window
}

// Different rate limits for different operation types
const RATE_LIMITS: Record<string, RateLimitConfig> = {
  // Read operations - more permissive
  READ: { windowMs: 60 * 1000, maxRequests: 100 }, // 100 requests per minute
  
  // Write operations - more restrictive
  WRITE: { windowMs: 60 * 1000, maxRequests: 30 }, // 30 requests per minute
  
  // Authentication - very restrictive
  AUTH: { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 requests per 15 minutes
  
  // File uploads - restrictive
  UPLOAD: { windowMs: 60 * 1000, maxRequests: 10 }, // 10 uploads per minute
  
  // Email sending - very restrictive
  EMAIL: { windowMs: 60 * 1000, maxRequests: 3 }, // 3 emails per minute
}

// In-memory store for rate limiting (for development)
// In production, use Redis or similar distributed cache
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Clean up expired entries periodically
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}, 60 * 1000) // Clean up every minute

export interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  limit: number
}

export function checkRateLimit(
  request: NextRequest,
  operationType: keyof typeof RATE_LIMITS,
  identifier?: string
): RateLimitResult {
  const config = RATE_LIMITS[operationType]
  if (!config) {
    throw new Error(`Unknown operation type: ${operationType}`)
  }

  // Create unique identifier for this client
  const clientId = identifier || getClientIdentifier(request)
  const key = `${operationType}:${clientId}`
  
  const now = Date.now()
  const windowStart = now - config.windowMs
  
  // Get or create rate limit entry
  let entry = rateLimitStore.get(key)
  
  if (!entry || now > entry.resetTime) {
    // Create new window
    entry = {
      count: 0,
      resetTime: now + config.windowMs
    }
  }
  
  // Check if limit exceeded
  const allowed = entry.count < config.maxRequests
  
  if (allowed) {
    entry.count++
    rateLimitStore.set(key, entry)
  }
  
  return {
    allowed,
    remaining: Math.max(0, config.maxRequests - entry.count),
    resetTime: entry.resetTime,
    limit: config.maxRequests
  }
}

export function createRateLimitResponse(
  operationType: keyof typeof RATE_LIMITS,
  result: RateLimitResult
): NextResponse {
  const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000)
  
  return NextResponse.json(
    { 
      error: 'Rate limit exceeded',
      type: operationType,
      limit: result.limit,
      remaining: result.remaining,
      retryAfter
    },
    { 
      status: 429,
      headers: {
        'X-RateLimit-Limit': result.limit.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': result.resetTime.toString(),
        'Retry-After': retryAfter.toString()
      }
    }
  )
}

function getClientIdentifier(request: NextRequest): string {
  // Try to get user ID from auth header or session
  const authHeader = request.headers.get('authorization')
  if (authHeader) {
    // Extract user ID from JWT or session
    // This is a simplified version - implement proper JWT parsing
    return `auth:${authHeader.slice(0, 20)}`
  }
  
  // Fallback to IP address
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : 
             request.headers.get('x-real-ip') || 
             'unknown'
  
  return `ip:${ip}`
}

// Middleware helper for adding rate limit headers to successful responses
export function addRateLimitHeaders(
  response: NextResponse,
  result: RateLimitResult
): NextResponse {
  response.headers.set('X-RateLimit-Limit', result.limit.toString())
  response.headers.set('X-RateLimit-Remaining', result.remaining.toString())
  response.headers.set('X-RateLimit-Reset', result.resetTime.toString())
  
  return response
}

// For production scaling to millions of users, consider:
// 1. Redis-based distributed rate limiting
// 2. Different limits per user tier (free vs premium)
// 3. Geographic rate limiting
// 4. API key-based rate limiting
// 5. Sliding window rate limiting
// 6. Rate limiting by endpoint complexity
// 7. Burst allowances for legitimate traffic spikes

export class RedisRateLimit {
  // TODO: Implement Redis-based rate limiting for production
  // This would use Redis INCR with EXPIRE for atomic operations
  // and support distributed rate limiting across multiple servers
  
  static async check(
    redisClient: any,
    key: string,
    limit: number,
    windowMs: number
  ): Promise<RateLimitResult> {
    // Implementation would use Redis pipeline:
    // MULTI
    // INCR key
    // EXPIRE key windowMs
    // EXEC
    throw new Error('Redis rate limiting not implemented yet')
  }
}
