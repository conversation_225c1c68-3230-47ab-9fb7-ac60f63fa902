"use client"

import Link from 'next/link'
import { ReactionSystem } from './ReactionSystem'

export function TrendingRecipeActions({
  recipeId,
  reactions,
  userReaction,
  currentUserId,
}: {
  recipeId: string
  reactions?: Record<string, number>
  userReaction?: string | null
  currentUserId?: string
}) {
  return (
    <div className="mt-2 flex items-center justify-between border-t border-gray-100 pt-3">
      <ReactionSystem
        contentId={recipeId}
        contentType="recipe"
        initialReactions={reactions || {}}
        userReaction={userReaction}
        currentUserId={currentUserId}
      />
      <Link href={`/recipes/${recipeId}#comments`} className="text-sm text-gray-600 hover:text-gray-800">
        View comments →
      </Link>
    </div>
  )
}

