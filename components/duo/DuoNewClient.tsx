"use client"

import { useEffect, useState } from 'react'
import { DuoInvite } from '@/components/duo/DuoInvite'
import { DuoUploader } from '@/components/duo/DuoUploader'
import { useRouter } from 'next/navigation'
import { createSupabaseClient } from '@/lib/supabase/client'

type DuoStatus = 'pending' | 'awaiting_approval' | 'completed' | string

export function DuoNewClient({ duoPostId }: { duoPostId: string }) {
  const supabase = createSupabaseClient()
  const router = useRouter()

  // Step state
  const [step1Done, setStep1Done] = useState(false)        // Part A uploaded
  const [step2Done, setStep2Done] = useState(false)        // Invite sent (or link shared/copied)
  const [selectedFriend, setSelectedFriend] = useState<{ id: string; name: string } | null>(null)

  // Status for Step 3
  const [duoStatus, setDuoStatus] = useState<DuoStatus>('pending')
  const [partBExists, setPartBExists] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Announce step changes for a11y
  const [ariaMsg, setAriaMsg] = useState<string>('Step 1 of 3: Record Part A')

  useEffect(() => {
    setAriaMsg(step2Done ? 'Step 3 of 3: Approve & Publish' : step1Done ? 'Step 2 of 3: Invite a Friend' : 'Step 1 of 3: Record Part A')
  }, [step1Done, step2Done])

  // Initial fetch of status (single read)
  useEffect(() => {
    // No need to block UI; best-effort initial read
    refreshStatus(false).catch(() => {})
    // Refresh once when page regains focus (if not completed)
    const onFocus = () => {
      if (duoStatus !== 'completed') {
        refreshStatus(false).catch(() => {})
      }
    }
    if (typeof window !== 'undefined') {
      window.addEventListener('focus', onFocus)
      document.addEventListener('visibilitychange', onFocus)
    }
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('focus', onFocus)
        document.removeEventListener('visibilitychange', onFocus)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  async function refreshStatus(showSpinner = true) {
    try {
      if (showSpinner) setRefreshing(true)
      // duo_posts status
      const { data: post } = await supabase
        .from('duo_posts' as any)
        .select('id, status')
        .eq('id', duoPostId)
        .maybeSingle()
      if (post?.status) {
        setDuoStatus(post.status as DuoStatus)
      }

      // whether part 2 exists
      const { data: parts } = await supabase
        .from('duo_parts' as any)
        .select('id, part_number')
        .eq('duo_post_id', duoPostId)
        .eq('part_number', 2)

      setPartBExists(!!(parts && parts.length > 0))
    } finally {
      if (showSpinner) setRefreshing(false)
    }
  }

  // Step 1 completion handler from uploader
  const handlePartADone = async () => {
    setStep1Done(true)
    setAriaMsg('Step 2 of 3: Invite a Friend')
    // After commitPart(1), status remains pending; nothing else needed here
  }

  // Step 2 completion handlers from invite component
  const handleInviteSent = (name: string) => {
    setStep2Done(true)
    setAriaMsg(`Invite sent to ${name}. Step 3 of 3: Approve & Publish`)
  }
  const handleShare = () => {
    setStep2Done(true)
    setAriaMsg('Invite link shared. Step 3 of 3: Approve & Publish')
  }
  const handleCopy = () => {
    setStep2Done(true)
    setAriaMsg('Invite link copied. Step 3 of 3: Approve & Publish')
  }

  const handleEditInvite = () => {
    setStep2Done(false)
    setAriaMsg('Step 2 of 3: Invite a Friend')
  }

  // Helper UI fragments
  const StepBadge = ({ n, label, done, current }: { n: number; label: string; done?: boolean; current?: boolean }) => (
    <div className={`flex items-center gap-2 ${current ? 'text-purple-700' : 'text-gray-700'}`}>
      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold ${done ? 'bg-green-100 text-green-700 border border-green-300' : current ? 'bg-purple-100 text-purple-700 border border-purple-300' : 'bg-gray-100 text-gray-700 border border-gray-300'}`}>
        {done ? '✓' : n}
      </div>
      <div className="text-sm font-medium">{label}</div>
      {done && <span className="ml-1 text-xs text-green-600">Completed</span>}
    </div>
  )

  return (
    <div>
      <p aria-live="polite" className="sr-only">{ariaMsg}</p>

      {/* Step 1: Record Part A */}
      <div className="rounded-xl border border-gray-200 bg-white p-4 sm:p-5 mb-4">
        <div className="flex items-center justify-between mb-2">
          <StepBadge n={1} label="Record Part A" done={step1Done} current={!step1Done} />
        </div>
        <p className="text-sm text-gray-600 mb-3">Record your part. Your friend replies with Part B.</p>
        {!step1Done ? (
          <DuoUploader duoPostId={duoPostId} partNumber={1} onDone={handlePartADone} />
        ) : (
          <div className="text-sm text-green-700">Part A ready. You can re-record if needed by uploading again.</div>
        )}
      </div>

      {/* Step 2: Invite a Friend (rendered after Step 1) */}
      <div className={`rounded-xl border p-4 sm:p-5 mb-4 ${step1Done ? 'border-gray-200 bg-white' : 'border-gray-100 bg-gray-50/60'}`}>
        <div className="flex items-center justify-between mb-2">
          <StepBadge n={2} label="Tag & Invite a Friend" done={step2Done} current={step1Done && !step2Done} />
        </div>
        <p className="text-sm text-gray-600 mb-3">Pick who replies. They’ll see your Part A and record Part B.</p>
        {step1Done && !step2Done ? (
          <DuoInvite
            duoPostId={duoPostId}
            onFriendSelected={setSelectedFriend}
            onInviteSent={handleInviteSent}
            onShare={handleShare}
            onCopy={handleCopy}
          />
        ) : step1Done && step2Done ? (
          <div className="rounded-md border border-green-200 bg-green-50 p-3 flex items-start justify-between gap-3">
            <div className="flex items-start gap-2">
              <div className="mt-0.5 inline-flex items-center justify-center w-5 h-5 rounded-full bg-green-500 text-white text-[10px]">✓</div>
              <div className="text-sm text-green-900">
                Invite sent{selectedFriend ? <> to <span className="font-medium">{selectedFriend.name}</span></> : ''}. You’re all set for this step.
              </div>
            </div>
            <button
              onClick={handleEditInvite}
              className="text-xs text-green-800 underline hover:text-green-900"
            >
              Change
            </button>
          </div>
        ) : (
          <div className="text-sm text-gray-500">Finish Step 1 to invite a friend.</div>
        )}
      </div>

      {/* Step 3: Approve & Publish */}
      <div className="rounded-xl border border-gray-200 bg-white p-4 sm:p-5">
        <div className="flex items-center justify-between mb-2">
          <StepBadge n={3} label="Approve & Publish" done={duoStatus === 'completed'} current={step2Done && duoStatus !== 'completed'} />
        </div>
        <p className="text-sm text-gray-600 mb-3">After your friend records Part B, approve to publish.</p>

        {/* Status line */}
        <div className="text-sm mb-3">
          {!step2Done ? (
            <span className="text-gray-500">Invite your friend to proceed.</span>
          ) : duoStatus === 'completed' ? (
            <span className="text-green-700">Published.</span>
          ) : duoStatus === 'awaiting_approval' ? (
            <span className="text-blue-700">Part B received. Review and approve.</span>
          ) : partBExists ? (
            <span className="text-blue-700">Part B uploaded. Awaiting approval status…</span>
          ) : (
            <span className="text-gray-600">Waiting for Part B. Tap Refresh to check.</span>
          )}
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => {
              // Navigate to dashboard and scroll to OnlyDuos section
              router.push('/dashboard')
              // Use setTimeout to ensure page loads before scrolling
              setTimeout(() => {
                const element = document.getElementById('onlyduos')
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth', block: 'start' })
                  // Add a subtle highlight effect
                  element.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.3)'
                  setTimeout(() => {
                    element.style.boxShadow = ''
                  }, 2000)
                }
              }, 100)
            }}
            className="px-3 py-2 rounded-md bg-gray-900 text-white text-sm hover:bg-black"
          >
            Manage this Duo
          </button>
          <button
            onClick={() => refreshStatus(true)}
            disabled={!step2Done || refreshing}
            className="px-3 py-2 rounded-md border border-gray-300 bg-white text-sm disabled:opacity-60"
          >
            {refreshing ? 'Refreshing…' : 'Refresh status'}
          </button>
        </div>

        {selectedFriend && step2Done && (
          <div className="mt-2 text-xs text-gray-600">
            Invite sent to <span className="font-medium">{selectedFriend.name}</span>. We’ll notify you when it’s ready to approve.
          </div>
        )}
      </div>
    </div>
  )
}
