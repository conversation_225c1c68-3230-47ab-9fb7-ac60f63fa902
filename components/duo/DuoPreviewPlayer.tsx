"use client"

import { useState, useRef, useEffect } from 'react'

interface DuoPart {
  id: string
  part_number: number
  media: {
    src_r2_key?: string
    hls_manifest?: string
  }
  author_user_id: string
}

interface DuoPreviewPlayerProps {
  partA: DuoPart
  partB: DuoPart
  initiatorName?: string
  responderName?: string
  onConfirm?: () => void
  onReRecord?: () => void
  showActions?: boolean
}

export function DuoPreviewPlayer({
  partA,
  partB,
  initiatorName = "Initiator",
  responderName = "You",
  onConfirm,
  onReRecord,
  showActions = true
}: DuoPreviewPlayerProps) {
  const [playMode, setPlayMode] = useState<'side-by-side' | 'sequential'>('side-by-side')
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentPart, setCurrentPart] = useState<1 | 2>(1)
  const [generatingPreview, setGeneratingPreview] = useState(false)
  const [enhancedPreviewUrl, setEnhancedPreviewUrl] = useState<string | null>(null)

  const partARef = useRef<HTMLVideoElement>(null)
  const partBRef = useRef<HTMLVideoElement>(null)
  const sequentialRef = useRef<HTMLVideoElement>(null)

  // Generate video URLs
  const partAUrl = partA.media.src_r2_key 
    ? `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partA.media.src_r2_key}`
    : null
  const partBUrl = partB.media.src_r2_key 
    ? `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partB.media.src_r2_key}`
    : null

  const generateEnhancedPreview = async () => {
    if (!partA?.media?.src_r2_key || !partB?.media?.src_r2_key) return

    setGeneratingPreview(true)
    try {
      // Use the same enhanced video processing as download
      const response = await fetch('/api/duo/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duoPostId: partA.duo_post_id || 'preview',
          includeWatermark: true,
          includeAttribution: true
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        setEnhancedPreviewUrl(url)

        // Auto-play the enhanced preview
        if (sequentialRef.current) {
          sequentialRef.current.src = url
          sequentialRef.current.play()
          setIsPlaying(true)
        }
      } else {
        // Fallback to simple sequential play
        playSequential()
      }
    } catch (error) {
      console.error('Failed to generate enhanced preview:', error)
      // Fallback to simple sequential play
      playSequential()
    } finally {
      setGeneratingPreview(false)
    }
  }

  const playSequential = async () => {
    if (!sequentialRef.current || !partAUrl || !partBUrl) return

    setIsPlaying(true)
    setCurrentPart(1)

    // Play Part A first
    sequentialRef.current.src = partAUrl
    await sequentialRef.current.play()

    // When Part A ends, play Part B
    sequentialRef.current.onended = async () => {
      if (sequentialRef.current && partBUrl) {
        setCurrentPart(2)
        sequentialRef.current.src = partBUrl
        await sequentialRef.current.play()

        // When Part B ends, reset
        sequentialRef.current.onended = () => {
          setIsPlaying(false)
          setCurrentPart(1)
        }
      }
    }
  }

  const stopSequential = () => {
    if (sequentialRef.current) {
      sequentialRef.current.pause()
      sequentialRef.current.currentTime = 0
      setIsPlaying(false)
      setCurrentPart(1)
    }
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 space-y-4">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-serif text-gray-900 mb-1">Preview Your OnlyDuo</h3>
        <p className="text-sm text-gray-600">Review both parts before sending for approval</p>
      </div>

      {/* View Mode Toggle */}
      <div className="flex justify-center">
        <div className="bg-gray-100 rounded-lg p-1 flex">
          <button
            onClick={() => setPlayMode('side-by-side')}
            className={`px-3 py-1.5 text-sm rounded-md transition ${
              playMode === 'side-by-side' 
                ? 'bg-white text-gray-900 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Side by Side
          </button>
          <button
            onClick={() => setPlayMode('sequential')}
            className={`px-3 py-1.5 text-sm rounded-md transition ${
              playMode === 'sequential' 
                ? 'bg-white text-gray-900 shadow-sm' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Full Preview
          </button>
        </div>
      </div>

      {/* Video Display */}
      {playMode === 'side-by-side' ? (
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-start">
          {/* Part A */}
          <div className="flex-1 max-w-xs">
            <div className="space-y-2">
              {partAUrl ? (
                <video
                  ref={partARef}
                  className="w-full aspect-[9/16] bg-black rounded-lg shadow-sm"
                  src={partAUrl}
                  controls
                  preload="metadata"
                  muted
                />
              ) : (
                <div className="w-full aspect-[9/16] bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 text-sm">Part A unavailable</span>
                </div>
              )}
              <div className="text-center">
                <div className="text-sm font-medium text-gray-900">Part A</div>
                <div className="text-xs text-gray-500">{initiatorName}</div>
              </div>
            </div>
          </div>

          {/* Part B */}
          <div className="flex-1 max-w-xs">
            <div className="space-y-2">
              {partBUrl ? (
                <video
                  ref={partBRef}
                  className="w-full aspect-[9/16] bg-black rounded-lg shadow-sm"
                  src={partBUrl}
                  controls
                  preload="metadata"
                  muted
                />
              ) : (
                <div className="w-full aspect-[9/16] bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 text-sm">Part B unavailable</span>
                </div>
              )}
              <div className="text-center">
                <div className="text-sm font-medium text-gray-900">Part B</div>
                <div className="text-xs text-gray-500">{responderName}</div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center space-y-4">
          {/* Sequential Player */}
          <div className="w-full max-w-xs">
            <video
              ref={sequentialRef}
              className="w-full aspect-[9/16] bg-black rounded-lg shadow-sm"
              controls={false}
              preload="metadata"
              muted
            />
            <div className="text-center mt-2">
              <div className="text-sm font-medium text-gray-900">
                {isPlaying ? (
                  currentPart === 1 ? `Part A - ${initiatorName}` : `Part B - ${responderName}`
                ) : (
                  'Ready to play full sequence'
                )}
              </div>
            </div>
          </div>

          {/* Sequential Controls */}
          <div className="flex gap-3">
            {!isPlaying && !generatingPreview ? (
              <>
                <button
                  onClick={generateEnhancedPreview}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition font-medium"
                >
                  🎬 Play Full Duo (Enhanced)
                </button>
                <button
                  onClick={playSequential}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                >
                  ▶️ Simple Preview
                </button>
              </>
            ) : generatingPreview ? (
              <button
                disabled
                className="px-4 py-2 bg-purple-400 text-white rounded-lg cursor-not-allowed"
              >
                🎬 Generating Enhanced Preview...
              </button>
            ) : (
              <button
                onClick={stopSequential}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition"
              >
                ⏹️ Stop
              </button>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {showActions && (
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
          <button
            onClick={onReRecord}
            className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition"
          >
            🎬 Re-record Part B
          </button>
          <button
            onClick={onConfirm}
            className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition font-medium"
          >
            ✅ Send for Approval
          </button>
        </div>
      )}
    </div>
  )
}
