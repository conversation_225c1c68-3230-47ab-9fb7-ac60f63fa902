"use client"

import Image from 'next/image'
import { DuoVideoPlayer } from './DuoVideoPlayer'

export type DuoPart = {
  part_number: 1 | 2
  author_user_id: string
  media: {
    type: 'video'
    src_r2_key: string
    hls_manifest?: string | null
  }
}

export function DuoCard({
  title,
  userA,
  userB,
  partA,
  partB,
  isInitiator,
  duoPostId,
  onApprove,
  signedPlayback = false,
}: {
  title: string
  userA: { id: string; name: string; profile_picture_url?: string | null }
  userB?: { id: string; name: string; profile_picture_url?: string | null }
  partA: DuoPart
  partB?: DuoPart
  isInitiator: boolean
  duoPostId: string
  onApprove?: () => void
  signedPlayback?: boolean
}) {
  return (
    <div className="px-4 pb-4">
      <h3 className="font-serif text-lg bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2 line-clamp-2">
        {title}
      </h3>
      <div className="flex items-center gap-2 mb-2">
        <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200">
          {userA.profile_picture_url ? (
            <Image src={userA.profile_picture_url} alt={userA.name} width={32} height={32} />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-500 text-sm">
              {userA.name.charAt(0)}
            </div>
          )}
        </div>
        <span className="text-gray-600">→</span>
        <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200">
          {userB?.profile_picture_url ? (
            <Image src={userB.profile_picture_url} alt={userB.name} width={32} height={32} />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-500 text-sm">
              {userB ? userB.name.charAt(0) : '?'}
            </div>
          )}
        </div>
        <span className="text-xs bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full">Duo</span>
      </div>

      <DuoVideoPlayer
        partA={{ type: 'video', src_r2_key: partA.media.src_r2_key, hls_manifest: partA.media.hls_manifest }}
        partB={partB ? { type: 'video', src_r2_key: partB.media.src_r2_key, hls_manifest: partB.media.hls_manifest } : undefined}
        signedPlayback={signedPlayback}
      />

      {isInitiator && partB && (
        <div className="mt-3">
          <button
            onClick={onApprove}
            className="px-3 py-2 text-sm rounded bg-blue-600 text-white hover:bg-blue-700"
          >Approve response</button>
        </div>
      )}
    </div>
  )
}

