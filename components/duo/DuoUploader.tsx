"use client"

import { useState } from 'react'

/**
 * Returns media duration in seconds by loading the file into a hidden media element.
 * For non-media files or if duration can't be read, resolves to null.
 */
async function getMediaDuration(file: File): Promise<number | null> {
  try {
    const isVideo = file.type?.startsWith('video')
    const isAudio = file.type?.startsWith('audio')
    if (!isVideo && !isAudio) return null

    const url = URL.createObjectURL(file)
    const el = isVideo ? document.createElement('video') : document.createElement('audio')
    el.preload = 'metadata'
    el.src = url

    const duration = await new Promise<number>((resolve, reject) => {
      const cleanup = () => URL.revokeObjectURL(url)
      el.onloadedmetadata = () => {
        const d = el.duration
        cleanup()
        resolve(isFinite(d) ? d : 0)
      }
      el.onerror = () => {
        cleanup()
        resolve(null as any) // treat as unknown duration; don't block upload
      }
    })
    return duration ?? null
  } catch {
    return null
  }
}

export function DuoUploader({
  duoPostId,
  partNumber,
  onDone,
}: {
  duoPostId: string
  partNumber: 1 | 2
  onDone?: () => void
}) {
  const [busy, setBusy] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleFile = async (file: File) => {
    setBusy(true); setError(null)

    // Helper: server-side direct upload (fallback when presigned PUT fails or CORS blocked)
    const directUpload = async (file: File): Promise<string> => {
      const form = new FormData()
      form.append('file', file)
      form.append('duoPostId', duoPostId)
      form.append('partNumber', String(partNumber))
      const res = await fetch('/api/duo/direct-upload', { method: 'POST', body: form })
      if (!res.ok) throw new Error('Direct upload failed')
      const json = await res.json()
      return json.r2Key as string
    }

    try {
      // Constraints: size <= 120MB, duration <= 60s (best-effort)
      if (file.size > 120 * 1024 * 1024) throw new Error('Max file size is 120MB')

      const dur = await getMediaDuration(file)
      if (dur && dur > 60.5) throw new Error('Max duration is 60 seconds')

      // Try presigned upload path first
      let finalR2Key: string | null = null
      try {
        const presignRes = await fetch('/api/duo/presign-upload', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            duoPostId,
            partNumber,
            filename: (file as any).name || 'upload.mp4',
            sizeBytes: file.size,
            mimeType: file.type || 'video/mp4'
          })
        })
        if (!presignRes.ok) throw new Error('Failed to prepare upload')
        const { uploadUrl, r2Key } = await presignRes.json()

        // PUT to R2 (may fail due to CORS/network)
        const putRes = await fetch(uploadUrl, {
          method: 'PUT',
          body: file,
          headers: { 'Content-Type': file.type || 'video/mp4' }
        })
        if (!putRes.ok) throw new Error('Upload failed')
        finalR2Key = r2Key
      } catch (e) {
        // Fallback to server-side direct upload
        finalR2Key = await directUpload(file)
      }

      // Minimal media metadata (duration/size can be probed client-side later)
      const media = {
        type: 'video',
        src_r2_key: finalR2Key,
        hls_manifest: null,
      }

      const commitRes = await fetch('/api/duo/commitPart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId, partNumber, media })
      })
      if (!commitRes.ok) throw new Error('Failed to save part')
      onDone?.()
    } catch (e: any) {
      setError(e.message || 'Upload failed')
    } finally {
      setBusy(false)
    }
  }

  return (
    <div className="mt-3">
      <label className="inline-flex items-center gap-2 text-sm text-blue-700 hover:text-blue-800 cursor-pointer">
        <span className="px-3 py-2 bg-blue-50 rounded border border-blue-200">{busy ? 'Uploading…' : `Upload Part ${partNumber} (≤60s, ≤120MB)`}</span>
        <input type="file" accept="video/mp4, audio/mp4, audio/aac, audio/m4a" className="hidden" disabled={busy} onChange={e => {
          const f = e.target.files?.[0]; if (f) handleFile(f)
        }} />
      </label>
      {error && <div className="text-xs text-red-600 mt-1">{error}</div>}
    </div>
  )
}
