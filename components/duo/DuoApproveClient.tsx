"use client"

import { useState } from 'react'

export function DuoApproveClient({
  duoPostId,
  isInitiator,
  hasPartB,
  status,
}: {
  duoPostId: string
  isInitiator: boolean
  hasPartB: boolean
  status: string
}) {
  const [approving, setApproving] = useState(false)
  const [approved, setApproved] = useState(status === 'completed')
  const [error, setError] = useState<string | null>(null)

  const canApprove = isInitiator && hasPartB && !approved

  const onApprove = async () => {
    setApproving(true); setError(null)
    try {
      const res = await fetch('/api/duo/approve', {
        method: 'POST', headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId })
      })
      if (!res.ok) {
        const j = await res.json().catch(() => ({} as any))
        throw new Error(j?.error || 'Failed to approve')
      }
      setApproved(true)
    } catch (e: any) {
      setError(e.message || 'Failed to approve')
    } finally {
      setApproving(false)
    }
  }

  return (
    <div className="mt-4">
      {approved ? (
        <div className="rounded-md bg-green-50 border border-green-200 p-3 text-green-800 text-sm">
          Approved. This Duo will now appear on your timeline.
        </div>
      ) : canApprove ? (
        <button
          onClick={onApprove}
          disabled={approving}
          className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-60"
        >
          {approving ? 'Approving…' : 'Approve response'}
        </button>
      ) : (
        <div className="text-sm text-gray-600">
          {isInitiator ? (
            hasPartB ? 'Ready for your approval.' : 'Waiting for the responder to upload Part B.'
          ) : (
            status === 'awaiting_approval' ? 'Waiting for initiator approval.' : 'Only the initiator can approve.'
          )}
        </div>
      )}
      {error && <div className="text-sm text-red-600 mt-2">{error}</div>}
    </div>
  )
}

