"use client"

import { useEffect, useRef, useState } from 'react'

type MediaVideo = {
  type: 'video'
  src_r2_key: string
  hls_manifest?: string | null
}

export function DashboardDuoPlayer({
  partA,
  partB,
  duoId,
  isInitiator,
  onDelete,
  onRequestRedo,
  signedPlayback = false,
  initiatorName = 'You',
  responderName = 'Friend',
  initiatorPhoto,
  responderPhoto,
  compact = false
}: {
  partA: MediaVideo
  partB?: MediaVideo
  duoId: string
  isInitiator: boolean
  onDelete?: () => void
  onRequestRedo?: () => void
  signedPlayback?: boolean
  initiatorName?: string
  responderName?: string
  initiatorPhoto?: string
  responderPhoto?: string
  compact?: boolean
}) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [muted, setMuted] = useState(false)
  const [playingB, setPlayingB] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showFullscreen, setShowFullscreen] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [showCountdown, setShowCountdown] = useState(false)
  const [currentPart, setCurrentPart] = useState<1 | 2>(1)
  const [partADuration, setPartADuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)

  // Public fallback URLs
  const publicA = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partA.src_r2_key}`
  const publicB = partB ? `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partB.src_r2_key}` : undefined

  // Signed URLs state
  const [signedA, setSignedA] = useState<string | null>(null)
  const [signedB, setSignedB] = useState<string | null>(null)

  // Fetch signed GET URLs (only when enabled)
  useEffect(() => {
    if (!signedPlayback) return
    let cancelled = false
    async function sign() {
      try {
        const resA = await fetch(`/api/media/sign?key=${encodeURIComponent(partA.src_r2_key)}`)
        if (resA.ok) {
          const j = await resA.json()
          if (!cancelled) setSignedA(j.url)
        }
        if (partB) {
          const resB = await fetch(`/api/media/sign?key=${encodeURIComponent(partB.src_r2_key)}`)
          if (resB.ok) {
            const j2 = await resB.json()
            if (!cancelled) setSignedB(j2.url)
          }
        }
      } catch {
        // ignore; fallback to public URLs
      }
    }
    sign()
    return () => { cancelled = true }
  }, [signedPlayback, partA.src_r2_key, partB?.src_r2_key])

  useEffect(() => {
    const el = videoRef.current
    if (!el) return

    const onEnded = () => {
      if (partB && !playingB) {
        // Seamlessly switch to Part B
        setPlayingB(true)
        setCurrentPart(2)
        const nextSrc = signedB || publicB
        if (nextSrc) {
          el.src = nextSrc
          el.currentTime = 0
          el.play().catch(() => {})
          setIsPlaying(true)
        }
      } else {
        // End of Part B - reset for next play
        setPlayingB(false)
        setCurrentPart(1)
        setIsPlaying(false)
      }
    }

    const onTimeUpdate = () => {
      setCurrentTime(el.currentTime)
      // Show countdown during last 3 seconds of Part A
      if (!playingB && partB && partADuration > 0) {
        const timeLeft = partADuration - el.currentTime
        if (timeLeft <= 3 && timeLeft > 0) {
          setShowCountdown(true)
          setCountdown(Math.ceil(timeLeft))
        } else {
          setShowCountdown(false)
        }
      }
    }

    const onLoadedMetadata = () => {
      if (!playingB) {
        setPartADuration(el.duration)
      }
    }

    const onPlay = () => setIsPlaying(true)
    const onPause = () => setIsPlaying(false)

    el.addEventListener('ended', onEnded)
    el.addEventListener('play', onPlay)
    el.addEventListener('pause', onPause)
    el.addEventListener('timeupdate', onTimeUpdate)
    el.addEventListener('loadedmetadata', onLoadedMetadata)
    return () => {
      el.removeEventListener('ended', onEnded)
      el.removeEventListener('play', onPlay)
      el.removeEventListener('pause', onPause)
      el.removeEventListener('timeupdate', onTimeUpdate)
      el.removeEventListener('loadedmetadata', onLoadedMetadata)
    }
  }, [partB, playingB, signedB, publicB])

  const initialSrc = signedA || publicA

  const togglePlay = () => {
    const el = videoRef.current
    if (!el) return

    if (isPlaying) {
      el.pause()
    } else {
      // Always reset to Part A when starting play (whether first time or replay)
      if (playingB || el.ended) {
        setPlayingB(false)
        setCurrentPart(1)
        el.src = signedA || publicA
        el.currentTime = 0
      }
      el.play().catch(() => {})
    }
  }

  const handleDelete = async () => {
    try {
      const res = await fetch('/api/duo/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId: duoId })
      })
      if (!res.ok) throw new Error('Failed to delete')
      onDelete?.()
    } catch (e) {
      alert('Failed to delete OnlyDuo')
    }
    setShowDeleteConfirm(false)
  }



  return (
    <>
      <div
        className={`relative ${compact ? 'w-32 cursor-pointer' : 'w-full'}`}
        style={{ aspectRatio: '9/16' }}
        onClick={compact ? () => setShowFullscreen(true) : undefined}
      >
        <video
          ref={videoRef}
          className="w-full h-full bg-black rounded-lg object-contain"
          src={initialSrc}
          playsInline
          muted={muted}
          preload="metadata"
          onClick={compact ? undefined : togglePlay}
        />
        
        {/* Custom Play Button Overlay */}
        {!isPlaying && !compact && (
          <button
            onClick={togglePlay}
            className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg"
          >
            <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center shadow-lg">
              <div className="w-0 h-0 border-l-[12px] border-l-black border-y-[8px] border-y-transparent ml-1"></div>
            </div>
          </button>
        )}

        {/* Compact Mode Click Indicator */}
        {compact && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg">
            <div className="w-6 h-6 bg-white/90 rounded-full flex items-center justify-center shadow-lg">
              <div className="w-0 h-0 border-l-[6px] border-l-black border-y-[4px] border-y-transparent ml-0.5"></div>
            </div>
          </div>
        )}

        {/* Dashboard Controls - Better positioned */}
        <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between">
          <div className="flex gap-1">
            <button
              className="bg-black/70 text-white text-xs rounded px-2 py-1 hover:bg-black/80 transition-colors"
              onClick={() => setMuted(m => !m)}
            >
              {muted ? '🔇' : '🔊'}
            </button>
            
            <button
              className="bg-black/70 text-white text-xs rounded px-2 py-1 hover:bg-black/80 transition-colors"
              onClick={() => setShowFullscreen(true)}
            >
              ⛶
            </button>
          </div>


        </div>

        {/* Official OnlyDuo Watermark */}
        <div className="absolute -top-8 -left-2">
          <img
            src="/oduo.png"
            alt="OnlyDuo by OnlyDiary"
            className="h-32 w-auto opacity-90 drop-shadow-lg"
          />
        </div>

        {/* Part Indicators */}
        {partB && !playingB && (
          <div className="absolute top-2 right-2 text-xs bg-black/70 text-white px-2 py-1 rounded">
            Part 1/2
          </div>
        )}
        {partB && playingB && (
          <div className="absolute top-2 right-2 text-xs bg-black/70 text-white px-2 py-1 rounded">
            Part 2/2
          </div>
        )}

        {/* Seamless Countdown - shows during last 3 seconds of Part A */}
        {showCountdown && !playingB && (
          <div className="absolute top-16 right-4">
            <div className="bg-black/70 text-white px-2 py-1 rounded text-sm font-medium">
              Next: {countdown}s
            </div>
          </div>
        )}

        {/* Attribution Watermark - Bottom Center */}
        {isPlaying && (
          <div className="absolute bottom-16 left-2 right-2 flex justify-center">
            <div className="flex items-center gap-2 bg-black/70 text-white px-3 py-2 rounded-lg shadow-lg backdrop-blur-sm">
              {/* Profile Photo */}
              <div className="w-6 h-6 rounded-full overflow-hidden bg-gray-300 flex-shrink-0">
                {currentPart === 1 ? (
                  initiatorPhoto ? (
                    <img src={initiatorPhoto} alt={initiatorName} className="w-full h-full object-cover" />
                  ) : (
                    <div className="w-full h-full bg-purple-500 flex items-center justify-center text-white text-xs font-bold">
                      {initiatorName.charAt(0).toUpperCase()}
                    </div>
                  )
                ) : (
                  responderPhoto ? (
                    <img src={responderPhoto} alt={responderName} className="w-full h-full object-cover" />
                  ) : (
                    <div className="w-full h-full bg-blue-500 flex items-center justify-center text-white text-xs font-bold">
                      {responderName.charAt(0).toUpperCase()}
                    </div>
                  )
                )}
              </div>
              {/* Dynamic Creator Attribution */}
              <span className="text-sm font-medium">
                Video {currentPart} created by {currentPart === 1 ? initiatorName : responderName}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Fullscreen Modal */}
      {showFullscreen && (
        <div className="fixed inset-0 bg-black z-50">
          <div className="flex items-center justify-center h-full px-4">
            <div className="relative w-full max-w-sm mx-auto" style={{ aspectRatio: '9/16', height: '90vh' }}>
              {/* Close button positioned within video area */}
              <button
                onClick={() => setShowFullscreen(false)}
                className="absolute top-4 right-4 z-10 bg-black/70 text-white rounded-full w-10 h-10 flex items-center justify-center shadow-lg hover:bg-black/90 transition-colors"
              >
                <span className="text-lg font-bold">✕</span>
              </button>

              <video
                className="w-full h-full bg-black rounded-lg object-contain"
                src={initialSrc}
                playsInline
                muted={muted}
                controls
                autoPlay
              />
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete OnlyDuo?</h3>
            <p className="text-gray-600 text-sm mb-4">
              This will permanently delete this OnlyDuo. This action cannot be undone.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="flex-1 px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
