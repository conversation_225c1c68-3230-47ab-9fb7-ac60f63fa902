"use client"

import { useState } from 'react'

export function CopyLinkButton({ url, className, label = 'Copy Link' }: { url: string; className?: string; label?: string }) {
  const [copied, setCopied] = useState(false)

  const onCopy = async () => {
    try {
      const absolute = url.startsWith('http') ? url : `${window.location.origin}${url}`
      await navigator.clipboard.writeText(absolute)
      setCopied(true)
      setTimeout(() => setCopied(false), 1500)
    } catch {}
  }

  return (
    <button onClick={onCopy} className={className} aria-live="polite">
      {copied ? 'Copied!' : label}
    </button>
  )
}

