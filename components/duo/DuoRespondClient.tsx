"use client"

import { useState, useEffect } from 'react'
import { DuoUploader } from './DuoUploader'
import { DuoVideoPlayer } from './DuoVideoPlayer'
import { DuoPreviewPlayer } from './DuoPreviewPlayer'
import { createSupabaseClient } from '@/lib/supabase/client'
import Image from 'next/image'

export function DuoRespondClient({
  duoPostId,
  initiatorName,
  initiatorProfilePicture,
  canUpload,
  status,
  partA,
  title,
  body,
}: {
  duoPostId: string
  initiatorName?: string
  initiatorProfilePicture?: string
  canUpload: boolean
  status: 'pending' | 'awaiting_approval' | 'completed' | 'expired' | string
  partA?: any
  title?: string
  body?: string
}) {
  const supabase = createSupabaseClient()
  const [done, setDone] = useState(false)
  const [step, setStep] = useState<'watch' | 'record' | 'preview' | 'complete'>('watch')
  const [partB, setPartB] = useState<any>(null)
  const [uploading, setUploading] = useState(false)

  // Persist state to prevent loss on page refresh
  useEffect(() => {
    const savedStep = localStorage.getItem(`duo-respond-step-${duoPostId}`)
    const savedPartB = localStorage.getItem(`duo-respond-partB-${duoPostId}`)

    if (savedStep && ['watch', 'record', 'preview'].includes(savedStep)) {
      setStep(savedStep as any)
    }

    if (savedPartB) {
      try {
        const parsedPartB = JSON.parse(savedPartB)
        setPartB(parsedPartB)
        setDone(true)
      } catch (e) {
        // Invalid saved data, ignore
      }
    }
  }, [duoPostId])

  // Save state on changes
  useEffect(() => {
    if (step !== 'complete') {
      localStorage.setItem(`duo-respond-step-${duoPostId}`, step)
    } else {
      // Clear saved state when complete
      localStorage.removeItem(`duo-respond-step-${duoPostId}`)
      localStorage.removeItem(`duo-respond-partB-${duoPostId}`)
    }
  }, [step, duoPostId])

  useEffect(() => {
    if (partB) {
      localStorage.setItem(`duo-respond-partB-${duoPostId}`, JSON.stringify(partB))
    }
  }, [partB, duoPostId])

  if (status === 'completed') {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">✅</span>
          </div>
          <h1 className="text-2xl font-serif font-semibold mb-2">Duo Complete!</h1>
          <p className="text-gray-600">This OnlyDuo has been published and is live on the timeline.</p>
        </div>
      </div>
    )
  }

  if (status === 'awaiting_approval') {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">⏳</span>
          </div>
          <h1 className="text-2xl font-serif font-semibold mb-2">Awaiting Approval</h1>
          <p className="text-gray-600 mb-4">{initiatorName} is reviewing your response. You'll see it on the timeline once approved!</p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-800 text-sm">💡 <strong>What happens next?</strong> {initiatorName} will review your Part B and approve it to publish the complete OnlyDuo to both of your timelines.</p>
          </div>
        </div>
      </div>
    )
  }

  if (!canUpload) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">🚫</span>
          </div>
          <h1 className="text-2xl font-serif font-semibold mb-2">Not Authorized</h1>
          <p className="text-gray-600">This OnlyDuo was created for a specific person. Only the invited user can respond.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-serif text-gray-900 mb-2">You've Been Invited!</h1>
        <p className="text-gray-600 font-serif">Complete this OnlyDuo by adding your Part B</p>
      </div>

      {/* Initiator Info */}
      {initiatorName && (
        <div className="flex items-center justify-center gap-3 mb-6">
          <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200">
            {initiatorProfilePicture ? (
              <Image
                src={initiatorProfilePicture}
                alt={initiatorName}
                width={48}
                height={48}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center text-white font-semibold">
                {initiatorName.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          <div>
            <p className="font-medium text-gray-900">{initiatorName}</p>
            <p className="text-sm text-gray-500">started this OnlyDuo</p>
          </div>
        </div>
      )}

      {/* Title & Body */}
      {title && (
        <div className="text-center mb-6">
          <h2 className="text-xl font-serif text-gray-800 mb-2">{title}</h2>
          {body && <p className="text-gray-600">{body}</p>}
        </div>
      )}

      {/* Step Indicator */}
      <div className="flex items-center justify-center gap-2 sm:gap-4 mb-8 overflow-x-auto">
        {/* Step 1: Watch */}
        <div className={`flex items-center gap-2 ${step === 'watch' ? 'text-blue-600' : ['record', 'preview', 'complete'].includes(step) ? 'text-green-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === 'watch' ? 'bg-blue-100 text-blue-600' : ['record', 'preview', 'complete'].includes(step) ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
            {['record', 'preview', 'complete'].includes(step) ? '✓' : '1'}
          </div>
          <span className="text-xs sm:text-sm font-medium whitespace-nowrap">Watch Part A</span>
        </div>
        <div className="w-4 sm:w-8 h-px bg-gray-300"></div>

        {/* Step 2: Record */}
        <div className={`flex items-center gap-2 ${step === 'record' ? 'text-blue-600' : ['preview', 'complete'].includes(step) ? 'text-green-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === 'record' ? 'bg-blue-100 text-blue-600' : ['preview', 'complete'].includes(step) ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
            {['preview', 'complete'].includes(step) ? '✓' : '2'}
          </div>
          <span className="text-xs sm:text-sm font-medium whitespace-nowrap">Record Part B</span>
        </div>
        <div className="w-4 sm:w-8 h-px bg-gray-300"></div>

        {/* Step 3: Preview */}
        <div className={`flex items-center gap-2 ${step === 'preview' ? 'text-blue-600' : step === 'complete' ? 'text-green-600' : 'text-gray-400'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === 'preview' ? 'bg-blue-100 text-blue-600' : step === 'complete' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
            {step === 'complete' ? '✓' : '3'}
          </div>
          <span className="text-xs sm:text-sm font-medium whitespace-nowrap">Preview & Send</span>
        </div>
      </div>

      {/* Content based on step */}
      {step === 'watch' && partA && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Part A</h3>
            <p className="text-gray-600 text-sm mb-4">Watch {initiatorName}'s video to see what you're responding to</p>
          </div>

          <div className="flex justify-center">
            <div className="w-full max-w-sm">
              <DuoVideoPlayer
                partA={{
                  type: 'video',
                  src_r2_key: partA.src_r2_key,
                  hls_manifest: partA.hls_manifest
                }}
                signedPlayback={false}
              />
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={() => setStep('record')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Ready to Record Part B →
            </button>
          </div>
        </div>
      )}

      {step === 'record' && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Record Your Response</h3>
            <p className="text-gray-600 text-sm mb-4">Upload your Part B video (≤60s, ≤120MB)</p>
          </div>

          {!done ? (
            <div className="space-y-4">
              <DuoUploader
                duoPostId={duoPostId}
                partNumber={2}
                onDone={async () => {
                  setUploading(true)
                  // Fetch the uploaded Part B data
                  try {
                    const { data: parts } = await supabase
                      .from('duo_parts')
                      .select('*')
                      .eq('duo_post_id', duoPostId)
                      .eq('part_number', 2)
                      .single()

                    if (parts) {
                      setPartB(parts)
                      setDone(true)
                      setStep('preview')
                    }
                  } catch (error) {
                    console.error('Error fetching Part B:', error)
                  } finally {
                    setUploading(false)
                  }
                }}
              />
              <div className="text-center">
                <button
                  onClick={() => setStep('watch')}
                  className="text-blue-600 text-sm hover:text-blue-700"
                >
                  ← Back to watch Part A
                </button>
              </div>
            </div>
          ) : null}
        </div>
      )}

      {step === 'preview' && partA && partB && (
        <div className="space-y-6">
          <DuoPreviewPlayer
            partA={{
              id: 'part-a',
              part_number: 1,
              media: {
                src_r2_key: partA.src_r2_key,
                hls_manifest: partA.hls_manifest
              },
              author_user_id: partA.author_user_id || ''
            }}
            partB={{
              id: partB.id,
              part_number: 2,
              media: partB.media,
              author_user_id: partB.author_user_id
            }}
            initiatorName={initiatorName}
            responderName="You"
            onReRecord={() => {
              setDone(false)
              setPartB(null)
              setStep('record')
            }}
            onConfirm={async () => {
              // Add confirmation dialog to prevent accidental submissions
              const confirmed = confirm(
                '🎬 Ready to submit your Part B?\n\n' +
                'This will send your video to ' + (initiatorName || 'the initiator') + ' for approval.\n\n' +
                'Once submitted, you can\'t edit it (but they can request a redo if needed).\n\n' +
                'Submit now?'
              )

              if (!confirmed) return

              // Submit the response for approval
              try {
                const response = await fetch('/api/duo/submit-response', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ duoPostId })
                })

                if (response.ok) {
                  setStep('complete')
                } else {
                  alert('Failed to submit response. Please try again.')
                }
              } catch (error) {
                console.error('Error submitting response:', error)
                alert('Failed to submit response. Please try again.')
              }
            }}
          />
        </div>
      )}

      {step === 'complete' && (
        <div className="text-center space-y-6">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <span className="text-3xl">🎉</span>
          </div>
          <div>
            <h3 className="text-2xl font-serif font-semibold text-gray-900 mb-2">Part B Uploaded!</h3>
            <p className="text-gray-600 mb-4">Your response has been sent to {initiatorName} for approval.</p>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h4 className="font-medium text-blue-900 mb-2">What happens next?</h4>
            <div className="text-blue-800 text-sm space-y-2">
              <p>• {initiatorName} will review your Part B</p>
              <p>• Once approved, the complete OnlyDuo will appear on both timelines</p>
              <p>• You'll get a notification when it's live!</p>
            </div>
          </div>

          <div className="space-y-3">
            <button
              onClick={() => window.location.href = '/timeline'}
              className="w-full px-6 py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors"
            >
              Go to Timeline
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="w-full px-6 py-3 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

