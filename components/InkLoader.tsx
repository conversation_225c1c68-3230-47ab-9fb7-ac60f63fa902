"use client"

interface InkLoaderProps {
  lines?: number
  delay?: number
  className?: string
}

export default function InkLoader({ lines = 3, delay = 0, className = "" }: InkLoaderProps) {
  return (
    <span className={`inline-block px-1 ${className}`}>
      {Array.from({ length: lines }).map((_, i) => (
        <span
          key={i}
          className="block h-[2px] w-full bg-gray-900 mb-[2px] last:mb-0 animate-pulse"
          style={{ animationDelay: `${(delay + i * 0.12) * 1000}ms` }}
        />
      ))}
    </span>
  )
}
