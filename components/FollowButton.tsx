"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { createSupabaseClient } from '@/lib/supabase/client'

interface FollowButtonProps {
  writerId: string
  writerName?: string
  initialIsFollowing?: boolean
  size?: 'sm' | 'md' | 'lg'
  onFollowChange?: (isFollowing: boolean) => void
}

export function FollowButton({ writerId, writerName, initialIsFollowing = false, size = 'md', onFollowChange }: FollowButtonProps) {
  const [loading, setLoading] = useState(false)
  const [isFollowing, setIsFollowing] = useState(initialIsFollowing)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    const supabase = createSupabaseClient()
    supabase.auth.getUser().then(({ data }) => {
      setCurrentUserId(data.user?.id ?? null)
    }).catch(() => setCurrentUserId(null))
  }, [])

  const handleFollowToggle = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()
    setLoading(true)
    try {
      const response = await fetch('/api/follow-dev', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          writerId, 
          action: isFollowing ? 'unfollow' : 'follow' 
        }),
      })
      const data = await response.json()
      
      if (data.success) {
        const newFollowingState = data.isFollowing ?? !isFollowing
        setIsFollowing(newFollowingState)

        // Call the callback to update parent component
        onFollowChange?.(newFollowingState)

        // Show success message without blocking UI
        console.log(data.message || `Successfully ${isFollowing ? 'unfollowed' : 'followed'} ${writerName || 'writer'}!`)
      } else {
        console.error(data.error || `Failed to ${isFollowing ? 'unfollow' : 'follow'}`)
      }
    } catch (error) {
      console.error('Follow error:', error)
    } finally {
      setLoading(false)
    }
  }

  const sizeClasses = {
    // Smaller on mobile; scale up slightly on larger screens
    sm: 'px-2 py-0.5 text-[11px] rounded-md sm:px-3 sm:py-1 sm:text-xs',
    md: 'px-3 py-1 text-xs rounded-lg sm:px-6 sm:py-2 sm:text-sm',
    lg: 'px-4 py-2 text-sm rounded-lg sm:px-8 sm:py-3 sm:text-base'
  }

  // Never show follow button for self
  if (currentUserId && currentUserId === writerId) return null

  // If already following, render nothing (timeline behavior). Keep unfollow available elsewhere.
  if (isFollowing) return null

  return (
    <button
      onClick={(e) => handleFollowToggle(e)}
      disabled={loading}
      className={`font-medium transition-colors disabled:opacity-50 ${sizeClasses[size]} bg-blue-600 text-white hover:bg-blue-700`}
    >
      {loading ? 'Following…' : 'Follow'}
    </button>
  )
}
