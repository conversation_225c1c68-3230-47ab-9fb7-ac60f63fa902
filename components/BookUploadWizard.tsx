"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { createSupabaseClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Upload, FileText, BookOpen, CheckCircle, AlertCircle, X } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface ProcessingStatus {
  status: 'uploading' | 'processing' | 'indexing' | 'optimizing' | 'completed' | 'failed'
  progress: number
  currentStep: string
  totalChapters?: number
  processedChapters?: number
  error?: string
}

interface BookUploadWizardProps {
  onComplete: (bookId: string) => void
  onClose: () => void
}

export function BookUploadWizard({ onComplete, onClose }: BookUploadWizardProps) {
  const [step, setStep] = useState<'upload' | 'metadata' | 'processing'>('upload')
  const [file, setFile] = useState<File | null>(null)
  const [bookData, setBookData] = useState({
    title: '',
    description: '',
    price: 0,
    tags: [] as string[],
    coverImage: null as File | null
  })
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({
    status: 'uploading',
    progress: 0,
    currentStep: 'Preparing upload...'
  })
  const [bookId, setBookId] = useState<string>('')
  
  const supabase = createSupabaseClient()

  // Enhanced file validation for e-reader compatibility
  const validateFile = (file: File): string | null => {
    const maxSize = 50 * 1024 * 1024 // 50MB
    const allowedTypes = [
      'application/epub+zip',
      'application/epub',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'application/rtf', // .rtf
      'text/rtf' // alternative RTF MIME type
    ]

    if (file.size > maxSize) {
      return 'File size must be less than 50MB'
    }

    if (!allowedTypes.includes(file.type)) {
      return 'Only EPUB, DOCX, DOC, and RTF files are supported'
    }

    return null
  }

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return
    
    const error = validateFile(file)
    if (error) {
      alert(error)
      return
    }
    
    setFile(file)
    
    // Auto-extract title from filename
    const filename = file.name.replace(/\.[^/.]+$/, "")
    setBookData(prev => ({
      ...prev,
      title: prev.title || filename.replace(/[-_]/g, ' ')
    }))
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/epub+zip': ['.epub'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/msword': ['.doc'],
      'application/rtf': ['.rtf'],
      'text/rtf': ['.rtf']
    },
    maxFiles: 1
  })

  const uploadAndProcess = async () => {
    if (!file) return
    
    setStep('processing')
    
    try {
      // Step 1: Upload file to Supabase Storage
      setProcessingStatus({
        status: 'uploading',
        progress: 10,
        currentStep: 'Uploading file...'
      })
      
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExt}`
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('book-uploads')
        .upload(fileName, file)
      
      if (uploadError) throw uploadError
      
      // Step 2: Create book record
      setProcessingStatus({
        status: 'processing',
        progress: 20,
        currentStep: 'Creating book record...'
      })
      
      const { data: bookRecord, error: bookError } = await supabase
        .from('projects')
        .insert({
          title: bookData.title,
          description: bookData.description,
          price_amount: bookData.price * 100, // Convert to cents
          is_ebook: true,
          original_format: fileExt,
          processing_status: 'pending',
          file_path: uploadData.path
        })
        .select()
        .single()
      
      if (bookError) throw bookError
      setBookId(bookRecord.id)
      
      // Step 3: Start processing job
      setProcessingStatus({
        status: 'processing',
        progress: 30,
        currentStep: 'Analyzing document structure...'
      })
      
      // Route processing to our server API which supports epub, docx, doc, and rtf
      const resp = await fetch('/api/process-ebook', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId: bookRecord.id,
          filePath: uploadData.path,
          bucketName: 'book-uploads',
          fileType: String(fileExt).toLowerCase()
        })
      })

      const result = await resp.json()

      if (!resp.ok || !result?.success) {
        throw new Error(result?.error || 'Failed to start processing')
      }

      // Step 4: Update progress and finish
      setProcessingStatus({
        status: 'indexing',
        progress: 70,
        currentStep: 'Indexing chapters...'
      })

      setProcessingStatus({
        status: 'optimizing',
        progress: 85,
        currentStep: 'Optimizing for fast reading...'
      })

      setProcessingStatus({
        status: 'completed',
        progress: 100,
        currentStep: 'Book ready for reading!'
      })

      // Trigger completion after a short delay
      setTimeout(() => onComplete(bookId), 1200)
      
    } catch (error) {
      console.error('Upload failed:', error)
      const err: any = error as any
      setProcessingStatus({
        status: 'failed',
        progress: 0,
        currentStep: 'Upload failed',
        error: err?.message || 'Unknown error'
      })
    }
  }

  const pollProcessingStatus = async (jobId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const { data } = await supabase
          .from('book_processing_jobs')
          .select('*')
          .eq('id', jobId)
          .single()
        
        if (!data) return
        
        const progress = Math.round((data.processed_chapters / data.total_chapters) * 100)
        
        setProcessingStatus({
          status: data.status as any,
          progress: Math.max(30, progress),
          currentStep: getStepMessage(data.status, data.processed_chapters, data.total_chapters),
          totalChapters: data.total_chapters,
          processedChapters: data.processed_chapters,
          error: data.error_message
        })
        
        if (data.status === 'completed') {
          clearInterval(pollInterval)
          setProcessingStatus({
            status: 'completed',
            progress: 100,
            currentStep: 'Book ready for reading!'
          })
          setTimeout(() => onComplete(bookId), 1500)
        } else if (data.status === 'failed') {
          clearInterval(pollInterval)
        }
        
      } catch (error) {
        console.error('Polling error:', error)
        clearInterval(pollInterval)
      }
    }, 2000)
  }

  const getStepMessage = (status: string, processed: number, total: number): string => {
    switch (status) {
      case 'extracting':
        return 'Extracting text content...'
      case 'structuring':
        return `Creating chapters... (${processed}/${total})`
      case 'indexing':
        return `Indexing for highlights... (${processed}/${total})`
      case 'optimizing':
        return 'Optimizing for fast reading...'
      default:
        return 'Processing...'
    }
  }

  const renderUploadStep = () => (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Your Book
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          {isDragActive ? (
            <p className="text-blue-600">Drop your book file here...</p>
          ) : (
            <div>
              <p className="text-lg mb-2">Drag & drop your book file here</p>
              <p className="text-gray-500 mb-4">or click to browse</p>
              <p className="text-sm text-gray-400">
                Supports EPUB, DOCX, DOC, and RTF files (max 50MB)
              </p>
            </div>
          )}
        </div>
        
        {file && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">{file.name}</p>
                  <p className="text-sm text-gray-600">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setFile(null)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <Button
              className="w-full mt-3"
              onClick={() => setStep('metadata')}
            >
              Continue to Book Details
            </Button>
          </motion.div>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <AnimatePresence mode="wait">
        {step === 'upload' && (
          <motion.div
            key="upload"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
          >
            {renderUploadStep()}
          </motion.div>
        )}
      </AnimatePresence>
      
      <Button
        variant="ghost"
        className="absolute top-4 right-4 text-white hover:bg-white/20"
        onClick={onClose}
      >
        <X className="h-6 w-6" />
      </Button>
    </div>
  )
}
