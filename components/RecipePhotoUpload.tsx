"use client"

import { useState, useRef } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { photoStorage } from "@/lib/supabase/storage"
import { addWatermarkToFiles, validateImageFile } from "@/lib/watermark"
import Image from "next/image"

interface RecipePhotoUploadProps {
  recipeId?: string | null
  onEnsureRecipeId?: () => Promise<string | null>
  onPhotosChange?: (photos: Array<{ id: string; url: string; alt_text: string }>) => void
}

export default function RecipePhotoUpload({ recipeId, onEnsureRecipeId, onPhotosChange }: RecipePhotoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [photos, setPhotos] = useState<Array<{ id: string; url: string; alt_text: string }>>([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = createSupabaseClient()

  const handleSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    try {
      setError(null)
      setUploading(true)

      // Ensure we have a recipe id
      let currentRecipeId = recipeId
      if (!currentRecipeId && onEnsureRecipeId) {
        currentRecipeId = await onEnsureRecipeId()
        if (!currentRecipeId) throw new Error('Failed to create recipe draft')
      }
      if (!currentRecipeId) throw new Error('No recipe')

      const fileArray = Array.from(files)
      // Validate images
      for (const f of fileArray) {
        const v = validateImageFile(f)
        if (!v.valid) throw new Error(v.error)
        if (f.size > 10 * 1024 * 1024) throw new Error(`${f.name} is too large (max 10MB)`)
      }

      // Watermark
      const watermarked = await addWatermarkToFiles(fileArray, {
        text: 'www.OnlyDiary.app',
        position: 'bottom-right',
        opacity: 0.7,
        fontSize: 16
      })

      const uploaded: Array<{ id: string; url: string; alt_text: string }> = []
      for (const file of watermarked) {
        const ext = file.name.split('.').pop()
        const key = `recipes/${currentRecipeId}/${Date.now()}-${Math.random().toString(36).slice(2)}.${ext}`
        const { error: upErr } = await photoStorage.upload(key, file)
        if (upErr) throw new Error(`Upload failed: ${upErr.message}`)
        const { data: { publicUrl } } = photoStorage.getPublicUrl(key)

        const { data: rec, error: dbErr } = await supabase
          .from('recipe_photos' as any)
          .insert({ recipe_id: currentRecipeId, url: publicUrl, alt_text: file.name })
          .select('id, url, alt_text')
        if (dbErr) throw new Error(`Save failed: ${dbErr.message}`)
        if (rec && rec[0]) uploaded.push(rec[0])
      }

      const newList = [...photos, ...uploaded]
      setPhotos(newList)
      onPhotosChange?.(newList)
    } catch (err: any) {
      setError(err.message || 'Upload failed')
    } finally {
      setUploading(false)
      if (fileInputRef.current) fileInputRef.current.value = ''
    }
  }

  return (
    <div className="border-t border-gray-200 p-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-gray-900">Recipe Photos</h3>
        <span className="text-sm text-gray-500">{photos.length}/20</span>
      </div>
      {error && <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded text-red-600 text-sm">{error}</div>}
      <input ref={fileInputRef} type="file" accept="image/*" multiple onChange={handleSelect} className="hidden" />
      <button
        onClick={() => fileInputRef.current?.click()}
        disabled={uploading}
        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 disabled:opacity-50"
      >
        {uploading ? 'Uploading…' : 'Add Photos'}
      </button>
      {photos.length > 0 && (
        <div className="mt-4 grid grid-cols-2 gap-3">
          {photos.map((p) => (
            <div key={p.id} className="relative border border-gray-200 rounded-lg overflow-hidden flex justify-center">
              <Image src={p.url} alt={p.alt_text} width={400} height={300} className="object-contain bg-gray-50" />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

