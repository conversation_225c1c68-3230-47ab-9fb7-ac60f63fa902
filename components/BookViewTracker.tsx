'use client'

import { useEffect, useRef } from 'react'

type Props = { projectId: string }

export function BookViewTracker({ projectId }: Props) {
  const fired = useRef(false)

  useEffect(() => {
    if (!projectId || fired.current) return
    fired.current = true

    const controller = new AbortController()
    // Fire-and-forget; keepalive helps on page unload
    fetch(`/api/books/${projectId}/track-view`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({}),
      keepalive: true,
      signal: controller.signal,
      cache: 'no-store',
    }).catch(() => {})

    return () => controller.abort()
  }, [projectId])

  return null
}

