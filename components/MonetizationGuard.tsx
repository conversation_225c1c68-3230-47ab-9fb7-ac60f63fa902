"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { AlertCircle, CreditCard, DollarSign, Lock } from 'lucide-react'
import { 
  checkMonetizationEligibility, 
  getMonetizationBlockedMessage, 
  type MonetizationFeature,
  type MonetizationStatus 
} from '@/lib/stripe/monetization-guard'

interface MonetizationGuardProps {
  feature: MonetizationFeature
  children: React.ReactNode
  userId?: string
  fallback?: React.ReactNode
  className?: string
}

export function MonetizationGuard({ 
  feature, 
  children, 
  userId, 
  fallback,
  className = ""
}: MonetizationGuardProps) {
  const [status, setStatus] = useState<MonetizationStatus>({
    canMonetize: false,
    hasStripeAccount: false,
    onboardingComplete: false,
    reason: 'Loading...',
    actionRequired: 'none'
  })
  const [loading, setLoading] = useState(true)
  const [connectingStripe, setConnectingStripe] = useState(false)

  useEffect(() => {
    checkMonetizationEligibility(userId)
      .then(setStatus)
      .finally(() => setLoading(false))
  }, [userId])

  const handleConnectStripe = async () => {
    setConnectingStripe(true)
    
    try {
      const response = await fetch('/api/stripe/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to connect Stripe')
      }

      const data = await response.json()
      if (data.url) {
        window.location.href = data.url
      } else {
        throw new Error('No redirect URL received')
      }
    } catch (error) {
      console.error('Error connecting to Stripe:', error)
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setConnectingStripe(false)
    }
  }

  // Show loading state
  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-200 rounded-lg h-32 flex items-center justify-center">
          <div className="text-gray-500">Checking monetization status...</div>
        </div>
      </div>
    )
  }

  // If user can monetize, show the protected content
  if (status.canMonetize) {
    return <>{children}</>
  }

  // If custom fallback is provided, use it
  if (fallback) {
    return <>{fallback}</>
  }

  // Show monetization blocked message
  const message = getMonetizationBlockedMessage(feature)

  return (
    <div className={className}>
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardContent className="p-8 text-center">
          <div className="flex flex-col items-center space-y-4">
            {/* Icon based on action required */}
            <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
              {status.actionRequired === 'connect_stripe' ? (
                <CreditCard className="w-8 h-8 text-blue-600" />
              ) : status.actionRequired === 'complete_onboarding' ? (
                <AlertCircle className="w-8 h-8 text-orange-600" />
              ) : (
                <Lock className="w-8 h-8 text-gray-600" />
              )}
            </div>

            {/* Title and Message */}
            <div className="space-y-2">
              <h3 className="text-xl font-semibold text-gray-900">
                {message.title}
              </h3>
              <p className="text-gray-600 max-w-md">
                {message.message}
              </p>
            </div>

            {/* Status Details */}
            <div className="bg-white rounded-lg p-4 border border-gray-200 w-full max-w-sm">
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Stripe Account:</span>
                  <span className={`font-medium ${status.hasStripeAccount ? 'text-green-600' : 'text-red-600'}`}>
                    {status.hasStripeAccount ? '✓ Connected' : '✗ Not Connected'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Onboarding:</span>
                  <span className={`font-medium ${status.onboardingComplete ? 'text-green-600' : 'text-orange-600'}`}>
                    {status.onboardingComplete ? '✓ Complete' : '⚠ Incomplete'}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Button */}
            {status.actionRequired !== 'none' && (
              <Button
                onClick={handleConnectStripe}
                isLoading={connectingStripe}
                className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3"
                size="lg"
              >
                <CreditCard className="w-5 h-5 mr-2" />
                {status.actionRequired === 'complete_onboarding' ? 'Complete Setup' : message.ctaText}
              </Button>
            )}

            {/* Benefits */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200 w-full max-w-md">
              <h4 className="font-medium text-blue-900 mb-2 flex items-center">
                <DollarSign className="w-4 h-4 mr-1" />
                Why Connect Stripe?
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Secure payment processing</li>
                <li>• Direct deposits to your bank</li>
                <li>• Professional invoicing</li>
                <li>• Tax reporting support</li>
                <li>• Global payment acceptance</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Simplified hook for checking if user can monetize
export function useCanMonetize(userId?: string) {
  const [canMonetize, setCanMonetize] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkMonetizationEligibility(userId)
      .then(status => setCanMonetize(status.canMonetize))
      .finally(() => setLoading(false))
  }, [userId])

  return { canMonetize, loading }
}

// Quick check component for inline use
export function MonetizationCheck({ 
  feature, 
  children, 
  blocked 
}: { 
  feature: MonetizationFeature
  children: React.ReactNode
  blocked?: React.ReactNode 
}) {
  const { canMonetize, loading } = useCanMonetize()

  if (loading) {
    return <div className="animate-pulse bg-gray-200 rounded h-8"></div>
  }

  if (!canMonetize) {
    return blocked || (
      <div className="text-gray-500 text-sm italic">
        Connect Stripe to enable this feature
      </div>
    )
  }

  return <>{children}</>
}
