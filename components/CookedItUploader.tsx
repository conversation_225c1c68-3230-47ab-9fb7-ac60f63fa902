"use client"

import { useRef, useState } from "react"

export default function CookedItUploader({ recipeId, onCompleted }: { recipeId: string; onCompleted?: () => void }) {
  const [creating, setCreating] = useState(false)
  const [eventId, setEventId] = useState<string | null>(null)
  const [clips, setClips] = useState<Array<{ index: number; file?: File; progress: number; error?: string }>>([
    { index: 1, progress: 0 },
    { index: 2, progress: 0 },
    { index: 3, progress: 0 }
  ])

  const inputRefs = [useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null)]

  const createEvent = async () => {
    if (creating) return
    setCreating(true)
    try {
      const res = await fetch('/api/recipes/cook', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ recipeId }) })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || 'Failed to create cook event')
      setEventId(data.eventId)
    } catch (e: any) {
      alert(e.message)
    } finally {
      setCreating(false)
    }
  }

  const handlePick = (i: number, f: File) => {
    if (!f.type.startsWith('video/')) return alert('Please select a video file')
    if (f.size > 500 * 1024 * 1024) return alert('Max 500MB')
    setClips(prev => prev.map(c => c.index === i ? { ...c, file: f, progress: 0, error: undefined } : c))
  }

  const uploadClip = async (i: number) => {
    const clip = clips.find(c => c.index === i)
    if (!clip || !clip.file) return
    if (!eventId) return alert('Create the COOKED IT event first')

    try {
      // Ask server for signed URL
      const create = await fetch('/api/r2/recipes/cook-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ recipeId, eventId, clipIndex: i, filename: clip.file.name, fileSize: clip.file.size, mimeType: clip.file.type, duration: 9 })
      })
      const data = await create.json()
      if (!create.ok) throw new Error(data.error || 'Failed to prepare upload')

      // Upload
      setClips(prev => prev.map(c => c.index === i ? { ...c, progress: 50 } : c))
      const put = await fetch(data.uploadUrl, { method: 'PUT', body: clip.file, headers: { 'Content-Type': clip.file.type } })
      if (!put.ok) throw new Error('Upload failed')

      setClips(prev => prev.map(c => c.index === i ? { ...c, progress: 100 } : c))
    } catch (e: any) {
      setClips(prev => prev.map(c => c.index === i ? { ...c, error: e.message } : c))
    }
  }

  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium text-gray-900">COOKED IT</h3>
        {!eventId ? (
          <button onClick={createEvent} disabled={creating} className="bg-green-600 hover:bg-green-700 text-white px-3 py-1.5 rounded-lg text-sm disabled:opacity-50">{creating ? 'Starting…' : 'Start'}</button>
        ) : (
          <span className="text-sm text-gray-600">Event ready</span>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {[1,2,3].map(i => (
          <div key={i} className="border border-gray-200 rounded p-3">
            <p className="text-sm text-gray-700 mb-2">Clip {i}</p>
            <input ref={inputRefs[i-1]} type="file" accept="video/*" className="hidden" onChange={(e) => { const f = e.target.files?.[0]; if (f) handlePick(i, f) }} />
            <button onClick={() => inputRefs[i-1].current?.click()} className="bg-gray-100 text-gray-700 px-3 py-1.5 rounded text-sm">Choose Video</button>
            <button onClick={() => uploadClip(i)} className="ml-2 bg-blue-600 text-white px-3 py-1.5 rounded text-sm">Upload</button>
            <div className="h-2 bg-blue-200 rounded mt-2">
              <div className="h-2 bg-blue-600 rounded" style={{ width: `${clips.find(c => c.index===i)?.progress || 0}%` }} />
            </div>
            {clips.find(c => c.index===i)?.error && (
              <p className="text-xs text-red-600 mt-1">{clips.find(c => c.index===i)?.error}</p>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

