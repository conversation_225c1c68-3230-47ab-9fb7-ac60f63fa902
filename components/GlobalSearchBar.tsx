"use client"

import { useRouter } from 'next/navigation'
import { useState } from 'react'

interface GlobalSearchBarProps {
  placeholder?: string
  className?: string
}

export function GlobalSearchBar({ placeholder = 'Search people, diary entries, books, audio…', className = '' }: GlobalSearchBarProps) {
  const router = useRouter()
  const [q, setQ] = useState('')

  const go = () => {
    const query = q.trim()
    if (!query) return
    router.push(`/search?q=${encodeURIComponent(query)}`)
  }

  return (
    <div className={`w-full max-w-2xl mx-auto ${className}`}>
      <div className="relative">
        <input
          type="text"
          value={q}
          onChange={(e) => setQ(e.target.value)}
          onKeyDown={(e) => { if (e.key === 'Enter') go() }}
          placeholder={placeholder}
          className="w-full pl-4 pr-24 py-3 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800 placeholder:text-gray-500"
        />
        <button
          onClick={go}
          className="absolute right-2 top-1/2 -translate-y-1/2 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700"
        >
          Search
        </button>
      </div>
    </div>
  )
}

