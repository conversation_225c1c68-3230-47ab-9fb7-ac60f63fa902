"use client"

import { useState, useEffect, useRef } from 'react'
import { DuoVideoPlayer } from '@/components/duo/DuoVideoPlayer'
import { formatDistanceToNow } from 'date-fns'

interface DuoTheaterProps {
  isOpen: boolean
  onClose: () => void
  duos: Array<{
    id: string
    initiator: { name: string; profile_picture_url?: string }
    responder: { name: string; profile_picture_url?: string }
    created_at: string
    final_video_url?: string
    final_video_r2_key?: string
    partA: { src_r2_key: string; hls_manifest?: string }
    partB: { src_r2_key: string; hls_manifest?: string }
    reactions_count: number
    comments_count: number
    downloads_count: number
  }>
  initialDuoId?: string
}

export function DuoTheater({ isOpen, onClose, duos, initialDuoId }: DuoTheaterProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isReacted, setIsReacted] = useState(false)
  const [showComments, setShowComments] = useState(false)
  const [downloading, setDownloading] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const touchStartX = useRef<number>(0)
  const touchEndX = useRef<number>(0)

  // Find initial duo index
  useEffect(() => {
    if (initialDuoId && duos.length > 0) {
      const index = duos.findIndex(duo => duo.id === initialDuoId)
      if (index !== -1) {
        setCurrentIndex(index)
      }
    }
  }, [initialDuoId, duos])

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      } else if (e.key === 'ArrowLeft') {
        goToPrevious()
      } else if (e.key === 'ArrowRight') {
        goToNext()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen])

  // Touch handlers for swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX
  }

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return
    
    const distance = touchStartX.current - touchEndX.current
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe) {
      goToNext()
    } else if (isRightSwipe) {
      goToPrevious()
    }
  }

  const goToNext = () => {
    if (currentIndex < duos.length - 1) {
      setCurrentIndex(currentIndex + 1)
      setIsReacted(false)
      setShowComments(false)
    }
  }

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
      setIsReacted(false)
      setShowComments(false)
    }
  }

  const handleReact = () => {
    setIsReacted(!isReacted)
    // TODO: Call API to update reaction
  }

  const handleDownload = async () => {
    setDownloading(true)
    try {
      const duo = duos[currentIndex]
      const response = await fetch('/api/duo/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duoPostId: duo.id,
          includeWatermark: true,
          includeAttribution: true
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `onlyduo-${duo.id}.mp4`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }
    } catch (error) {
      console.error('Download failed:', error)
    } finally {
      setDownloading(false)
    }
  }

  if (!isOpen || duos.length === 0) return null

  const currentDuo = duos[currentIndex]
  const timeAgo = formatDistanceToNow(new Date(currentDuo.created_at), { addSuffix: true })

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      {/* Close Button */}
      <button
        onClick={onClose}
        className="absolute top-6 right-6 z-10 bg-white/10 backdrop-blur-sm text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-white/20 transition-colors"
      >
        <span className="text-xl">✕</span>
      </button>

      {/* Navigation Arrows */}
      {currentIndex > 0 && (
        <button
          onClick={goToPrevious}
          className="absolute left-6 top-1/2 -translate-y-1/2 z-10 bg-white/10 backdrop-blur-sm text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-white/20 transition-colors"
        >
          <span className="text-xl">←</span>
        </button>
      )}
      
      {currentIndex < duos.length - 1 && (
        <button
          onClick={goToNext}
          className="absolute right-6 top-1/2 -translate-y-1/2 z-10 bg-white/10 backdrop-blur-sm text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-white/20 transition-colors"
        >
          <span className="text-xl">→</span>
        </button>
      )}

      {/* Main Content */}
      <div 
        ref={containerRef}
        className="relative w-full h-full flex items-center justify-center"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Video Player */}
        <div className="relative w-full max-w-sm mx-auto" style={{ aspectRatio: '9/16', height: '90vh' }}>
          <DuoVideoPlayer
            partA={{
              type: 'video',
              src_r2_key: currentDuo.partA.src_r2_key,
              hls_manifest: currentDuo.partA.hls_manifest
            }}
            partB={{
              type: 'video',
              src_r2_key: currentDuo.partB.src_r2_key,
              hls_manifest: currentDuo.partB.hls_manifest
            }}
            signedPlayback={false}
          />

          {/* Overlay Info */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
            {/* Creator Info */}
            <div className="flex items-center space-x-3 mb-4">
              <div className="flex -space-x-2">
                {/* Initiator Avatar */}
                <div className="relative">
                  {currentDuo.initiator.profile_picture_url ? (
                    <img
                      src={currentDuo.initiator.profile_picture_url}
                      alt={currentDuo.initiator.name}
                      className="w-10 h-10 rounded-full border-2 border-white bg-gray-100"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded-full border-2 border-white bg-purple-500 flex items-center justify-center text-white text-sm font-medium">
                      {currentDuo.initiator.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                {/* Responder Avatar */}
                <div className="relative">
                  {currentDuo.responder.profile_picture_url ? (
                    <img
                      src={currentDuo.responder.profile_picture_url}
                      alt={currentDuo.responder.name}
                      className="w-10 h-10 rounded-full border-2 border-white bg-gray-100"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded-full border-2 border-white bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                      {currentDuo.responder.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
              </div>
              <div>
                <div className="text-white font-medium">
                  {currentDuo.initiator.name} & {currentDuo.responder.name}
                </div>
                <div className="text-white/70 text-sm">{timeAgo}</div>
              </div>
            </div>

            {/* Engagement Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleReact}
                  className={`flex items-center space-x-2 transition-colors ${
                    isReacted ? 'text-red-400' : 'text-white/70 hover:text-red-400'
                  }`}
                >
                  <span className="text-2xl">{isReacted ? '❤️' : '🤍'}</span>
                  <span className="font-medium">{currentDuo.reactions_count}</span>
                </button>
                
                <button
                  onClick={() => setShowComments(!showComments)}
                  className="flex items-center space-x-2 text-white/70 hover:text-blue-400 transition-colors"
                >
                  <span className="text-2xl">💬</span>
                  <span className="font-medium">{currentDuo.comments_count}</span>
                </button>
                
                <button
                  onClick={handleDownload}
                  disabled={downloading}
                  className="flex items-center space-x-2 text-white/70 hover:text-green-400 disabled:opacity-50 transition-colors"
                >
                  <span className="text-2xl">⬇️</span>
                  <span className="font-medium">{downloading ? 'Downloading...' : currentDuo.downloads_count}</span>
                </button>
              </div>

              {/* Progress Indicator */}
              <div className="text-white/50 text-sm">
                {currentIndex + 1} / {duos.length}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Comments Sidebar */}
      {showComments && (
        <div className="absolute right-0 top-0 bottom-0 w-80 bg-white/95 backdrop-blur-sm p-6 overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Comments</h3>
            <button
              onClick={() => setShowComments(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          
          {/* Comment Input */}
          <div className="mb-4">
            <textarea
              placeholder="Add a comment..."
              className="w-full p-3 border border-gray-200 rounded-lg resize-none"
              rows={3}
            />
            <button className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Post Comment
            </button>
          </div>
          
          {/* Comments List */}
          <div className="space-y-4">
            <div className="text-gray-500 text-sm text-center">
              No comments yet. Be the first to comment!
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
