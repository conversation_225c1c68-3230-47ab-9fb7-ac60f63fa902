'use client'

import { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { createSupabaseClient } from '@/lib/supabase/client'

interface Friend {
  id: string
  name: string
  avatar_url: string | null
  custom_url?: string | null
}

export function FriendsWhoBoughtInline({
  bookId,
  currentUserId,
  onOpenAll,
  className = ''
}: {
  bookId: string
  currentUserId?: string | null
  onOpenAll: () => void
  className?: string
}) {
  const [friends, setFriends] = useState<Friend[]>([])
  const [extraCount, setExtraCount] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const run = async () => {
      if (!bookId) return
      if (!currentUserId) {
        setLoading(false)
        return
      }
      setLoading(true)
      try {
        const supabase = createSupabaseClient()

        // Fetch follow relationships
        const [{ data: followers }, { data: following }] = await Promise.all([
          supabase.from('follows').select('follower_id').eq('writer_id', currentUserId),
          supabase.from('follows').select('writer_id').eq('follower_id', currentUserId),
        ])

        const followerIds = (followers || []).map((f: any) => f.follower_id)
        const followingIds = (following || []).map((f: any) => f.writer_id)
        const allRelatedUserIds: string[] = Array.from(new Set([...followerIds, ...followingIds]))

        if (allRelatedUserIds.length === 0) {
          setFriends([])
          setExtraCount(0)
          setLoading(false)
          return
        }

        // Purchases by related users
        const { data: purchases } = await supabase
          .from('book_purchases')
          .select('user_id, created_at')
          .eq('project_id', bookId)
          .in('user_id', allRelatedUserIds)
          .order('created_at', { ascending: false })

        const purchaserIds = Array.from(new Set((purchases || []).map((p: any) => p.user_id)))
        if (purchaserIds.length === 0) {
          setFriends([])
          setExtraCount(0)
          setLoading(false)
          return
        }

        // Fetch user profiles for top N (limit 10 for inline)
        const topIds = purchaserIds.slice(0, 10)
        const { data: profiles } = await supabase
          .from('users')
          .select('id, name, avatar, profile_picture_url, custom_url')
          .in('id', topIds)

        const profileMap = new Map((profiles || []).map((u: any) => [u.id, u]))
        const topFriends: Friend[] = topIds
          .map((id) => {
            const u = profileMap.get(id)
            if (!u) return null
            const avatarUrl = u.profile_picture_url || u.avatar || null
            return {
              id: u.id,
              name: u.name || 'Friend',
              avatar_url: avatarUrl,
              custom_url: u.custom_url || null,
            }
          })
          .filter(Boolean) as Friend[]

        setFriends(topFriends.slice(0, 5))
        setExtraCount(Math.max(0, purchaserIds.length - 5))
      } finally {
        setLoading(false)
      }
    }

    run()
  }, [bookId, currentUserId])

  // Not signed in: show subtle prompt to sign in to see friends
  if (!currentUserId) {
    return (
      <div className={`rounded-lg border border-gray-100 bg-white p-3 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">Sign in to see which of your friends bought this</div>
          <Link href="/login" className="text-xs text-purple-700 hover:underline">Sign in</Link>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className={`rounded-lg border border-gray-100 bg-white p-3 ${className}`}>
        <div className="h-6 bg-gray-100 rounded w-40"></div>
      </div>
    )
  }

  if (friends.length === 0) {
    // No social proof: render nothing to avoid negative signal
    return null
  }

  const totalCount = friends.length + extraCount
  const nameSample = friends.slice(0, 2).map(f => f.name.split(' ')[0])
  const summary = totalCount <= 2
    ? nameSample.join(', ')
    : `${nameSample.join(', ')} and ${Math.max(totalCount - nameSample.length, 0)} others`

  return (
    <div className={`rounded-lg border border-gray-100 bg-white p-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 min-w-0">
          <span className="text-sm font-medium text-gray-900">Friends who bought ({totalCount})</span>
          <div className="flex -space-x-2">
            {friends.map((f) => (
              <Link
                key={f.id}
                href={f.custom_url ? `/${f.custom_url}` : `/u/${f.id}`}
                className="inline-block w-7 h-7 rounded-full ring-2 ring-white overflow-hidden bg-gray-200"
                title={f.name}
              >
                {f.avatar_url ? (
                  <Image src={f.avatar_url} alt={f.name} width={28} height={28} className="object-cover w-full h-full" />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-[10px] text-gray-600">
                    {f.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </Link>
            ))}
            {extraCount > 0 && (
              <button
                type="button"
                onClick={onOpenAll}
                className="inline-flex items-center justify-center w-7 h-7 rounded-full ring-2 ring-white bg-gray-100 text-[10px] text-gray-700"
                title="See all"
              >
                +{extraCount >= 100 ? '100+' : extraCount}
              </button>
            )}
          </div>
        </div>
        <button
          onClick={onOpenAll}
          className="text-xs text-gray-600 truncate max-w-[40%] text-right"
          title={summary}
        >
          {summary}
        </button>
      </div>
    </div>
  )
}

