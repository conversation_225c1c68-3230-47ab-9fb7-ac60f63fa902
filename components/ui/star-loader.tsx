"use client"

import { cn } from "@/lib/utils"

interface StarLoaderProps {
  size?: "sm" | "md" | "lg"
  className?: string
}

export function StarLoader({ size = "md", className }: StarLoaderProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8"
  }

  const ringSize = size === 'sm' ? '12px' : size === 'md' ? '16px' : '20px'

  return (
    <div className={cn("relative inline-flex items-center justify-center", sizeClasses[size], className)}>
      {/* Center star pulsing */}
      <span className="absolute inset-0 flex items-center justify-center">
        <span className="text-yellow-400 animate-pulse">⭐</span>
      </span>

      {/* Orbiting stars using CSS animation */}
      {[0, 120, 240].map((angle, index) => (
        <span
          key={index}
          className="absolute"
          style={{
            transformOrigin: 'center',
            animation: `spin 3s linear infinite`,
            animationDelay: `${index * 0.2}s`
          }}
        >
          <span
            className="relative inline-block"
            style={{
              transform: `translateY(-${ringSize})`,
              animation: `pulse 1.5s ease-in-out ${index * 0.3}s infinite`
            }}
          >
            <span className="text-blue-400 text-xs">✨</span>
          </span>
        </span>
      ))}

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes pulse {
          0% { transform: translateY(-${ringSize}) scale(0.5); opacity: 0.3; }
          50% { transform: translateY(-${ringSize}) scale(1); opacity: 1; }
          100% { transform: translateY(-${ringSize}) scale(0.5); opacity: 0.3; }
        }
      `}</style>
    </div>
  )
}
