/**
 * Real-time notification listener for OnlyDiary
 * Handles payment and message sound notifications using centralized real-time system
 */

'use client'

import { useEffect } from 'react'
import { useNotificationSounds } from '@/hooks/useNotificationSounds'
import { usePaymentEvents, useSubscriptionEvents, useMessageEvents, useRealtime } from '@/lib/realtime/RealtimeManager'

interface NotificationListenerProps {
  userId?: string
}

function NotificationListenerCore({ userId }: NotificationListenerProps) {
  const { playPayment, playMessage } = useNotificationSounds()
  const { isConnected } = useRealtime()

  // Handle payment events
  usePaymentEvents((event) => {
    console.log('💰 Payment notification received:', event.data)
    playPayment()
    showPaymentToast(event.data)
  })

  // Handle subscription events
  useSubscriptionEvents((event) => {
    console.log('📝 Subscription notification received:', event.data)
    playPayment() // Use same sound for subscriptions
    showSubscriptionToast(event.data)
  })

  // Handle message events
  useMessageEvents((event) => {
    console.log('💬 Message notification received:', event.data)
    playMessage()
    showMessageToast(event.data)
  })

  useEffect(() => {
    if (userId && isConnected) {
      console.log('✅ Notification listener connected for user:', userId)
    }
  }, [userId, isConnected])

  // Helper functions for visual notifications
  const showPaymentToast = (data: any) => {
    // You can integrate with your toast system here
    console.log(`💰 Payment received: $${(data.amount / 100).toFixed(2)}`)
  }

  const showSubscriptionToast = (_data: any) => {
    console.log('📝 New subscriber!')
  }

  const showMessageToast = (_data: any) => {
    console.log('💬 New message received')
  }

  // This component doesn't render anything visible
  return null
}

// Main component that uses centralized real-time system
export function NotificationListener() {
  const { userId } = useRealtime()

  if (!userId) return null

  return <NotificationListenerCore userId={userId} />
}

// Hook for easy integration - now uses centralized system
export function useRealTimeNotifications() {
  const { playPayment, playMessage } = useNotificationSounds()

  // Use centralized event system instead of creating duplicate subscriptions
  usePaymentEvents(() => {
    playPayment()
  })

  useSubscriptionEvents(() => {
    playPayment()
  })

  useMessageEvents(() => {
    playMessage()
  })
}

// Removed duplicate function - use useRealTimeNotifications instead
