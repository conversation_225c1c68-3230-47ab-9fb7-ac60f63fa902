'use client'

import { useState } from 'react'

interface GoldenPenRatingProps {
  rating: number // 0-10
  totalReviews?: number
  interactive?: boolean
  onRatingChange?: (rating: number) => void
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  compact?: boolean // when true, show a single dynamic pen and toggle to expand full details
}

export function GoldenPenRating({
  rating,
  totalReviews,
  interactive = false,
  onRatingChange,
  size = 'md',
  showText = true,
  compact = false,
}: GoldenPenRatingProps) {
  const [hoverRating, setHoverRating] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)
  const [expanded, setExpanded] = useState(false)

  const displayRating = interactive && hoverRating > 0 ? hoverRating : rating
  const fullPens = Math.floor(displayRating)
  const partialPen = displayRating - fullPens

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  const handlePenClick = (penIndex: number) => {
    if (!interactive || !onRatingChange) return

    setIsAnimating(true)
    onRatingChange(penIndex + 1)

    setTimeout(() => setIsAnimating(false), 600)
  }

  const handlePenHover = (penIndex: number) => {
    if (!interactive) return
    setHoverRating(penIndex + 1)
  }

  const handleMouseLeave = () => {
    if (!interactive) return
    setHoverRating(0)
  }

  const FountainPen = ({
    filled,
    partial = 0,
    index,
    isHovered = false
  }: {
    filled: boolean
    partial?: number
    index: number
    isHovered?: boolean
  }) => (
    <div
      className={`relative ${sizeClasses[size]} cursor-pointer transition-all duration-200 ${
        isHovered ? 'scale-110' : ''
      }`}
      onClick={() => handlePenClick(index)}
      onMouseEnter={() => handlePenHover(index)}
      style={{
        filter: filled || partial > 0 ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.6))' : 'none'
      }}
    >
      {/* Fountain Pen SVG */}
      <svg
        viewBox="0 0 24 24"
        className={`w-full h-full transition-all duration-300 ${
          isAnimating ? 'animate-pulse' : ''
        }`}
        style={{
          filter: filled || partial > 0 ? 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))' : 'none'
        }}
      >
        {/* Pen Body */}
        <path
          d="M3 21l1.9-1.9L9.2 14.8c0.4-0.4 1-0.4 1.4 0l0.4 0.4c0.4 0.4 0.4 1 0 1.4L6.7 20.5L3 21z"
          fill={filled || partial > 0 ? '#FFD700' : '#E5E7EB'}
          className="transition-all duration-300"
        />

        {/* Pen Tip */}
        <path
          d="M14.1 12.4l2.8-2.8c0.4-0.4 1-0.4 1.4 0l2.8 2.8c0.4 0.4 0.4 1 0 1.4l-2.8 2.8c-0.4 0.4-1 0.4-1.4 0l-2.8-2.8c-0.4-0.4-0.4-1 0-1.4z"
          fill={filled || partial > 0 ? '#FFA500' : '#D1D5DB'}
          className="transition-all duration-300"
        />

        {/* Nib */}
        <path
          d="M11 16.4l2.8-2.8 1.4 1.4-2.8 2.8z"
          fill={filled || partial > 0 ? '#FF8C00' : '#9CA3AF'}
          className="transition-all duration-300"
        />

        {/* Partial fill overlay */}
        {partial > 0 && partial < 1 && (
          <defs>
            <linearGradient id={`partial-${index}`} x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset={`${partial * 100}%`} stopColor="#FFD700" />
              <stop offset={`${partial * 100}%`} stopColor="#E5E7EB" />
            </linearGradient>
          </defs>
        )}

        {partial > 0 && partial < 1 && (
          <path
            d="M3 21l1.9-1.9L9.2 14.8c0.4-0.4 1-0.4 1.4 0l0.4 0.4c0.4 0.4 0.4 1 0 1.4L6.7 20.5L3 21z"
            fill={`url(#partial-${index})`}
          />
        )}
      </svg>

      {/* Hover flourish animation */}
      {isHovered && interactive && (
        <div className="absolute -top-1 -right-1 pointer-events-none">
          <svg className="w-3 h-3 text-yellow-400 animate-bounce" viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
            />
          </svg>
        </div>
      )}
      </div>
    )

  if (compact) {
    if (!expanded) {
    // Single dynamic pen display with optional text and an expand/collapse to show full 10-pen view
    const normalized = Math.max(0, Math.min(10, rating))
    const penLabel = normalized === 10 ? 'Perfect' : normalized >= 7.5 ? 'Excellent' : normalized >= 5 ? 'Good' : normalized >= 2.5 ? 'Okay' : 'Poor'
    const showHalf = normalized > 0 && normalized < 10 && Math.abs(normalized - 5) < 0.5

    return (
      <div className="flex items-center gap-2">
        {/* Dynamic single pen */}
        <div className={`relative ${sizeClasses[size]}`}>
          <svg viewBox="0 0 24 24" className="w-full h-full">
            {/* Base pen (gray) */}
            <path d="M3 21l1.9-1.9L9.2 14.8c0.4-0.4 1-0.4 1.4 0l0.4 0.4c0.4 0.4 0.4 1 0 1.4L6.7 20.5L3 21z" fill="#E5E7EB" />
            <path d="M14.1 12.4l2.8-2.8c0.4-0.4 1-0.4 1.4 0l2.8 2.8c0.4 0.4 0.4 1 0 1.4l-2.8 2.8c-0.4 0.4-1 0.4-1.4 0l-2.8-2.8c-0.4-0.4-0.4-1 0-1.4z" fill="#D1D5DB" />
            <path d="M11 16.4l2.8-2.8 1.4 1.4-2.8 2.8z" fill="#9CA3AF" />
            {/* Gold overlay fill proportionally by rating */}
            <defs>
              <linearGradient id="gold-fill" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset={`${(normalized/10)*100}%`} stopColor="#FFD700" />
                <stop offset={`${(normalized/10)*100}%`} stopColor="#E5E7EB" />
              </linearGradient>
            </defs>
            <path d="M3 21l1.9-1.9L9.2 14.8c0.4-0.4 1-0.4 1.4 0l0.4 0.4c0.4 0.4 0.4 1 0 1.4L6.7 20.5L3 21z" fill="url(#gold-fill)" />
          </svg>
        </div>
        {showText && (
          <span className="text-xs sm:text-sm text-gray-800">
            {rating.toFixed(1)}/10{typeof totalReviews === 'number' && totalReviews > 0 ? ` • ${totalReviews} ${totalReviews === 1 ? 'review' : 'reviews'}` : ''}
          </span>
        )}
        <button type="button" onClick={() => setExpanded(true)} className="text-xs text-blue-600 hover:text-blue-700">Details</button>
      </div>
    )
  }

  // Expanded view: show the full 10-pen component below the compact row
  return (
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-2">
          <span className="text-xs sm:text-sm text-gray-800">{rating.toFixed(1)}/10</span>
          <button type="button" onClick={() => setExpanded(false)} className="text-xs text-blue-600 hover:text-blue-700">Hide</button>
        </div>
        {/* Reuse full renderer below */}
        <div>
          {/* Pen Rating Display */}
          <div className="flex items-center gap-1" onMouseLeave={handleMouseLeave}>
            {Array.from({ length: 10 }, (_, index) => {
              const penNumber = index + 1
              const isFilled = penNumber <= fullPens
              const isPartial = penNumber === fullPens + 1 && partialPen > 0
              const isHovered = interactive && hoverRating >= penNumber
              return (
                <FountainPen key={index} filled={isFilled} partial={isPartial ? partialPen : 0} index={index} isHovered={isHovered} />
              )
            })}
          </div>
          {showText && (
            <div className={`flex items-center gap-2 ${textSizeClasses[size]}`}>
              <span className="font-semibold text-gray-900">{rating.toFixed(1)}/10</span>
              {typeof totalReviews === 'number' && totalReviews > 0 && (
                <span className="text-gray-600">({totalReviews.toLocaleString()} {totalReviews === 1 ? 'review' : 'reviews'})</span>
              )}
            </div>
          )}
        </div>
      </div>
    )

  }

  return (
    <div className="flex flex-col gap-2">
      {/* Pen Rating Display */}
      <div
        className="flex items-center gap-1"
        onMouseLeave={handleMouseLeave}
      >
        {Array.from({ length: 10 }, (_, index) => {
          const penNumber = index + 1
          const isFilled = penNumber <= fullPens
          const isPartial = penNumber === fullPens + 1 && partialPen > 0
          const isHovered = interactive && hoverRating >= penNumber

          return (
            <FountainPen
              key={index}
              filled={isFilled}
              partial={isPartial ? partialPen : 0}
              index={index}
              isHovered={isHovered}
            />
          )
        })}
      </div>

      {/* Rating Text */}
      {showText && (
        <div className={`flex items-center gap-2 ${textSizeClasses[size]}`}>
          <span className="font-semibold text-gray-900">
            {displayRating.toFixed(1)}/10
          </span>
          {typeof totalReviews === 'number' && totalReviews > 0 && (
            <span className="text-gray-600">
              ({totalReviews.toLocaleString()} {totalReviews === 1 ? 'review' : 'reviews'})
            </span>
          )}
        </div>
      )}

      {/* Interactive feedback */}
      {interactive && hoverRating > 0 && (
        <div className="text-sm text-gray-600 animate-fade-in">
          This book deserves {hoverRating} golden pen{hoverRating !== 1 ? 's' : ''}
        </div>
      )}
    </div>
  )
}

// Rating distribution component
export function RatingDistribution({
  ratings
}: {
  ratings: { [key: number]: number }
}) {
  const totalRatings = Object.values(ratings).reduce((sum, count) => sum + count, 0)
  const maxCount = Math.max(...Object.values(ratings))

  return (
    <div className="space-y-1">
      <h4 className="text-sm font-medium text-gray-700 mb-2">Rating Distribution</h4>
      {Array.from({ length: 10 }, (_, index) => {
        const penCount = 10 - index
        const count = ratings[penCount] || 0
        const percentage = totalRatings > 0 ? (count / totalRatings) * 100 : 0
        const barWidth = maxCount > 0 ? (count / maxCount) * 100 : 0

        return (
          <div key={penCount} className="flex items-center gap-2 text-xs">
            <span className="w-6 text-right">{penCount}</span>
            <div className="flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden">
              <div
                className="bg-gradient-to-r from-yellow-400 to-yellow-600 h-full rounded-full transition-all duration-500"
                style={{ width: `${barWidth}%` }}
              />
            </div>
            <span className="w-12 text-right text-gray-600">
              {percentage.toFixed(0)}%
            </span>
          </div>
        )
      })}
    </div>
  )
}
