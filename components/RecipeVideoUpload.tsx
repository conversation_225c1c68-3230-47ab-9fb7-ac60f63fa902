"use client"

import { useState, useRef } from "react"

interface Props {
  recipeId?: string | null
  onEnsureRecipeId?: () => Promise<string | null>
  onUploaded?: (video: { id: string; r2_public_url: string; title?: string }) => void
}

export default function RecipeVideoUpload({ recipeId, onEnsureRecipeId, onUploaded }: Props) {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const startUpload = async (file: File) => {
    // Validate type
    if (!file.type.startsWith('video/')) {
      setError('Please select a valid video file')
      return
    }
    // 500MB limit
    if (file.size > 500 * 1024 * 1024) {
      setError('Video file must be less than 500MB')
      return
    }

    try {
      setUploading(true)
      setProgress(10)
      setError(null)

      let currentRecipeId = recipeId
      if (!currentRecipeId && onEnsureRecipeId) {
        currentRecipeId = await onEnsureRecipeId()
      }
      if (!currentRecipeId) throw new Error('Failed to create recipe draft')

      // Get signed URL
      const createRes = await fetch('/api/r2/recipes/upload-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filename: file.name, recipeId: currentRecipeId, fileSize: file.size, mimeType: file.type })
      })
      if (!createRes.ok) {
        const data = await createRes.json().catch(() => ({}))
        throw new Error(data.error || 'Failed to prepare upload')
      }
      const { uploadUrl, publicUrl, video } = await createRes.json()

      setProgress(50)
      // Upload to R2
      const putRes = await fetch(uploadUrl, { method: 'PUT', body: file, headers: { 'Content-Type': file.type } })
      if (!putRes.ok) throw new Error('Upload failed')

      setProgress(100)
      onUploaded?.({ id: video.id, r2_public_url: publicUrl, title: video.title })
    } catch (e: any) {
      setError(e.message || 'Upload failed')
    } finally {
      setUploading(false)
      setTimeout(() => setProgress(0), 1500)
      if (inputRef.current) inputRef.current.value = ''
    }
  }

  return (
    <div className="border-t border-gray-200 p-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-gray-900">Instruction Video</h3>
      </div>
      {error && <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded text-red-600 text-sm">{error}</div>}
      <input ref={inputRef} type="file" accept="video/*" className="hidden" onChange={(e) => {
        const f = e.target.files?.[0]
        if (f) startUpload(f)
      }} />
      <button
        onClick={() => inputRef.current?.click()}
        disabled={uploading}
        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 disabled:opacity-50"
      >
        {uploading ? 'Uploading…' : 'Add Video'}
      </button>
      {progress > 0 && (
        <div className="mt-3 w-full bg-blue-200 h-2 rounded">
          <div className="bg-blue-600 h-2 rounded" style={{ width: `${progress}%` }} />
        </div>
      )}
      <p className="text-xs text-gray-500 mt-2">Supported: MP4, MOV, AVI, MKV, WebM • Max size: 500MB</p>
    </div>
  )
}

