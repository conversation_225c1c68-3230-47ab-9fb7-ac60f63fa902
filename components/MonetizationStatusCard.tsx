"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CheckCircle, AlertCircle, CreditCard, DollarSign } from 'lucide-react'
import { checkMonetizationEligibility, type MonetizationStatus } from '@/lib/stripe/monetization-guard'

interface MonetizationStatusCardProps {
  userId?: string
  className?: string
}

export function MonetizationStatusCard({ userId, className = "" }: MonetizationStatusCardProps) {
  const [status, setStatus] = useState<MonetizationStatus>({
    canMonetize: false,
    hasStripeAccount: false,
    onboardingComplete: false,
    reason: 'Loading...',
    actionRequired: 'none'
  })
  const [loading, setLoading] = useState(true)
  const [connectingStripe, setConnectingStripe] = useState(false)

  useEffect(() => {
    checkMonetizationEligibility(userId)
      .then(setStatus)
      .finally(() => setLoading(false))
  }, [userId])

  const handleConnectStripe = async () => {
    setConnectingStripe(true)
    
    try {
      const response = await fetch('/api/stripe/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to connect Stripe')
      }

      const data = await response.json()
      if (data.url) {
        window.location.href = data.url
      } else {
        throw new Error('No redirect URL received')
      }
    } catch (error) {
      console.error('Error connecting to Stripe:', error)
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setConnectingStripe(false)
    }
  }

  if (loading) {
    return (
      <Card className={`border-gray-200 ${className}`}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`border-gray-200 ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {status.canMonetize ? (
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
            ) : (
              <div className="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center">
                <AlertCircle className="w-5 h-5 text-orange-600" />
              </div>
            )}
            
            <div>
              <h3 className="font-medium text-gray-900">
                {status.canMonetize ? 'Monetization Active' : 'Monetization Setup Required'}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {status.reason}
              </p>
            </div>
          </div>

          {status.actionRequired !== 'none' && (
            <Button
              onClick={handleConnectStripe}
              isLoading={connectingStripe}
              size="sm"
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              <CreditCard className="w-4 h-4 mr-1" />
              {status.actionRequired === 'complete_onboarding' ? 'Complete Setup' : 'Connect Stripe'}
            </Button>
          )}
        </div>

        {/* Status Details */}
        <div className="mt-4 grid grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${status.hasStripeAccount ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm text-gray-600">Stripe Account</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${status.onboardingComplete ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm text-gray-600">Onboarding Complete</span>
          </div>
        </div>

        {/* Benefits when ready */}
        {status.canMonetize && (
          <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center space-x-2 mb-2">
              <DollarSign className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">Ready to Earn!</span>
            </div>
            <ul className="text-xs text-green-800 space-y-1">
              <li>• Sell books and earn 80% royalties</li>
              <li>• Accept subscriptions from readers</li>
              <li>• Monetize recipes and audio content</li>
              <li>• Receive direct deposits to your bank</li>
            </ul>
          </div>
        )}

        {/* Setup encouragement */}
        {!status.canMonetize && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center space-x-2 mb-2">
              <CreditCard className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Why Connect Stripe?</span>
            </div>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• Secure payment processing</li>
              <li>• Direct deposits to your bank account</li>
              <li>• Professional invoicing and tax reporting</li>
              <li>• Global payment acceptance</li>
              <li>• Industry-standard security</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Simplified version for smaller spaces
export function MonetizationStatusBadge({ userId, className = "" }: MonetizationStatusCardProps) {
  const [status, setStatus] = useState<MonetizationStatus>({
    canMonetize: false,
    hasStripeAccount: false,
    onboardingComplete: false,
    reason: 'Loading...',
    actionRequired: 'none'
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkMonetizationEligibility(userId)
      .then(setStatus)
      .finally(() => setLoading(false))
  }, [userId])

  if (loading) {
    return <div className={`animate-pulse bg-gray-200 rounded-full h-6 w-24 ${className}`}></div>
  }

  return (
    <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium ${
      status.canMonetize 
        ? 'bg-green-100 text-green-800' 
        : 'bg-orange-100 text-orange-800'
    } ${className}`}>
      {status.canMonetize ? (
        <>
          <CheckCircle className="w-3 h-3" />
          <span>Monetization Active</span>
        </>
      ) : (
        <>
          <AlertCircle className="w-3 h-3" />
          <span>Setup Required</span>
        </>
      )}
    </div>
  )
}
