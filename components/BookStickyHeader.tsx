'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'

type Props = {
  title: string
  priceCents: number
  bookId: string
  hasPurchased: boolean
  chaptersAvailable: boolean
}

export function BookStickyHeader({ title, priceCents, bookId, hasPurchased, chaptersAvailable }: Props) {
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    const onScroll = () => setVisible(window.scrollY > 240)
    onScroll()
    window.addEventListener('scroll', onScroll, { passive: true })
    return () => window.removeEventListener('scroll', onScroll)
  }, [])

  if (!visible) return null

  const isFree = priceCents === 0

  return (
    <div className="fixed bottom-3 left-1/2 -translate-x-1/2 z-40 w-[calc(100%-1.5rem)] sm:w-[calc(100%-3rem)] max-w-3xl">
      <div className="rounded-full border border-gray-200 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/60 shadow-lg px-3 py-2 flex items-center gap-3">
        <div className="min-w-0 truncate font-medium text-gray-900">{title}</div>
        <div className="ml-auto flex items-center gap-2">
          {hasPurchased ? (
            <Link href={`/books/${bookId}/read`} className="px-3 py-1.5 text-xs rounded-full bg-black text-white hover:bg-gray-900">
              Read
            </Link>
          ) : (
            <button
              onClick={async () => {
                if (isFree) {
                  try {
                    const res = await fetch('/api/books/free-download', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ bookId })
                    })
                    const data = await res.json()
                    if (data.success) {
                      window.location.href = `/books/${bookId}/read`
                    }
                  } catch (e) { console.error(e) }
                } else {
                  try {
                    const res = await fetch('/api/books/purchase', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ bookId, priceAmount: priceCents })
                    })
                    const data = await res.json()
                    if (data.url) {
                      window.location.href = data.url
                    }
                  } catch (e) { console.error(e) }
                }
              }}
              className={`px-3 py-1.5 text-xs rounded-full text-white ${isFree ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700' : 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700'}`}
              disabled={!chaptersAvailable && isFree}
            >
              {isFree ? (chaptersAvailable ? 'Read' : 'Processing...') : 'Buy Now'}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

