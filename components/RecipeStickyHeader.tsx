'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { FollowButton } from '@/components/FollowButton'

type Props = {
  title: string
  rank: number | null
  writerId: string
  writerName?: string | null
}

export function RecipeStickyHeader({ title, rank, writerId, writerName }: Props) {
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    const onScroll = () => {
      // Show after user has scrolled a bit
      setVisible(window.scrollY > 240)
    }
    onScroll()
    window.addEventListener('scroll', onScroll, { passive: true })
    return () => window.removeEventListener('scroll', onScroll)
  }, [])

  if (!visible) return null

  return (
    <div className="fixed bottom-3 left-1/2 -translate-x-1/2 z-40 w-[calc(100%-1.5rem)] sm:w-[calc(100%-3rem)] max-w-3xl">
      <div className="rounded-full border border-gray-200 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/60 shadow-lg px-3 py-2 flex items-center gap-3">
        <div className="flex items-center gap-2 min-w-0">
          {rank && (
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-semibold border bg-blue-50 text-blue-700 border-blue-200">
              {rank === 1 ? '#1 Recipe' : `#${rank} in Recipes`}
            </span>
          )}
          <span className="truncate font-medium text-gray-900">{title}</span>
        </div>
        <div className="ml-auto flex items-center gap-2">
          <Link href="#instructions" className="px-3 py-1.5 text-xs rounded-full bg-black text-white hover:bg-gray-900">Start Cooking</Link>
          <FollowButton writerId={writerId} writerName={writerName || undefined} size="sm" />
        </div>
      </div>
    </div>
  )
}

