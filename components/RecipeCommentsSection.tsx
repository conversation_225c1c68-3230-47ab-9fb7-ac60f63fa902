"use client"

import { useEffect, useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { Day1Badge } from './Day1Badge'
import { ReactionSystem } from './ReactionSystem'

interface Comment {
  id: string
  body: string
  created_at: string
  parent_comment_id?: string
  user: {
    id: string
    name: string
    avatar?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  reactions?: Record<string, number>
  userReaction?: string | null
}

export function RecipeCommentsSection({
  recipeId,
  canComment,
  userId,
  maxDepth = 3,
  isCompact = true,
}: {
  recipeId: string
  canComment: boolean
  userId?: string
  maxDepth?: number
  isCompact?: boolean
}) {
  const supabase = createSupabaseClient()
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [newComment, setNewComment] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyText, setReplyText] = useState('')

  useEffect(() => {
    loadComments()
  }, [recipeId])

  async function loadComments() {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          id,
          body,
          created_at,
          parent_comment_id,
          user:users!user_id (
            id,
            name,
            avatar,
            has_day1_badge,
            signup_number,
            badge_tier
          )
        `)
        .eq('recipe_id', recipeId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true })

      if (!error && data) {
        setComments(data as any)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newComment.trim() || !userId) return
    setSubmitting(true)
    try {
      const { error } = await supabase.from('comments').insert({
        recipe_id: recipeId,
        user_id: userId,
        body: newComment.trim(),
      })
      if (!error) {
        setNewComment('')
        loadComments()
      }
    } finally {
      setSubmitting(false)
    }
  }

  const handleReply = async (parentId: string) => {
    if (!replyText.trim() || !userId) return
    setSubmitting(true)
    try {
      const { error } = await supabase.from('comments').insert({
        recipe_id: recipeId,
        user_id: userId,
        body: replyText.trim(),
        parent_comment_id: parentId,
      })
      if (!error) {
        setReplyText('')
        setReplyingTo(null)
        loadComments()
      }
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) return <div className="text-xs text-gray-500 py-2">Loading comments…</div>

  return (
    <div className="space-y-3">
      {/* New comment */}
      {canComment && userId && (
        <form onSubmit={handleSubmit} className="space-y-2">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Write a comment…"
            className={`w-full p-2 border border-gray-300 rounded bg-white text-gray-900 placeholder-gray-500 ${isCompact ? 'text-xs' : 'text-sm'} resize-none focus:outline-none focus:ring-1 focus:ring-blue-500`}
            rows={2}
            maxLength={1000}
            disabled={submitting}
          />
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={!newComment.trim() || submitting}
              className="px-3 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700 disabled:opacity-50"
            >
              {submitting ? 'Posting…' : 'Post'}
            </button>
          </div>
        </form>
      )}

      {/* List */}
      <div className="space-y-3">
        {comments.map((c) => (
          <div key={c.id} className="space-y-1">
            <div className="flex items-start gap-2">
              <div className={`${isCompact ? 'w-6 h-6' : 'w-8 h-8'} rounded-full bg-gray-200 flex items-center justify-center overflow-hidden`}>
                {c.user.avatar ? (
                  <img src={c.user.avatar} alt={c.user.name} className={`${isCompact ? 'w-6 h-6' : 'w-8 h-8'} object-cover rounded-full`} />
                ) : (
                  <span className={`${isCompact ? 'text-xs' : 'text-sm'} text-gray-600`}>{c.user.name.charAt(0).toUpperCase()}</span>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <button onClick={() => window.location.href = `/u/${c.user.id}`} className={`font-medium text-gray-900 ${isCompact ? 'text-xs' : 'text-sm'} hover:text-blue-600`}>
                    {c.user.name}
                  </button>
                  {c.user.has_day1_badge && (
                    <Day1Badge signupNumber={c.user.signup_number} badgeTier={c.user.badge_tier} size="sm" />
                  )}
                  <span className="text-xs text-gray-500">{new Date(c.created_at).toLocaleString()}</span>
                </div>
                <p className={`${isCompact ? 'text-xs' : 'text-sm'} text-gray-700`}>{c.body}</p>
                <div className="flex items-center gap-3 mt-1">
                  <ReactionSystem contentId={c.id} contentType="comment" currentUserId={userId} initialReactions={c.reactions || {}} userReaction={c.userReaction} onReactionUpdate={() => {}} />
                  {canComment && userId && (
                    <button onClick={() => setReplyingTo(replyingTo === c.id ? null : c.id)} className="text-blue-600 hover:text-blue-700 text-xs font-medium">
                      {replyingTo === c.id ? 'Cancel' : 'Reply'}
                    </button>
                  )}
                </div>
                {replyingTo === c.id && (
                  <form
                    onSubmit={(e) => {
                      e.preventDefault()
                      if (replyText.trim()) handleReply(c.id)
                    }}
                    className="mt-2"
                  >
                    <textarea
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      placeholder={`Reply to ${c.user.name}…`}
                      className={`w-full p-2 border border-gray-300 rounded bg-white text-gray-900 placeholder-gray-500 ${isCompact ? 'text-xs' : 'text-sm'} resize-none focus:outline-none focus:ring-1 focus:ring-blue-500`}
                      rows={2}
                      maxLength={1000}
                      disabled={submitting}
                    />
                    <div className="flex justify-end mt-1">
                      <button type="submit" disabled={!replyText.trim() || submitting} className="px-2 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700 disabled:opacity-50">{submitting ? 'Posting…' : 'Reply'}</button>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

