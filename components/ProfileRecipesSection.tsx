"use client"

import Link from "next/link"

export interface ProfileRecipeItem {
  id: string
  title: string
  description?: string
  cover_photo_url?: string
  is_free: boolean
  created_at: string
}

export function ProfileRecipesSection({
  recipes,
  writerName
}: {
  recipes: ProfileRecipeItem[]
  writerName: string
}) {
  if (recipes.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">🍳</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Recipes Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any recipes yet. Check back soon!
        </p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {recipes.map((r) => (
        <Link key={r.id} href={`/recipes/${r.id}`} className="group block">
          <div className="bg-white rounded-xl overflow-hidden border border-gray-100 hover:shadow-md transition-all">
            <div className="aspect-video bg-gray-50">
              {r.cover_photo_url ? (
                <img src={r.cover_photo_url} alt={r.title} className="w-full h-full object-cover" />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-3xl">🍳</div>
              )}
            </div>
            <div className="p-3">
              <h3 className="font-serif text-base text-gray-900 mb-1 line-clamp-1 group-hover:text-green-700 transition-colors">{r.title}</h3>
              {r.description && (
                <p className="text-xs text-gray-600 line-clamp-2">{r.description}</p>
              )}
              <div className="mt-2 text-xs text-gray-500 flex items-center gap-2">
                <span className="px-2 py-0.5 rounded-full bg-purple-100 text-purple-700 border border-purple-200">
                  {r.is_free ? 'Free' : 'Paid'}
                </span>
                <span>{new Date(r.created_at).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}

