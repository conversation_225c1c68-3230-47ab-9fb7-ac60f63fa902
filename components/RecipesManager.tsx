'use client'

import { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { createSupabaseClient } from '@/lib/supabase/client'
import { DeleteConfirmModal, useDeleteConfirm } from '@/components/DeleteConfirmModal'

interface Recipe {
  id: string
  title: string
  description?: string
  cover_photo_url?: string
  is_free: boolean
  created_at: string
}

export function RecipesManager({ userId }: { userId: string }) {
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [loading, setLoading] = useState(true)
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const supabase = createSupabaseClient()
  const deleteConfirm = useDeleteConfirm()

  useEffect(() => {
    const load = async () => {
      try {
        const { data } = await supabase
          .from('recipes' as any)
          .select('id, title, description, cover_photo_url, is_free, created_at')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })

        setRecipes(data || [])
      } finally {
        setLoading(false)
      }
    }

    load()
  }, [supabase, userId])

  const handleDelete = (recipe: Recipe) => {
    deleteConfirm.showConfirm(async () => {
      setDeletingId(recipe.id)
      try {
        // Delete dependent records first
        await supabase.from('recipe_steps' as any).delete().eq('recipe_id', recipe.id)
        await supabase.from('recipe_ingredients' as any).delete().eq('recipe_id', recipe.id)
        await supabase.from('recipe_videos' as any).delete().eq('recipe_id', recipe.id)
        await supabase.from('recipe_photos' as any).delete().eq('recipe_id', recipe.id)
        await supabase.from('recipe_cook_events' as any).delete().eq('recipe_id', recipe.id)

        const { error } = await supabase.from('recipes' as any).delete().eq('id', recipe.id)
        if (error) throw error

        setRecipes(prev => prev.filter(r => r.id !== recipe.id))
      } finally {
        setDeletingId(null)
      }
    })
  }

  if (loading) {
    return (
      <div className="p-4 text-sm text-gray-600">Loading recipes...</div>
    )
  }

  if (recipes.length === 0) {
    return (
      <div className="border border-gray-200 rounded-lg p-6 text-center">
        <h3 className="text-lg font-serif text-gray-800 mb-3">No recipes yet</h3>
        <p className="text-gray-600 text-sm">Create your first one to get started.</p>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {recipes.map(r => (
        <div key={r.id} className="border rounded-lg p-3 flex items-center gap-3">
          <div className="w-16 h-16 rounded overflow-hidden bg-gray-100 flex items-center justify-center">
            {r.cover_photo_url ? (
              <Image src={r.cover_photo_url} alt={r.title} width={64} height={64} className="w-full h-full object-cover" />
            ) : (
              <span className="text-xl">🍳</span>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h4 className="font-medium text-gray-900 truncate">{r.title}</h4>
              {r.is_free ? (
                <span className="text-[10px] bg-gray-100 text-gray-700 px-2 py-0.5 rounded">FREE</span>
              ) : (
                <span className="text-[10px] bg-purple-100 text-purple-700 px-2 py-0.5 rounded">PAID</span>
              )}
            </div>
            <p className="text-xs text-gray-600 line-clamp-2">{r.description}</p>
          </div>
          <div className="flex items-center gap-2">
            <Link href={`/recipes/${r.id}`} className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1.5 rounded">View</Link>
            <Link href={`/write/recipes?edit=${r.id}`} className="text-xs bg-gray-900 hover:bg-gray-800 text-white px-3 py-1.5 rounded">Edit</Link>
            <button
              onClick={() => handleDelete(r)}
              disabled={deletingId === r.id || deleteConfirm.isLoading}
              className="text-xs bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-3 py-1.5 rounded"
            >
              {deletingId === r.id ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </div>
      ))}

      <DeleteConfirmModal
        isOpen={deleteConfirm.isOpen}
        onClose={deleteConfirm.handleClose}
        onConfirm={deleteConfirm.handleConfirm}
        isLoading={deleteConfirm.isLoading}
        entryTitle="this recipe"
        title="Delete Recipe"
        message="Are you sure you want to delete this recipe and all associated photos, videos, ingredients, and steps? This cannot be undone."
        confirmText="Delete Forever"
      />
    </div>
  )
}

