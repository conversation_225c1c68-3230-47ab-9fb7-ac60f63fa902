"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Day1Badge } from "./Day1Badge"
import { ReactionSystem } from "./ReactionSystem"

interface Comment {
  id: string
  body: string
  created_at: string
  parent_comment_id?: string
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  replies?: Comment[]
  depth?: number
  reactions?: Record<string, number>
  userReaction?: string | null
}

interface DuoCompactCommentsSectionProps {
  duoId: string
  canComment: boolean
  userId?: string
  isOpen: boolean
  onToggle: () => void
  commentCount?: number
  onCommentCountChange?: (newCount: number) => void
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit'
  })
}

// Recursive component to render nested comments
function NestedComment({ 
  comment, 
  canComment, 
  userId, 
  onReply, 
  replyingTo, 
  setReplyingTo,
  replyText,
  setReplyText,
  submitting,
  maxDepth = 5
}: {
  comment: Comment
  canComment: boolean
  userId?: string
  onReply: (parentId: string, text: string) => void
  replyingTo: string | null
  setReplyingTo: (id: string | null) => void
  replyText: string
  setReplyText: (text: string) => void
  submitting: boolean
  maxDepth?: number
}) {
  const depth = comment.depth || 0
  const canReply = canComment && userId && depth < maxDepth
  const marginLeft = Math.min(depth * 16, 64) // Max 64px indent

  return (
    <div className="py-3" style={{ marginLeft: `${marginLeft}px` }}>
      <div className="flex gap-3">
        {/* Avatar */}
        <div className="flex-shrink-0">
          {comment.user.profile_picture_url || comment.user.avatar ? (
            <img
              src={comment.user.profile_picture_url || comment.user.avatar}
              alt={comment.user.name}
              className="w-8 h-8 rounded-full bg-gray-200 object-cover"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white text-sm font-bold">
              {comment.user.name.charAt(0).toUpperCase()}
            </div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-gray-900 text-sm">
              {comment.user.name}
            </span>
            {comment.user.has_day1_badge && (
              <Day1Badge 
                signupNumber={comment.user.signup_number} 
                badgeTier={comment.user.badge_tier}
                size="xs" 
              />
            )}
            <span className="text-xs text-gray-500">
              {formatDate(comment.created_at)}
            </span>
          </div>

          {/* Comment body */}
          <div className="text-sm text-gray-700 mb-2 whitespace-pre-wrap break-words">
            {comment.body}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-4">
            <ReactionSystem
              contentId={comment.id}
              contentType="comment"
              currentUserId={userId}
              initialReactions={comment.reactions || {}}
              userReaction={comment.userReaction}
              onReactionUpdate={() => {}}
              variant="fb"
            />
            
            {canReply && (
              <button
                onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                className="text-xs text-gray-500 hover:text-gray-700 font-medium"
              >
                Reply
              </button>
            )}
          </div>

          {/* Reply form */}
          {replyingTo === comment.id && (
            <div className="mt-3">
              <textarea
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder="Write a reply..."
                className="w-full p-2 text-sm border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={2}
              />
              <div className="flex gap-2 mt-2">
                <button
                  onClick={() => {
                    onReply(comment.id, replyText)
                  }}
                  disabled={!replyText.trim() || submitting}
                  className="px-3 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Posting...' : 'Reply'}
                </button>
                <button
                  onClick={() => {
                    setReplyingTo(null)
                    setReplyText("")
                  }}
                  className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}

          {/* Nested replies */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-2">
              {comment.replies.map((reply) => (
                <NestedComment
                  key={reply.id}
                  comment={reply}
                  canComment={canComment}
                  userId={userId}
                  onReply={onReply}
                  replyingTo={replyingTo}
                  setReplyingTo={setReplyingTo}
                  replyText={replyText}
                  setReplyText={setReplyText}
                  submitting={submitting}
                  maxDepth={maxDepth}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export function DuoCompactCommentsSection({
  duoId,
  canComment,
  userId,
  isOpen,
  onToggle,
  commentCount = 0,
  onCommentCountChange
}: DuoCompactCommentsSectionProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(false)
  const [newComment, setNewComment] = useState("")
  const [replyText, setReplyText] = useState("")
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState("")

  const supabase = createSupabaseClient()

  useEffect(() => {
    if (isOpen) {
      loadComments()
    }
  }, [isOpen, duoId])

  const loadComments = async () => {
    if (!isOpen) return
    
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          id,
          body,
          created_at,
          parent_comment_id,
          user:users!user_id (
            id,
            name,
            avatar,
            profile_picture_url,
            has_day1_badge,
            signup_number,
            badge_tier
          )
        `)
        .eq('duo_post_id', duoId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true })

      if (error) {
        console.error('Error loading comments:', error)
        return
      }

      // Build nested comment structure
      const commentMap = new Map<string, Comment>()
      const rootComments: Comment[] = []

      // First pass: create all comments
      data?.forEach((comment: any) => {
        const commentObj: Comment = {
          ...comment,
          replies: [],
          depth: 0
        }
        commentMap.set(comment.id, commentObj)
      })

      // Second pass: build hierarchy
      data?.forEach((comment: any) => {
        const commentObj = commentMap.get(comment.id)!
        
        if (comment.parent_comment_id) {
          const parent = commentMap.get(comment.parent_comment_id)
          if (parent) {
            commentObj.depth = (parent.depth || 0) + 1
            parent.replies = parent.replies || []
            parent.replies.push(commentObj)
          }
        } else {
          rootComments.push(commentObj)
        }
      })

      setComments(rootComments)

      // Update comment count
      const totalComments = data?.length || 0
      onCommentCountChange?.(totalComments)
    } catch (err) {
      console.error('Error loading comments:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newComment.trim() || !userId) return

    setSubmitting(true)
    setError("")

    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          duo_post_id: duoId,
          user_id: userId,
          body: newComment.trim()
        })

      if (error) {
        setError(`Failed to post comment: ${error.message}`)
      } else {
        setNewComment("")
        loadComments()
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  const handleSubmitReply = async (parentCommentId: string, text: string) => {
    if (!text.trim() || !userId) return

    setSubmitting(true)
    setError("")

    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          duo_post_id: duoId,
          user_id: userId,
          body: text,
          parent_comment_id: parentCommentId
        })

      if (error) {
        setError(`Failed to post reply: ${error.message}`)
      } else {
        setReplyText("")
        setReplyingTo(null)
        loadComments()
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="border-t border-gray-100">
      {isOpen && (
        <div className="px-4 py-3 bg-gray-50/50">
          {error && (
            <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-red-600 text-xs">
              {error}
            </div>
          )}

          {/* Comment Input */}
          {canComment && userId && (
            <form onSubmit={handleSubmitComment} className="mb-4">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment..."
                className="w-full p-3 text-sm border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={2}
              />
              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-gray-500">
                  {newComment.length}/1000
                </span>
                <button
                  type="submit"
                  disabled={!newComment.trim() || submitting}
                  className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Posting...' : 'Comment'}
                </button>
              </div>
            </form>
          )}

          {/* Comments List */}
          {loading ? (
            <div className="text-center py-3">
              <div className="text-xs text-gray-500">Loading comments...</div>
            </div>
          ) : comments.length > 0 ? (
            <div className="max-h-80 overflow-y-auto divide-y divide-gray-100">
              {comments.map((comment) => (
                <NestedComment
                  key={comment.id}
                  comment={comment}
                  canComment={canComment}
                  userId={userId}
                  onReply={handleSubmitReply}
                  replyingTo={replyingTo}
                  setReplyingTo={setReplyingTo}
                  replyText={replyText}
                  setReplyText={setReplyText}
                  submitting={submitting}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500 text-xs">
                No comments yet. Be the first to share your thoughts!
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
