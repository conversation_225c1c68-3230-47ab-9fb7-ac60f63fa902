"use client"

import { useState } from "react"

export interface IngredientItem {
  section?: string
  amount?: string
  unit?: string
  item: string
  note?: string
}

export default function IngredientsEditor({ value, onChange }: { value: IngredientItem[]; onChange: (v: IngredientItem[]) => void }) {
  const [items, setItems] = useState<IngredientItem[]>(value)

  const update = (next: IngredientItem[]) => {
    setItems(next)
    onChange(next)
  }

  const add = () => update([...items, { item: "" }])
  const remove = (i: number) => update(items.filter((_, idx) => idx !== i))
  const move = (i: number, dir: -1 | 1) => {
    const j = i + dir
    if (j < 0 || j >= items.length) return
    const next = items.slice()
    const tmp = next[i]
    next[i] = next[j]
    next[j] = tmp
    update(next)
  }

  const setField = (i: number, field: keyof IngredientItem, val: string) => {
    const next = items.slice()
    next[i] = { ...next[i], [field]: val }
    update(next)
  }

  return (
    <div className="border-t border-gray-200 p-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-gray-900">Ingredients</h3>
        <button onClick={add} className="bg-gray-100 text-gray-700 px-3 py-1.5 rounded text-sm">Add</button>
      </div>
      <div className="space-y-3">
        {items.map((it, i) => (
          <div key={i} className="grid grid-cols-1 md:grid-cols-6 gap-2 items-start">
            <div className="md:col-span-2">
              <input className="w-full border border-gray-300 rounded px-2 py-1" placeholder="Section (optional)" value={it.section || ''} onChange={e => setField(i, 'section', e.target.value)} />
              <p className="text-xs text-gray-500 italic mt-1">e.g., Sauce, Toppings</p>
            </div>
            <div>
              <input className="w-full border border-gray-300 rounded px-2 py-1" placeholder="Amount" value={it.amount || ''} onChange={e => setField(i, 'amount', e.target.value)} />
              <p className="text-xs text-gray-500 italic mt-1">e.g., 1</p>
            </div>
            <div>
              <input className="w-full border border-gray-300 rounded px-2 py-1" placeholder="Unit" value={it.unit || ''} onChange={e => setField(i, 'unit', e.target.value)} />
              <p className="text-xs text-gray-500 italic mt-1">e.g., cup, tsp</p>
            </div>
            <div className="md:col-span-2">
              <input className="w-full border border-gray-300 rounded px-2 py-1" placeholder="Item" value={it.item} onChange={e => setField(i, 'item', e.target.value)} />
              <p className="text-xs text-gray-500 italic mt-1">e.g., all-purpose flour</p>
            </div>
            <div className="md:col-span-3">
              <input className="w-full border border-gray-300 rounded px-2 py-1" placeholder="Note (optional)" value={it.note || ''} onChange={e => setField(i, 'note', e.target.value)} />
              <p className="text-xs text-gray-500 italic mt-1">e.g., sifted, room temperature</p>
            </div>
            <div className="flex gap-2">
              <button onClick={() => move(i, -1)} className="px-2 py-1 bg-gray-100 rounded">↑</button>
              <button onClick={() => move(i, 1)} className="px-2 py-1 bg-gray-100 rounded">↓</button>
              <button onClick={() => remove(i)} className="px-2 py-1 bg-red-100 text-red-700 rounded">Remove</button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

