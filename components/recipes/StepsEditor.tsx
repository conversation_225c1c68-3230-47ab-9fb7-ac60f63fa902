"use client"

import { useState } from "react"

export interface StepItem { instruction: string; time_hint?: string }

export default function StepsEditor({ value, onChange }: { value: StepItem[]; onChange: (v: StepItem[]) => void }) {
  const [items, setItems] = useState<StepItem[]>(value)

  const update = (next: StepItem[]) => { setItems(next); onChange(next) }
  const add = () => update([...items, { instruction: "" }])
  const remove = (i: number) => update(items.filter((_, idx) => idx !== i))
  const move = (i: number, dir: -1 | 1) => {
    const j = i + dir
    if (j < 0 || j >= items.length) return
    const next = items.slice()
    const tmp = next[i]; next[i] = next[j]; next[j] = tmp
    update(next)
  }

  const setField = (i: number, field: keyof StepItem, val: string) => {
    const next = items.slice(); next[i] = { ...next[i], [field]: val }; update(next)
  }

  return (
    <div className="border-t border-gray-200 p-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-gray-900">Instructions</h3>
        <button onClick={add} className="bg-gray-100 text-gray-700 px-3 py-1.5 rounded text-sm">Add Step</button>
      </div>
      <ol className="space-y-3 list-decimal pl-6">
        {items.map((it, i) => (
          <li key={i} className="space-y-2">
            <textarea className="w-full border border-gray-300 rounded px-3 py-2" placeholder="Step instruction" value={it.instruction} onChange={e => setField(i, 'instruction', e.target.value)} />
            <p className="text-xs text-gray-500 italic mt-1">e.g., Whisk eggs and sugar until pale and fluffy.</p>
            <input className="w-full border border-gray-300 rounded px-3 py-2" placeholder="Time hint (e.g., 10 min simmer)" value={it.time_hint || ''} onChange={e => setField(i, 'time_hint', e.target.value)} />
            <p className="text-xs text-gray-500 italic mt-1">e.g., 10 min, 350°F for 20 min</p>
            <div className="flex gap-2">
              <button onClick={() => move(i, -1)} className="px-2 py-1 bg-gray-100 rounded">↑</button>
              <button onClick={() => move(i, 1)} className="px-2 py-1 bg-gray-100 rounded">↓</button>
              <button onClick={() => remove(i)} className="px-2 py-1 bg-red-100 text-red-700 rounded">Remove</button>
            </div>
          </li>
        ))}
      </ol>
    </div>
  )
}

