"use client"

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { GoldenPenRating } from './GoldenPenRating'
import Image from 'next/image'

interface BookReview {
  id: string
  rating: number
  review_text?: string
  created_at: string
  user: {
    id: string
    name: string
    profile_picture_url?: string
  }
}

interface BookDetails {
  id: string
  title: string
  description?: string
  cover_image_url?: string
  genre?: string
  price_amount: number
  average_rating?: number
  review_count?: number
  user: {
    name: string
    profile_picture_url?: string
  }
}

interface BookDetailsModalProps {
  bookId: string
  onClose: () => void
}

export function BookDetailsModal({ bookId, onClose }: BookDetailsModalProps) {
  const [book, setBook] = useState<BookDetails | null>(null)
  const [reviews, setReviews] = useState<BookReview[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchBookDetails = async () => {
      try {
        const supabase = createSupabaseClient()
        
        // Fetch book details
        const { data: bookData, error: bookError } = await supabase
          .from('books')
          .select(`
            id,
            title,
            description,
            cover_image_url,
            genre,
            price_amount,
            average_rating,
            review_count,
            user:users!books_user_id_fkey(name, profile_picture_url)
          `)
          .eq('id', bookId)
          .single()

        if (bookError) throw bookError

        // Fetch reviews
        const { data: reviewsData, error: reviewsError } = await supabase
          .from('book_reviews')
          .select(`
            id,
            rating,
            review_text,
            created_at,
            user:users!book_reviews_user_id_fkey(id, name, profile_picture_url)
          `)
          .eq('book_id', bookId)
          .order('created_at', { ascending: false })
          .limit(10)

        if (reviewsError) throw reviewsError

        setBook(bookData)
        setReviews(reviewsData || [])
      } catch (error) {
        console.error('Error fetching book details:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchBookDetails()
  }, [bookId])

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Book Details</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(80vh-80px)]">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : book ? (
            <div className="p-4 space-y-6">
              {/* Book Info */}
              <div className="flex gap-4">
                <div className="w-20 flex-shrink-0">
                  {book.cover_image_url ? (
                    <Image
                      src={book.cover_image_url}
                      alt={book.title}
                      width={80}
                      height={120}
                      className="w-full h-auto object-contain rounded"
                    />
                  ) : (
                    <div className="w-full aspect-[3/4] bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-2xl opacity-50">📖</span>
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="font-serif text-lg text-gray-900 mb-2">{book.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">by {book.user.name}</p>
                  
                  {book.average_rating && book.review_count && (
                    <div className="mb-3">
                      <GoldenPenRating
                        rating={book.average_rating}
                        totalReviews={book.review_count}
                        size="sm"
                        showText={true}
                      />
                    </div>
                  )}
                  
                  <div className="flex items-center gap-3 text-sm">
                    {book.genre && (
                      <span className="bg-gray-100 px-2 py-1 rounded text-xs">
                        {book.genre}
                      </span>
                    )}
                    <span className="font-medium text-green-600">
                      {book.price_amount === 0 ? 'Free' : `$${(book.price_amount / 100).toFixed(2)}`}
                    </span>
                  </div>
                </div>
              </div>

              {book.description && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-sm text-gray-600 leading-relaxed">{book.description}</p>
                </div>
              )}

              {/* Reviews Section */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">
                  Reviews {reviews.length > 0 && `(${reviews.length})`}
                </h4>
                
                {reviews.length > 0 ? (
                  <div className="space-y-4">
                    {reviews.map((review) => (
                      <div key={review.id} className="border border-gray-100 rounded-lg p-3">
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
                            {review.user.profile_picture_url ? (
                              <Image
                                src={review.user.profile_picture_url}
                                alt={review.user.name}
                                width={32}
                                height={32}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <span className="text-sm text-gray-500">
                                {review.user.name.charAt(0).toUpperCase()}
                              </span>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-sm text-gray-900">{review.user.name}</span>
                              <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <span
                                    key={i}
                                    className={`text-sm ${
                                      i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                                    }`}
                                  >
                                    ✒️
                                  </span>
                                ))}
                              </div>
                              <span className="text-xs text-gray-500">{formatTimeAgo(review.created_at)}</span>
                            </div>
                            {review.review_text && (
                              <p className="text-sm text-gray-600 leading-relaxed">{review.review_text}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 italic">No reviews yet</p>
                )}
              </div>
            </div>
          ) : (
            <div className="p-8 text-center">
              <p className="text-gray-500">Book not found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
