"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Day1Badge } from "./Day1Badge"
import { ReactionSystem } from "./ReactionSystem"
import { usePathname } from "next/navigation"
import { REALTIME_ENABLED } from "@/lib/config"

interface Comment {
  id: string
  body: string
  created_at: string
  parent_comment_id?: string
  user: {
    id: string
    name: string
    avatar?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  replies?: Comment[]
  depth?: number
  reactions?: Record<string, number>
  userReaction?: string | null
}

interface CommentsSectionProps {
  entryId: string
  canComment: boolean
  userId?: string
  maxDepth?: number
  showInCard?: boolean
  maxCommentsInCard?: number
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function CommentsSection({ entryId, canComment, userId }: CommentsSectionProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState("")
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState("")
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyText, setReplyText] = useState("")

  const pathname = usePathname()
  const supabase = createSupabaseClient()

  useEffect(() => {
    loadComments()

    // Check if realtime is disabled globally
    if (!REALTIME_ENABLED) {
      console.log('🚫 Comments real-time disabled via config')
      return
    }

    // Disable real-time on timeline page for performance
    if (pathname === '/timeline') {
      console.log('🔔 Comments real-time disabled on timeline page for performance')
      return
    }

    // Set up real-time subscription for new comments (only on non-timeline pages)
    const channel = supabase
      .channel(`comments-${entryId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'comments',
          filter: `diary_entry_id=eq.${entryId}`
        },
        (payload) => {
          console.log('New comment received:', payload)
          
          // EMERGENCY FIX: Add debouncing to prevent infinite loops
          setTimeout(() => {
            loadComments()
          }, 1000)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'comments',
          filter: `diary_entry_id=eq.${entryId}`
        },
        (payload) => {
          console.log('Comment updated:', payload)
          
          // EMERGENCY FIX: Add debouncing to prevent infinite loops  
          setTimeout(() => {
            loadComments()
          }, 1000)
        }
      )
      .subscribe()

    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(channel)
    }
  }, [entryId, pathname, supabase])

  const loadComments = async () => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          id,
          body,
          created_at,
          parent_comment_id,
          user:users!user_id (
            id,
            name,
            avatar,
            has_day1_badge,
            signup_number,
            badge_tier
          )
        `)
        .eq('diary_entry_id', entryId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: true })

      if (error) {
        console.error('Error loading comments:', error)
      } else {
        // Load reaction data for each comment
        const commentsWithReactions = await Promise.all(
          (data || []).map(async (comment) => {
            try {
              const response = await fetch(`/api/comments/${comment.id}/reactions`)
              if (response.ok) {
                const reactionData = await response.json()
                return {
                  ...comment,
                  reactions: reactionData.reactions || {},
                  userReaction: reactionData.userReaction
                }
              }
            } catch (err) {
              console.error('Error loading reactions for comment:', comment.id, err)
            }
            return {
              ...comment,
              reactions: {},
              userReaction: null
            }
          })
        )
        setComments(commentsWithReactions)
      }
    } catch (err) {
      console.error('Error loading comments:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newComment.trim() || !userId) return

    setSubmitting(true)
    setError("")

    try {
      console.log('Attempting to insert comment with:', {
        diary_entry_id: entryId,
        user_id: userId,
        body: newComment.trim().substring(0, 50) + '...'
      })

      const { error } = await supabase
        .from('comments')
        .insert({
          diary_entry_id: entryId,
          user_id: userId,
          body: newComment.trim()
        })

      if (error) {
        console.error('Comment insert error:', error)
        console.error('Error details:', JSON.stringify(error, null, 2))
        
        // Check if it's an RLS policy error (empty error object)
        if (Object.keys(error).length === 0) {
          // Check the type of entry to provide a more specific error
          setError('You do not have permission to comment on this entry. This might be because you need to purchase credits or subscribe to this writer.')
        } else if (error.code === '42501') {
          setError('You need to be subscribed to this writer to comment on paid entries.')
        } else if (error.code === '23503') {
          setError('Invalid entry or user. Please refresh the page and try again.')
        } else if (error.message) {
          setError(`Failed to post comment: ${error.message}`)
        } else {
          setError('Unable to post comment. Please try refreshing the page.')
        }
      } else {
        // Don't add to local state - real-time subscription will handle it
        setNewComment("")
      }
    } catch (err: unknown) {
      console.error('Unexpected comment error:', err)
      setError(`An unexpected error occurred: ${err.message || 'Please try again.'}`)
    } finally {
      setSubmitting(false)
    }
  }

  const handleSubmitReply = async (parentCommentId: string) => {
    if (!replyText.trim() || !userId) return

    setSubmitting(true)
    setError("")

    try {
      const { error } = await supabase
        .from('comments')
        .insert({
          diary_entry_id: entryId,
          user_id: userId,
          body: replyText.trim(),
          parent_comment_id: parentCommentId
        })

      if (error) {
        console.error('Reply insert error:', error)
        setError(`Failed to post reply: ${error.message || 'Please try again.'}`)
      } else {
        // Don't add to local state - real-time subscription will handle it
        setReplyText("")
        setReplyingTo(null)
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-serif mb-4 text-gray-800">Comments</h3>
        <div className="text-gray-500 text-sm">Loading comments...</div>
      </div>
    )
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 shadow-sm">
      <h3 className="text-lg font-serif mb-4 text-blue-800">
        💬 Comments ({comments.length})
      </h3>

      {/* Comment Form */}
      {canComment && userId ? (
        <form onSubmit={handleSubmitComment} className="mb-6">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Share your thoughts..."
            className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent resize-none bg-white text-gray-900 placeholder-gray-500"
            rows={3}
            maxLength={1000}
          />
          
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-gray-500">
              {newComment.length}/1000 characters
            </span>
            
            {error && (
              <span className="text-red-600 text-sm">{error}</span>
            )}
            
            <button
              type="submit"
              disabled={submitting || !newComment.trim()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {submitting ? "Posting..." : "Post Comment"}
            </button>
          </div>
        </form>
      ) : !canComment ? (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-gray-600 text-sm">
            Subscribe to this writer to join the conversation
          </p>
        </div>
      ) : (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-gray-600 text-sm">
            Please sign in to comment
          </p>
        </div>
      )}

      {/* Comments List */}
      {comments.length > 0 ? (
        <div className="space-y-6">
          {/* Render top-level comments and their replies */}
          {comments
            .filter(comment => !comment.parent_comment_id)
            .map((comment) => (
              <div key={comment.id} className="space-y-3">
                {/* Main Comment */}
                <div className="border-b border-gray-100 pb-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0">
                      {comment.user.avatar ? (
                        <button
                          onClick={() => window.location.href = `/u/${comment.user.id}`}
                          className="focus:outline-none focus:ring-2 focus:ring-blue-300 rounded-full"
                          aria-label={`View ${comment.user.name}'s profile`}
                        >
                          <img
                            src={comment.user.avatar}
                            alt={comment.user.name}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        </button>
                      ) : (
                        <button
                          onClick={() => window.location.href = `/u/${comment.user.id}`}
                          className="text-sm font-medium text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-300 rounded-full w-full h-full"
                          aria-label={`View ${comment.user.name}'s profile`}
                        >
                          {comment.user.name.charAt(0).toUpperCase()}
                        </button>
                      )}
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <button
                          onClick={() => window.location.href = `/u/${comment.user.id}`}
                          className="font-medium text-gray-900 text-sm hover:text-blue-600 transition-colors text-left"
                        >
                          {comment.user.name}
                        </button>
                        {comment.user.has_day1_badge && (
                          <Day1Badge
                            signupNumber={comment.user.signup_number}
                            badgeTier={comment.user.badge_tier}
                            size="sm"
                            className="flex-shrink-0"
                          />
                        )}
                        <span className="text-xs text-gray-500">
                          {formatDate(comment.created_at)}
                        </span>
                      </div>

                      <p className="text-gray-700 text-sm leading-relaxed mb-2">
                        {comment.body}
                      </p>

                      {/* Reaction System */}
                      <div className="flex items-center gap-3 mb-1">
                        <ReactionSystem
                          contentId={comment.id}
                          contentType="comment"
                          currentUserId={userId}
                          initialReactions={comment.reactions || {}}
                          userReaction={comment.userReaction}
                          onReactionUpdate={(reactions, userReaction) => {
                            // Update local state for immediate feedback
                            setComments(prevComments =>
                              prevComments.map(c =>
                                c.id === comment.id
                                  ? { ...c, reactions, userReaction }
                                  : c
                              )
                            )
                          }}
                        />

                        {/* Reply Button */}
                        {canComment && userId && (
                          <button
                            onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                            className="text-blue-600 hover:text-blue-700 text-xs font-medium"
                          >
                            {replyingTo === comment.id ? 'Cancel' : 'Reply'}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Reply Form */}
                {replyingTo === comment.id && canComment && userId && (
                  <div className="ml-11 bg-blue-50 rounded-lg p-3">
                    <form onSubmit={(e) => {
                      e.preventDefault()
                      handleSubmitReply(comment.id)
                    }}>
                      <textarea
                        value={replyText}
                        onChange={(e) => setReplyText(e.target.value)}
                        placeholder={`Reply to ${comment.user.name}...`}
                        className="w-full p-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm bg-white text-gray-900 placeholder-gray-500"
                        rows={2}
                        disabled={submitting}
                        maxLength={1000}
                      />
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-xs text-gray-500">
                          {replyText.length}/1000 characters
                        </span>
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={() => {
                              setReplyingTo(null)
                              setReplyText("")
                            }}
                            className="px-3 py-1 text-gray-600 hover:text-gray-800 text-xs"
                            disabled={submitting}
                          >
                            Cancel
                          </button>
                          <button
                            type="submit"
                            disabled={!replyText.trim() || submitting}
                            className="px-3 py-1 bg-blue-600 text-white rounded text-xs font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {submitting ? 'Posting...' : 'Reply'}
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                )}

                {/* Replies */}
                {comments
                  .filter(reply => reply.parent_comment_id === comment.id)
                  .map((reply) => (
                    <div key={reply.id} className="ml-11 bg-blue-50 rounded-lg p-3">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 rounded-full bg-blue-200 flex items-center justify-center flex-shrink-0">
                          {reply.user.avatar ? (
                            <button
                              onClick={() => window.location.href = `/u/${reply.user.id}`}
                              className="focus:outline-none focus:ring-2 focus:ring-blue-300 rounded-full"
                              aria-label={`View ${reply.user.name}'s profile`}
                            >
                              <img
                                src={reply.user.avatar}
                                alt={reply.user.name}
                                className="w-6 h-6 rounded-full object-cover"
                              />
                            </button>
                          ) : (
                            <button
                              onClick={() => window.location.href = `/u/${reply.user.id}`}
                              className="text-xs font-medium text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300 rounded-full w-full h-full"
                              aria-label={`View ${reply.user.name}'s profile`}
                            >
                              {reply.user.name.charAt(0).toUpperCase()}
                            </button>
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <button
                              onClick={() => window.location.href = `/u/${reply.user.id}`}
                              className="font-medium text-gray-900 text-xs hover:text-blue-600 transition-colors text-left"
                            >
                              {reply.user.name}
                            </button>
                            {reply.user.has_day1_badge && (
                              <Day1Badge
                                signupNumber={reply.user.signup_number}
                                badgeTier={reply.user.badge_tier}
                                size="sm"
                                className="flex-shrink-0"
                              />
                            )}
                            <span className="text-xs text-gray-500">
                              {formatDate(reply.created_at)}
                            </span>
                          </div>
                          <p className="text-gray-700 text-xs leading-relaxed mb-2">
                            {reply.body}
                          </p>

                          {/* Reaction System for Reply */}
                          <ReactionSystem
                            contentId={reply.id}
                            contentType="comment"
                            currentUserId={userId}
                            initialReactions={reply.reactions || {}}
                            userReaction={reply.userReaction}
                            onReactionUpdate={(reactions, userReaction) => {
                              // Update local state for immediate feedback
                              setComments(prevComments =>
                                prevComments.map(c =>
                                  c.id === reply.id
                                    ? { ...c, reactions, userReaction }
                                    : c
                                )
                              )
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 text-sm">
            No comments yet. Be the first to share your thoughts!
          </p>
        </div>
      )}
    </div>
  )
}
