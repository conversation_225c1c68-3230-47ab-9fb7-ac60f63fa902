"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { BookCommentsSection } from "./BookCommentsSection"

interface CompactBookCommentsSectionProps {
  bookId: string
  canComment: boolean
  userId?: string
  isOpen: boolean
  onToggle: () => void
  commentCount?: number
  onCommentCountChange?: (newCount: number) => void
}

export function CompactBookCommentsSection({
  bookId,
  canComment,
  userId,
  isOpen,
  onToggle,
  commentCount = 0,
  onCommentCountChange
}: CompactBookCommentsSectionProps) {
  return (
    <div className="border-t border-gray-100">
      {/* Comments Section - No redundant toggle button */}
      {isOpen && (
        <div className="px-4 py-3 bg-gray-50/50">
          <BookCommentsSection
            bookId={bookId}
            canComment={canComment}
            userId={userId}
            maxDepth={3}
            isCompact={true}
            onCommentCountChange={onCommentCountChange}
          />
        </div>
      )}
    </div>
  )
}
