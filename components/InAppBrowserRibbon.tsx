"use client"

import { useEffect, useState } from 'react'

function isInAppBrowserUA(ua: string) {
  return /FBAN|FBAV|Instagram|Line|Wechat|Weibo|TikTok/i.test(ua)
}

export function InAppBrowserRibbon() {
  const [visible, setVisible] = useState(false)
  const [notice, setNotice] = useState<string>("")

  useEffect(() => {
    // Avoid SSR: run only on client
    const ua = navigator.userAgent || ''
    const dismissed = sessionStorage.getItem('iab_ribbon_dismissed') === '1'
    if (!dismissed && isInAppBrowserUA(ua)) {
      setVisible(true)
    }
  }, [])

  if (!visible) return null

  const attemptOpenInBrowser = () => {
    try {
      const url = window.location.href
      const ua = navigator.userAgent || ''
      if (/Android/i.test(ua)) {
        const target = 'intent://' + url.replace(/^https?:\/\//, '') + '#Intent;scheme=https;package=com.android.chrome;end'
        window.location.href = target
        setTimeout(() => {
          window.open(url, '_blank', 'noopener,noreferrer')
        }, 400)
      } else {
        const win = window.open(url, '_blank', 'noopener,noreferrer')
        if (!win) {
          setNotice('If it stays inside the app, use ••• or share icon → Open in Browser')
        }
      }
    } catch {
      setNotice('If it stays inside the app, use ••• or share icon → Open in Browser')
    }
  }

  const shareCurrent = async () => {
    try {
      const url = window.location.href
      if (navigator.share) {
        await navigator.share({ title: 'OnlyDiary', text: 'Open in your device browser', url })
      } else {
        await navigator.clipboard.writeText(url)
        setNotice('Link copied — paste into Safari/Chrome')
      }
    } catch {}
  }

  const copyCurrent = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href)
      setNotice('Link copied — open your browser and paste')
    } catch {}
  }

  const dismiss = () => {
    sessionStorage.setItem('iab_ribbon_dismissed', '1')
    setVisible(false)
  }

  return (
    <div className="fixed inset-x-0 bottom-0 z-[9998] px-3 pb-3">
      <div className="mx-auto max-w-2xl rounded-2xl border border-gray-200 bg-white/95 backdrop-blur-md shadow-lg">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between gap-3">
            <div className="text-sm text-gray-800">
              Open this in your device browser to sign in with Google
            </div>
            <button onClick={dismiss} aria-label="Dismiss" className="text-gray-400 hover:text-gray-600">✕</button>
          </div>
          <div className="mt-3 flex items-center gap-2">
            <button onClick={attemptOpenInBrowser} className="px-3 py-1.5 rounded-md bg-gray-900 text-white text-xs">Open in Browser</button>
            <button onClick={shareCurrent} className="px-3 py-1.5 rounded-md border border-gray-300 text-xs bg-white">Share</button>
            <button onClick={copyCurrent} className="px-3 py-1.5 rounded-md border border-gray-300 text-xs bg-white">Copy Link</button>
          </div>
          {notice && <div className="mt-2 text-[11px] text-gray-600">{notice}</div>}
        </div>
      </div>
    </div>
  )
}

