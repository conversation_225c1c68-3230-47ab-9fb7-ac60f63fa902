'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { AudioRecorder } from './AudioRecorder'
import { AudioRepliesList } from './AudioRepliesList'
import { ReactionSystem } from './ReactionSystem'
import { ShareButton } from './ShareButton'
import { FollowButton } from './FollowButton'
import { Button } from './ui/button'
import { Heart, MoreHorizontal } from 'lucide-react'
import { Day1Badge } from './Day1Badge'
import { createSupabaseClient } from '@/lib/supabase/client'

interface AudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  play_count?: number
  created_at: string
  reactions?: Record<string, number>
  userReaction?: string | null
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
}

interface AudioPostProps {
  post: AudioPost
  currentUserId?: string
  isFollowing?: boolean

  onLove?: (postId: string) => void
  onReply?: (postId: string) => void
  onUserClick?: (userId: string) => void
}

export function AudioPost({
  post,
  currentUserId,
  isFollowing = false,

  onLove,
  onReply,
  onUserClick
}: AudioPostProps) {
  const [showReplies, setShowReplies] = useState(false)
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)
  const [reactions, setReactions] = useState(post.reactions || {})
  const [userReaction, setUserReaction] = useState(post.userReaction)
  const [playCount, setPlayCount] = useState(post.play_count || 0)
  const supabase = createSupabaseClient()



  const handleReply = () => {
    setShowReplyRecorder(true)
  }

  const handleReactionUpdate = (newReactions: Record<string, number>, newUserReaction: string | null) => {
    setReactions(newReactions)
    setUserReaction(newUserReaction)
  }

  const handlePlayCountUpdate = (newPlayCount: number) => {
    setPlayCount(newPlayCount)
  }

  const handleAudioReplyComplete = async (audioBlob: Blob, duration: number) => {
    try {
      console.log('Audio reply recorded:', { duration, size: audioBlob.size })

      // Step 1: Get upload URL for the reply
      const uploadResponse = await fetch('/api/audio/replies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parentPostId: post.id,
          duration: Math.round(duration * 10) / 10
        })
      })

      if (!uploadResponse.ok) {
        throw new Error('Failed to get upload URL for reply')
      }

      const { uploadUrl, key, publicUrl } = await uploadResponse.json()

      // Step 2: Upload the audio file to R2
      const uploadResult = await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: {
          'Content-Type': 'audio/webm'
        }
      })

      if (!uploadResult.ok) {
        throw new Error('Failed to upload audio reply')
      }

      // Step 3: Save the reply to the database
      const createResponse = await fetch('/api/audio/replies/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parentPostId: post.id,
          audioUrl: publicUrl,
          audioKey: key,
          duration: Math.round(duration * 10) / 10,
          description: '' // Could add description input later
        })
      })

      if (!createResponse.ok) {
        throw new Error('Failed to save audio reply')
      }

      // Success!
      setShowReplyRecorder(false)

      // Refresh the page to show the new reply with proper user data
      window.location.reload()

    } catch (error) {
      console.error('Error posting audio reply:', error)
      alert('Failed to post audio reply. Please try again.')
    }
  }

  const handleUserClick = () => {
    if (onUserClick) {
      onUserClick(post.user.id)
    }
  }

  const [timeAgo, setTimeAgo] = useState(() => {
    const now = new Date()
    const postDate = new Date(post.created_at)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'now'
    if (diffInMinutes < 60) return `${diffInMinutes}m`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`
    return `${Math.floor(diffInMinutes / 1440)}d`
  })

  // Update time every minute, but only on client side
  useEffect(() => {
    const updateTime = () => {
      const now = new Date()
      const postDate = new Date(post.created_at)
      const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))

      if (diffInMinutes < 1) setTimeAgo('now')
      else if (diffInMinutes < 60) setTimeAgo(`${diffInMinutes}m`)
      else if (diffInMinutes < 1440) setTimeAgo(`${Math.floor(diffInMinutes / 60)}h`)
      else setTimeAgo(`${Math.floor(diffInMinutes / 1440)}d`)
    }

    // Update immediately on client mount to sync with current time
    updateTime()

    // Then update every minute
    const interval = setInterval(updateTime, 60000)
    return () => clearInterval(interval)
  }, [post.created_at])

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 hover:shadow-md transition-shadow duration-200 overflow-hidden">
      {/* Header */}
      <div className="flex items-start gap-2 sm:gap-3 mb-3">
        <button
          onClick={handleUserClick}
          className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-gradient-to-br from-blue-100 via-cyan-50 to-purple-100 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-4 hover:ring-blue-200/50 transition-all cursor-pointer shadow-md hover:shadow-lg hover:scale-105 duration-300"
        >
          {post.user.profile_picture_url || post.user.avatar ? (
            <img
              src={post.user.profile_picture_url || post.user.avatar}
              alt={post.user.name || 'User avatar'}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.style.display = 'none'
              }}
            />
          ) : (
            <span className="text-2xl font-serif text-gray-600">
              {post.user.name?.charAt(0).toUpperCase() || 'U'}
            </span>
          )}
        </button>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <button
              onClick={handleUserClick}
              className="font-bold text-gray-900 hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300 truncate text-base sm:text-lg"
            >
              {post.user.name}
            </button>
            {post.user.has_day1_badge && (
              <Day1Badge
                signupNumber={post.user.signup_number}
                badgeTier={post.user.badge_tier}
                size="sm"
                className="flex-shrink-0"
              />
            )}
            <span className="text-gray-300 text-sm">•</span>
            <span className="text-gray-400 text-xs sm:text-sm font-medium">
              {timeAgo}
            </span>
          </div>

          {/* Audio indicator */}
          <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
            <div className="flex items-center gap-1 bg-gradient-to-r from-blue-100 to-cyan-100 px-2 py-1 rounded-full">
              <span className="text-blue-600 animate-pulse">🎵</span>
              <span className="font-semibold text-blue-700">Audio • {post.duration_seconds.toFixed(1)}s</span>
            </div>

          </div>
        </div>

        {/* Follow Button */}
        {currentUserId && currentUserId !== post.user.id && (
          <div className="flex-shrink-0">
            <FollowButton
              writerId={post.user.id}
              writerName={post.user.name}
              initialIsFollowing={isFollowing}
              size="sm"
            />
          </div>
        )}
      </div>

      {/* Description */}
      {post.description && (
        <div className="mb-3">
          <p className="text-gray-800 text-sm leading-relaxed">
            {post.description}
          </p>
        </div>
      )}

      {/* Audio Player */}
      <div className="mb-3 overflow-hidden">
        <AudioPlayer
          audioUrl={post.audio_url}
          duration={post.duration_seconds}
          className="w-full max-w-full"
          postId={post.id}
          onPlayCountUpdate={handlePlayCountUpdate}
        />
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between mt-3">
        <div className="flex items-center gap-6">
          {/* Reaction System */}
          <ReactionSystem
            contentId={post.id}
            contentType="audio"
            currentUserId={currentUserId}
            initialReactions={reactions}
            userReaction={userReaction}
            onReactionUpdate={handleReactionUpdate}
          />

          <button
            onClick={handleReply}
            disabled={!currentUserId}
            className={`flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors min-h-[44px] ${
              !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <span className="text-sm">🎤</span>
            <span className="text-sm font-medium">Reply</span>
            {post.reply_count > 0 && (
              <span className="text-xs bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-2 py-1 rounded-full font-bold animate-pulse">
                {post.reply_count}
              </span>
            )}
          </button>

          {/* Share Button */}
          <ShareButton
            title={post.description || 'Audio Post'}
            writerName={post.user.name}
            contentType="audio"
            contentId={post.id}
            variant="compact"
          />

          {/* Play Count */}
          {playCount > 0 && (
            <div className="flex items-center gap-1 text-gray-500">
              <span className="text-sm">▶️</span>
              <span className="text-sm font-medium">{playCount.toLocaleString()}</span>
            </div>
          )}
        </div>

        {/* Floating indicator + View replies */}
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowReplies(!showReplies)}
            className="group text-sm text-blue-600 hover:text-blue-700 font-bold transition-all duration-300 hover:scale-105 min-h-[44px] flex items-center gap-2"
          >
            <span>{showReplies ? 'Hide' : 'View'} replies</span>
            {post.reply_count > 0 && (
              <span className="text-xs bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-2 py-1 rounded-full font-bold animate-pulse">
                {post.reply_count}
              </span>
            )}
          </button>

          {/* Subtle floating indicator */}
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
            <div className="w-1 h-1 bg-cyan-300 rounded-full animate-pulse delay-100"></div>
            <div className="w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse delay-200"></div>
          </div>
        </div>
      </div>

      {/* Audio Reply Recorder */}
      {showReplyRecorder && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-blue-900">🎤 Record Audio Reply</h4>
            <button
              onClick={() => setShowReplyRecorder(false)}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Cancel
            </button>
          </div>
          <AudioRecorder
            maxDuration={9}
            onRecordingComplete={handleAudioReplyComplete}
            onCancel={() => setShowReplyRecorder(false)}
          />
        </div>
      )}

      {/* Audio Replies List */}
      {showReplies && (
        <AudioRepliesList
          postId={post.id}
          currentUserId={currentUserId}
          onReplyCountChange={(count) => {
            // Update the reply count in the post
            // This will be reflected in the UI
          }}
        />
      )}
    </div>
  )
}
