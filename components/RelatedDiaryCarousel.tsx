"use client"

import React, { useMemo, useRef } from "react"
import Link from "next/link"
import Image from "next/image"
import { Day1Badge } from "./Day1Badge"

export interface RelatedDiaryEntry {
  id: string
  title: string
  created_at: string
  user: {
    id: string
    name: string
    avatar?: string | null
    profile_picture_url?: string | null
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
}

interface RelatedDiaryCarouselProps {
  entries: RelatedDiaryEntry[]
}

export function RelatedDiaryCarousel({ entries }: RelatedDiaryCarouselProps) {
  if (!entries || entries.length === 0) return null

  const scrollRef = useRef<HTMLDivElement>(null)

  const scrollPage = (dir: -1 | 1) => {
    const el = scrollRef.current
    if (!el) return
    const step = Math.max(el.clientWidth - 40, 240)
    el.scrollBy({ left: dir * step, behavior: "smooth" })
  }

  // Ensure consistent small-section visuals and gradient title
  return (
    <div className="mt-10">
      <div className="mb-3 grid grid-cols-3 items-center">
        <div />
        <h3 className="text-center text-lg font-serif bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
          Diary Entries You May Like
        </h3>
        <div className="flex items-center justify-end gap-2">
          <button
            type="button"
            aria-label="Scroll left"
            onClick={() => scrollPage(-1)}
            className="w-8 h-8 flex items-center justify-center rounded-md border border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
          >
            ←
          </button>
          <button
            type="button"
            aria-label="Scroll right"
            onClick={() => scrollPage(1)}
            className="w-8 h-8 flex items-center justify-center rounded-md border border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
          >
            →
          </button>
        </div>
      </div>

      <div>
        <div ref={scrollRef} className="flex gap-3 overflow-x-auto pb-2 px-1 [-ms-overflow-style:none] [scrollbar-width:none] snap-x snap-mandatory">
          {/* hide scrollbar */}
          <style jsx>{`
            div::-webkit-scrollbar { display: none; }
          `}</style>

          {entries.map((e) => (
            <Link
              key={e.id}
              href={`/d/${e.id}`}
              className="min-w-[240px] max-w-[240px] bg-white border border-gray-100 rounded-xl p-3 hover:shadow-sm transition-all snap-start"
            >
              <div className="flex items-center gap-2 mb-2">
                <div className="w-7 h-7 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                  {(() => {
                    const src = e.user.profile_picture_url || e.user.avatar || ''
                    const isUrl = typeof src === 'string' && (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('/'))
                    if (isUrl) {
                      return (
                        <Image src={src} alt={e.user.name} width={28} height={28} className="w-full h-full object-cover" />
                      )
                    }
                    return null
                  })() || (
                    <div className="w-full h-full bg-gray-300 flex items-center justify-center text-[10px] text-gray-600">
                      {e.user.name?.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-1 min-w-0">
                  <span className="text-xs text-gray-700 truncate max-w-[120px] sm:max-w-[160px]">{e.user.name}</span>
                  {e.user.has_day1_badge && (
                    <Day1Badge signupNumber={e.user.signup_number} badgeTier={e.user.badge_tier} size="sm" clickable={false} className="flex-shrink-0" />
                  )}
                </div>
              </div>

              <div className="font-serif text-sm mb-1 line-clamp-2 min-h-[2.5rem] bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                {e.title}
              </div>
              <div className="mt-2 text-[11px] text-gray-500">{new Date(e.created_at).toLocaleDateString()}</div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}

