'use client'

import React, { useEffect, useState } from 'react'

const ALL = { diary: true, audio: true, book: true, recipe: true, duo: true }

type Enabled = typeof ALL

type Props = {
  onChange?: (enabled: Enabled) => void
}

export function TimelineRealmFilters({ onChange }: Props) {
  const [enabled, setEnabled] = useState<Enabled>(ALL)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    let cancelled = false
    const load = async () => {
      try {
        const res = await fetch('/api/timeline/prefs', { cache: 'no-store' })
        const data = await res.json()
        if (cancelled) return
        if (data?.enabled) {
          const next = { ...ALL, ...data.enabled }
          setEnabled(next)
          onChange?.(next)
        } else {
          setEnabled(ALL)
          onChange?.(ALL)
        }
      } catch (e) {
        setEnabled(ALL)
        onChange?.(ALL)
      } finally {
        if (!cancelled) setLoading(false)
      }
    }
    load()
    return () => { cancelled = true }
  }, [])

  // Single-select with unselect-to-ALL: clicking the same active pill again returns to ALL
  const selectOnly = async (key: keyof Enabled) => {
    const isOnlyThisActive = enabled[key] && Object.entries(enabled).every(([k, v]) => (k === key ? v === true : v === false))
    const next: Enabled = isOnlyThisActive
      ? { ...ALL }
      : { diary: false, audio: false, book: false, recipe: false, duo: false, [key]: true } as Enabled

    setEnabled(next)
    onChange?.(next)
    setSaving(true)
    try {
      await fetch('/api/timeline/prefs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: next })
      })
    } finally {
      setSaving(false)
    }
  }

  const Pill = ({ active, children, onClick }: { active: boolean; children: React.ReactNode; onClick: () => void }) => (
    <button
      onClick={onClick}
      className={`px-3 py-1.5 rounded-full text-xs font-medium border transition-colors ${
        active ? 'bg-black text-white border-black' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
      }`}
      aria-pressed={active}
    >
      {children}
    </button>
  )

  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <div className="w-3 h-3 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
        <span className="text-xs text-gray-600">Loading filters…</span>
      </div>
    )
  }

  return (
    <div className="flex items-center flex-wrap gap-2">
      <Pill active={enabled.diary} onClick={() => selectOnly('diary')}>Diary</Pill>
      <Pill active={enabled.audio} onClick={() => selectOnly('audio')}>Audio</Pill>
      <Pill active={enabled.book} onClick={() => selectOnly('book')}>Books</Pill>
      <Pill active={enabled.recipe} onClick={() => selectOnly('recipe')}>Recipes</Pill>
      <Pill active={enabled.duo} onClick={() => selectOnly('duo')}>OnlyDuos</Pill>
      {saving && <span className="text-xs text-gray-500">Saving…</span>}
    </div>
  )
}

