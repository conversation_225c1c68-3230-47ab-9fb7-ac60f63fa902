"use client"

import React from "react"

interface AuthorProfileLinkProps {
  userId?: string
  userName?: string | null
  className?: string
}

// Renders a clickable author name that navigates to the user's profile.
// Designed to be used inside a parent Link without causing nested anchor issues.
export function AuthorProfileLink({ userId, userName, className }: AuthorProfileLinkProps) {
  if (!userId || !userName) {
    return <span className={className}>{userName || "Anonymous"}</span>
  }

  const handleClick: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    // Prevent parent Link (e.g., book card) from handling the click
    e.stopPropagation()
    // Navigate to profile
    window.location.href = `/u/${userId}`
  }

  return (
    <button
      type="button"
      onClick={handleClick}
      className={className ? `${className} hover:text-blue-600 transition-colors` : "hover:text-blue-600 transition-colors"}
    >
      {userName}
    </button>
  )
}

