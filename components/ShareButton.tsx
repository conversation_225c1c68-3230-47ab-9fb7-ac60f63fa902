"use client"

import { useState, useRef, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface ShareButtonProps {
  title: string
  writerName: string
  url?: string
  contentType: 'diary' | 'audio' | 'book' | 'recipe'
  contentId: string
  variant?: 'default' | 'compact' | 'icon-only'
  className?: string
}

const SOCIAL_PLATFORMS = [
  {
    id: 'twitter',
    name: 'Twitter',
    icon: '𝕏',
    color: 'hover:bg-black hover:text-white',
    getUrl: (text: string, url: string) =>
      `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: '📘',
    color: 'hover:bg-blue-600 hover:text-white',
    getUrl: (text: string, url: string) =>
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: '💼',
    color: 'hover:bg-blue-700 hover:text-white',
    getUrl: (text: string, url: string) =>
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`
  },
  {
    id: 'reddit',
    name: 'Reddit',
    icon: '🔴',
    color: 'hover:bg-orange-600 hover:text-white',
    getUrl: (text: string, url: string) =>
      `https://reddit.com/submit?title=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`
  },
  {
    id: 'email',
    name: 'Email',
    icon: '📧',
    color: 'hover:bg-gray-600 hover:text-white',
    getUrl: (text: string, url: string) =>
      `mailto:?subject=${encodeURIComponent(text)}&body=${encodeURIComponent(`${text}\n\n${url}`)}`
  }
]

export function ShareButton({
  title,
  writerName,
  url,
  contentType,
  contentId,
  variant = 'default',
  className = ""
}: ShareButtonProps) {
  const [isSharing, setIsSharing] = useState(false)
  const [showMenu, setShowMenu] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const supabase = createSupabaseClient()

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false)
      }
    }

    if (typeof document !== 'undefined') {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const shareUrl = url || (typeof window !== 'undefined' ? window.location.href : '')
  const shareText = `Read "${title}" by ${writerName} on OnlyDiary`

  const trackShare = async (platform: string) => {
    try {
      await fetch('/api/engagement/share', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contentType,
          contentId,
          platform
        })
      })
    } catch (error) {
      console.error('Failed to track share:', error)
    }
  }

  const handleNativeShare = async () => {
    setIsSharing(true)

    const shareData = {
      title: `${title} - OnlyDiary`,
      text: shareText,
      url: shareUrl
    }

    try {
      if (typeof navigator !== 'undefined' && typeof (navigator as any).share === 'function') {
        await navigator.share(shareData)
        await trackShare('native')
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(shareUrl)
        await trackShare('copy')
        alert('Link copied to clipboard!')
      }
    } catch (error) {
      console.log('Share cancelled or failed')
    } finally {
      setIsSharing(false)
      setShowMenu(false)
    }
  }

  const handlePlatformShare = async (platform: typeof SOCIAL_PLATFORMS[0]) => {
    try {
      const shareLink = platform.getUrl(shareText, shareUrl)

      if (typeof window !== 'undefined') {
        if (platform.id === 'email') {
          window.location.href = shareLink
        } else {
          window.open(shareLink, '_blank', 'width=600,height=400')
        }
      }

      await trackShare(platform.id)
    } catch (error) {
      console.error('Failed to share:', error)
    } finally {
      setShowMenu(false)
    }
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      await trackShare('copy')
      alert('Link copied to clipboard!')
    } catch (error) {
      // Fallback for older browsers
      if (typeof document !== 'undefined') {
        const textArea = document.createElement('textarea')
        textArea.value = shareUrl
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        await trackShare('copy')
        alert('Link copied to clipboard!')
      }
    } finally {
      setShowMenu(false)
    }
  }

  // Compact variant for timeline/cards
  if (variant === 'compact') {
    return (
      <div className="relative" ref={menuRef}>
        <button
          onClick={() => setShowMenu(!showMenu)}
          className={`flex items-center gap-1 px-2 py-1 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded transition-colors text-sm ${className}`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
          <span>Share</span>
        </button>

        {showMenu && (
          <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-50 min-w-[200px]">
            <div className="grid grid-cols-2 gap-1">
              {SOCIAL_PLATFORMS.map((platform) => (
                <button
                  key={platform.id}
                  onClick={() => handlePlatformShare(platform)}
                  className={`flex items-center gap-2 p-2 rounded text-sm transition-colors ${platform.color}`}
                >
                  <span>{platform.icon}</span>
                  <span>{platform.name}</span>
                </button>
              ))}
            </div>
            <div className="border-t border-gray-200 mt-2 pt-2">
              <button
                onClick={handleCopyLink}
                className="w-full flex items-center gap-2 p-2 rounded text-sm hover:bg-gray-100 transition-colors"
              >
                <span>🔗</span>
                <span>Copy Link</span>
              </button>
              {typeof navigator !== 'undefined' && typeof (navigator as any).share === 'function' && (
                <button
                  onClick={handleNativeShare}
                  disabled={isSharing}
                  className="w-full flex items-center gap-2 p-2 rounded text-sm hover:bg-gray-100 transition-colors disabled:opacity-50"
                >
                  <span>📱</span>
                  <span>{isSharing ? 'Sharing...' : 'More Options'}</span>
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    )
  }

  // Icon-only variant for minimal UI
  if (variant === 'icon-only') {
    return (
      <div className="relative" ref={menuRef}>
        <button
          onClick={() => setShowMenu(!showMenu)}
          className={`p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors ${className}`}
          title="Share"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
        </button>

        {showMenu && (
          <div className="absolute bottom-full right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-50 min-w-[180px]">
            <div className="space-y-1">
              {SOCIAL_PLATFORMS.map((platform) => (
                <button
                  key={platform.id}
                  onClick={() => handlePlatformShare(platform)}
                  className={`w-full flex items-center gap-2 p-2 rounded text-sm transition-colors ${platform.color}`}
                >
                  <span>{platform.icon}</span>
                  <span>{platform.name}</span>
                </button>
              ))}
              <div className="border-t border-gray-200 my-1"></div>
              <button
                onClick={handleCopyLink}
                className="w-full flex items-center gap-2 p-2 rounded text-sm hover:bg-gray-100 transition-colors"
              >
                <span>🔗</span>
                <span>Copy Link</span>
              </button>
            </div>
          </div>
        )}
      </div>
    )
  }

  // Default variant (original style)
  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setShowMenu(!showMenu)}
        disabled={isSharing}
        className={`flex items-center gap-2 px-4 py-2 text-gray-700 hover:text-gray-900 bg-white hover:bg-gray-100 border border-gray-200 rounded-lg transition-colors disabled:opacity-50 ${className}`}
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
          />
        </svg>
        <span className="font-semibold text-gray-700">
          {isSharing ? 'Sharing...' : 'Share'}
        </span>
      </button>

      {showMenu && (
        <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-50 min-w-[250px]">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Share this {contentType}</h3>

          <div className="grid grid-cols-2 gap-2 mb-3">
            {SOCIAL_PLATFORMS.map((platform) => (
              <button
                key={platform.id}
                onClick={() => handlePlatformShare(platform)}
                className={`flex items-center gap-2 p-2 rounded text-sm transition-colors ${platform.color} border border-gray-200`}
              >
                <span>{platform.icon}</span>
                <span>{platform.name}</span>
              </button>
            ))}
          </div>

          <div className="border-t border-gray-200 pt-2 space-y-1">
            <button
              onClick={handleCopyLink}
              className="w-full flex items-center gap-2 p-2 rounded text-sm hover:bg-gray-100 transition-colors"
            >
              <span>🔗</span>
              <span>Copy Link</span>
            </button>

            {typeof navigator !== 'undefined' && typeof (navigator as any).share === 'function' && (
              <button
                onClick={handleNativeShare}
                disabled={isSharing}
                className="w-full flex items-center gap-2 p-2 rounded text-sm hover:bg-gray-100 transition-colors disabled:opacity-50"
              >
                <span>📱</span>
                <span>{isSharing ? 'Sharing...' : 'More Options'}</span>
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
