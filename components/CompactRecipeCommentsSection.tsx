"use client"

import { useState } from "react"
import { RecipeCommentsSection } from "./RecipeCommentsSection"

interface CompactRecipeCommentsSectionProps {
  recipeId: string
  canComment: boolean
  userId?: string
  isOpen: boolean
  onToggle: () => void
  commentCount?: number
}

export function CompactRecipeCommentsSection({
  recipeId,
  canComment,
  userId,
  isOpen,
  onToggle,
  commentCount = 0,
}: CompactRecipeCommentsSectionProps) {
  return (
    <div className="border-t border-gray-100">
      {/* Comments Section - No redundant toggle button */}
      {isOpen && (
        <div className="px-4 py-3 bg-gray-50/50">
          <RecipeCommentsSection
            recipeId={recipeId}
            canComment={canComment}
            userId={userId}
            maxDepth={3}
            isCompact={true}
          />
        </div>
      )}
    </div>
  )
}

