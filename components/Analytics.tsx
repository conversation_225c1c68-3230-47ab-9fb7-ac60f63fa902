'use client'

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

// Google Analytics
export function GoogleAnalytics({ gaId }: { gaId: string }) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!gaId) return

    // Load Google Analytics
    const script1 = document.createElement('script')
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`
    script1.async = true
    document.head.appendChild(script1)

    const script2 = document.createElement('script')
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        page_title: document.title,
        page_location: window.location.href,
      });
    `
    document.head.appendChild(script2)

    return () => {
      document.head.removeChild(script1)
      document.head.removeChild(script2)
    }
  }, [gaId])

  useEffect(() => {
    if (!gaId) return

    const url = pathname + searchParams.toString()

    // Track page views
    if (typeof window !== 'undefined' && (window as { gtag?: (...args: unknown[]) => void }).gtag) {
      (window as { gtag: (...args: unknown[]) => void }).gtag('config', gaId, {
        page_path: url,
      })
    }
  }, [pathname, searchParams, gaId])

  return null
}

// Facebook Pixel
export function FacebookPixel({ pixelId }: { pixelId: string }) {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (!pixelId) return

    // Load Facebook Pixel base code if not already present
    if (!(window as any).fbq) {
      !(function (f: any, b: any, e: any, v: any, n?: any, t?: any, s?: any) {
        if (f.fbq) return
        n = f.fbq = function () {
          // eslint-disable-next-line prefer-rest-params
          ;(n as any).callMethod ? (n as any).callMethod.apply(n, arguments) : (n as any).queue.push(arguments)
        }
        if (!f._fbq) f._fbq = n
        ;(n as any).push = (n as any)
        ;(n as any).loaded = true
        ;(n as any).version = '2.0'
        ;(n as any).queue = []
        t = b.createElement(e)
        t.async = true
        t.src = 'https://connect.facebook.net/en_US/fbevents.js'
        s = b.getElementsByTagName(e)[0]
        s.parentNode && s.parentNode.insertBefore(t, s)
      })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js')
    }

    try {
      ;(window as any).fbq('init', pixelId)
      ;(window as any).fbq('consent', 'grant')
      ;(window as any).fbq('track', 'PageView')
    } catch (e) {
      console.warn('FB Pixel init error', e)
    }
  }, [pixelId])

  useEffect(() => {
    if (!pixelId) return
    try {
      ;(window as any).fbq && (window as any).fbq('track', 'PageView')
    } catch {}
  }, [pathname, searchParams, pixelId])

  return null
}

// Performance monitoring
export function PerformanceMonitor() {
  useEffect(() => {
    // Core Web Vitals monitoring
    if (typeof window !== 'undefined' && 'performance' in window) {
      // Monitor Largest Contentful Paint (LCP)
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            console.log('LCP:', entry.startTime)
            // Send to analytics
            if ((window as Window & { gtag?: (...args: unknown[]) => void }).gtag) {
              (window as Window & { gtag?: (...args: unknown[]) => void }).gtag!('event', 'web_vitals', {
                name: 'LCP',
                value: Math.round(entry.startTime),
                event_category: 'Web Vitals',
              })
            }
          }
        }
      })

      observer.observe({ entryTypes: ['largest-contentful-paint'] })

      // Monitor Cumulative Layout Shift (CLS)
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as PerformanceEntry & { hadRecentInput?: boolean }).hadRecentInput) {
            clsValue += (entry as PerformanceEntry & { value: number }).value
          }
        }
      })

      clsObserver.observe({ entryTypes: ['layout-shift'] })

      // Send CLS on page unload
      window.addEventListener('beforeunload', () => {
        if ((window as any).gtag) {
          (window as any).gtag('event', 'web_vitals', {
            name: 'CLS',
            value: Math.round(clsValue * 1000),
            event_category: 'Web Vitals',
          })
        }
      })

      return () => {
        observer.disconnect()
        clsObserver.disconnect()
      }
    }
  }, [])

  return null
}

// Custom event tracking
export function trackEvent(eventName: string, parameters?: Record<string, any>) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', eventName, parameters)
  }
}
