// Minimal type shim to satisfy TypeScript when react-dropzone types are unavailable.
// If the project installs @types/react-dropzone or the package provides its own types,
// this shim can be removed.
declare module 'react-dropzone' {
  // Basic shape covering the usage in BookUploadWizard
  export interface DropzoneOptions {
    onDrop?: (acceptedFiles: File[], fileRejections?: any, event?: any) => void;
    accept?: Record<string, string[]>;
    maxFiles?: number;
  }

  export function useDropzone(options?: DropzoneOptions): {
    getRootProps: (props?: any) => any;
    getInputProps: (props?: any) => any;
    isDragActive: boolean;
    acceptedFiles: File[];
    fileRejections: any[];
    rootRef: any;
    inputRef: any;
    open: () => void;
  };
}
