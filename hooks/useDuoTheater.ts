"use client"

import { useState, useEffect } from 'react'

interface Duo {
  id: string
  initiator: { name: string; profile_picture_url?: string }
  responder: { name: string; profile_picture_url?: string }
  created_at: string
  final_video_url?: string
  final_video_r2_key?: string
  partA: { src_r2_key: string; hls_manifest?: string }
  partB: { src_r2_key: string; hls_manifest?: string }
  reactions_count: number
  comments_count: number
  downloads_count: number
  popularity_score?: number
}

export function useDuoTheater() {
  const [isOpen, setIsOpen] = useState(false)
  const [duos, setDuos] = useState<Duo[]>([])
  const [loading, setLoading] = useState(false)
  const [initialDuoId, setInitialDuoId] = useState<string | undefined>()

  // Fetch all published duos sorted by popularity
  const fetchDuos = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/duos/timeline')
      if (response.ok) {
        const data = await response.json()
        // Sort by popularity (reactions + comments + downloads)
        const sortedDuos = data.duos.sort((a: Duo, b: Duo) => {
          const scoreA = (a.reactions_count || 0) + (a.comments_count || 0) + (a.downloads_count || 0)
          const scoreB = (b.reactions_count || 0) + (b.comments_count || 0) + (b.downloads_count || 0)
          return scoreB - scoreA
        })
        setDuos(sortedDuos)
      }
    } catch (error) {
      console.error('Failed to fetch duos:', error)
    } finally {
      setLoading(false)
    }
  }

  // Open theater with specific duo
  const openTheater = (duoId?: string) => {
    setInitialDuoId(duoId)
    setIsOpen(true)
    if (duos.length === 0) {
      fetchDuos()
    }
  }

  // Close theater
  const closeTheater = () => {
    setIsOpen(false)
    setInitialDuoId(undefined)
  }

  // Handle reactions
  const handleReact = async (duoId: string) => {
    try {
      const response = await fetch('/api/duos/react', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoId })
      })
      
      if (response.ok) {
        // Update local state
        setDuos(prev => prev.map(duo => 
          duo.id === duoId 
            ? { ...duo, reactions_count: duo.reactions_count + 1 }
            : duo
        ))
      }
    } catch (error) {
      console.error('Failed to react:', error)
    }
  }

  // Handle comments
  const handleComment = async (duoId: string, comment: string) => {
    try {
      const response = await fetch('/api/duos/comment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoId, comment })
      })
      
      if (response.ok) {
        // Update local state
        setDuos(prev => prev.map(duo => 
          duo.id === duoId 
            ? { ...duo, comments_count: duo.comments_count + 1 }
            : duo
        ))
      }
    } catch (error) {
      console.error('Failed to comment:', error)
    }
  }

  // Handle downloads
  const handleDownload = async (duoId: string) => {
    try {
      const response = await fetch('/api/duo/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duoPostId: duoId,
          includeWatermark: true,
          includeAttribution: true
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `onlyduo-${duoId}.mp4`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        // Update local state
        setDuos(prev => prev.map(duo => 
          duo.id === duoId 
            ? { ...duo, downloads_count: duo.downloads_count + 1 }
            : duo
        ))
      }
    } catch (error) {
      console.error('Failed to download:', error)
    }
  }

  return {
    isOpen,
    duos,
    loading,
    initialDuoId,
    openTheater,
    closeTheater,
    handleReact,
    handleComment,
    handleDownload,
    fetchDuos
  }
}
