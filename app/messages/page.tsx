'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { MessageThreadModal } from '@/components/MessageThreadModal'
import Image from 'next/image'
import { AuthorProfileLink } from '@/components/AuthorProfileLink'

import Link from 'next/link'

import { User, Search, MessageCircle } from 'lucide-react'
import { usePathname } from 'next/navigation'
import { REALTIME_ENABLED } from '@/lib/config'

interface Conversation {
  otherUser: {
    id: string
    name: string
    profile_picture_url?: string
    avatar?: string
  }
  lastMessage: {
    id: string
    body: string
    photo_url?: string
    message_type: string
    created_at: string
    sender_id: string
  }
  unreadCount: number
  totalMessages: number
}

export default function MessagesPage() {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null)
  const [messageThreadOpen, setMessageThreadOpen] = useState(false)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const pathname = usePathname()
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchCurrentUser()
  }, [])

  useEffect(() => {
    if (currentUserId) {
      fetchConversations()
    }
  }, [currentUserId])

  // Real-time subscriptions for new messages
  useEffect(() => {
    if (!currentUserId) return

    // Check if realtime is disabled globally
    if (!REALTIME_ENABLED) {
      console.log('🚫 Messages real-time disabled via config')
      return
    }

    // Only enable real-time on the actual messages page, not on timeline
    if (pathname !== '/messages') {
      console.log('🔔 Messages real-time disabled outside messages page for performance')
      return
    }

    const channel = supabase
      .channel('messages')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'direct_messages',
        filter: `or(sender_id.eq.${currentUserId},recipient_id.eq.${currentUserId})`
      }, () => {
        fetchConversations()
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [currentUserId, pathname])

  const fetchCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (user) {
      setCurrentUserId(user.id)
    }
  }

  const fetchConversations = async () => {
    if (!currentUserId) return

    try {
      // Get all messages involving the current user
      const { data: messages, error } = await supabase
        .from('direct_messages')
        .select(`
          id,
          sender_id,
          recipient_id,
          body,
          photo_url,
          message_type,
          created_at,
          read_at,
          sender:users!sender_id(id, name, profile_picture_url, avatar),
          recipient:users!recipient_id(id, name, profile_picture_url, avatar)
        `)
        .or(`sender_id.eq.${currentUserId},recipient_id.eq.${currentUserId}`)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Group messages by conversation (other user)
      const conversationMap = new Map<string, Conversation>()

      messages?.forEach(message => {
        const isFromMe = message.sender_id === currentUserId
        const otherUser = isFromMe ? message.recipient : message.sender
        const otherUserId = otherUser.id

        if (!conversationMap.has(otherUserId)) {
          conversationMap.set(otherUserId, {
            otherUser,
            lastMessage: message,
            unreadCount: 0,
            totalMessages: 0
          })
        }

        const conversation = conversationMap.get(otherUserId)!
        conversation.totalMessages++

        // Count unread messages (messages from other user that haven't been read)
        if (!isFromMe && !message.read_at) {
          conversation.unreadCount++
        }

        // Update last message if this one is more recent
        if (new Date(message.created_at) > new Date(conversation.lastMessage.created_at)) {
          conversation.lastMessage = message
        }
      })

      setConversations(Array.from(conversationMap.values()))
    } catch (error) {
      console.error('Error fetching conversations:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  const getMessagePreview = (message: Conversation['lastMessage']) => {
    if (message.message_type === 'photo') return '📷 Photo'
    if (message.message_type === 'text_with_photo') return `📷 ${message.body?.substring(0, 30)}...`
    return message.body?.substring(0, 50) + (message.body && message.body.length > 50 ? '...' : '')
  }

  const filteredConversations = conversations.filter(conv =>
    conv.otherUser.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleConversationClick = (conversation: Conversation) => {
    setSelectedConversation(conversation)
    setMessageThreadOpen(true)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <MessageCircle className="w-6 h-6 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
            </div>
            <div className="text-sm text-gray-500">
              {conversations.length} conversation{conversations.length !== 1 ? 's' : ''}
            </div>
          </div>

          {/* Search */}
          <div className="mt-4 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredConversations.length > 0 ? (
          <div className="space-y-2">
            {filteredConversations.map((conversation) => (
              <div
                key={conversation.otherUser.id}
                onClick={() => handleConversationClick(conversation)}
                className="bg-white rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all cursor-pointer"
              >
                <div className="flex items-center gap-4">
                  {/* Avatar */}
                  {conversation.otherUser.profile_picture_url || conversation.otherUser.avatar ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        window.location.href = `/u/${conversation.otherUser.id}`
                      }}
                      className="w-12 h-12 rounded-full overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-300"
                      aria-label={`View ${conversation.otherUser.name}'s profile`}
                    >
                      <Image
                        src={conversation.otherUser.profile_picture_url || conversation.otherUser.avatar || ''}
                        alt={conversation.otherUser.name}
                        width={48}
                        height={48}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    </button>
                  ) : (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        window.location.href = `/u/${conversation.otherUser.id}`
                      }}
                      className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center text-white font-medium focus:outline-none focus:ring-2 focus:ring-blue-300"
                      aria-label={`View ${conversation.otherUser.name}'s profile`}
                    >
                      <User className="w-6 h-6" />
                    </button>
                  )}

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-semibold text-gray-900 truncate">
                        <AuthorProfileLink userId={conversation.otherUser.id} userName={conversation.otherUser.name} />
                      </h3>
                      <div className="flex items-center gap-2">
                        {conversation.unreadCount > 0 && (
                          <span className="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[20px] text-center">
                            {conversation.unreadCount}
                          </span>
                        )}
                        <span className="text-xs text-gray-500">
                          {formatTimeAgo(conversation.lastMessage.created_at)}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <p className={`text-sm truncate flex-1 ${
                        conversation.unreadCount > 0 ? 'font-medium text-gray-900' : 'text-gray-600'
                      }`}>
                        {conversation.lastMessage.sender_id === currentUserId && (
                          <span className="text-gray-500">You: </span>
                        )}
                        {getMessagePreview(conversation.lastMessage)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No conversations found' : 'No messages yet'}
            </h3>
            <p className="text-gray-500">
              {searchQuery
                ? 'Try searching for a different name'
                : 'Start a conversation by visiting someone\'s profile and sending them a message'
              }
            </p>
          </div>
        )}
      </div>

      {/* Message Thread Modal */}
      {selectedConversation && (
        <MessageThreadModal
          isOpen={messageThreadOpen}
          onClose={() => {
            setMessageThreadOpen(false)
            setSelectedConversation(null)
            // Refresh conversations to update read status and new messages
            fetchConversations()
          }}
          otherUserId={selectedConversation.otherUser.id}
          otherUserName={selectedConversation.otherUser.name}
          otherUserAvatar={selectedConversation.otherUser.profile_picture_url || selectedConversation.otherUser.avatar}
          currentUserId={currentUserId || ''}
        />
      )}
    </div>
  )
}
