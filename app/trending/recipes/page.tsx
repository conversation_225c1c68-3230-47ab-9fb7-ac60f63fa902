import { createSupabaseServerClient } from "@/lib/supabase/client"
import Link from "next/link"
import Image from "next/image"
import { GlobalSearchBar } from "@/components/GlobalSearchBar"
import { AuthorProfileLink } from "@/components/AuthorProfileLink"

import { Day1Badge } from '@/components/Day1Badge'
import { CompactRecipeCommentsSection } from '@/components/CompactRecipeCommentsSection'
import { TrendingRecipeActions } from '@/components/TrendingRecipeActions'

interface TopRecipe {
  recipe_id: string
  title: string
  writer_name: string
  writer_id: string
  writer_avatar: string | null
  hourly_cooks: number
  writer_has_day1_badge?: boolean
  writer_signup_number?: number
  writer_badge_tier?: string
  reactions?: Record<string, number>
  userReaction?: string | null

  total_cooks: number
  cover_photo_url: string | null
  created_at: string
  is_free: boolean
  price_cents: number | null
}

function formatTimeAgo(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return "Just now"
  if (diffInHours === 1) return "1 hour ago"
  if (diffInHours < 24) return `${diffInHours} hours ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays === 1) return "1 day ago"
  return `${diffInDays} days ago`
}

export default async function TrendingRecipesPage() {
  const supabase = await createSupabaseServerClient()

  // Recent recipes in the last 7 days
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
  const { data: recentRecipes } = await supabase
    .from('recipes' as any)
    .select(`
      id,
      title,
      description,
      created_at,
      is_free,
      price_cents,
      cover_photo_url,
      cooked_count,
      users!inner(
        id,
        name,
        profile_picture_url,
        has_day1_badge,
        signup_number,
        badge_tier
      )
    `)
    .gte('created_at', oneWeekAgo)
    .order('created_at', { ascending: false })
    .limit(50)

  if (!recentRecipes || recentRecipes.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">🍳 Trending Recipes</h1>
            <p className="text-gray-600">No new recipes in the past week</p>
          </div>
        </div>
      </div>
    )
  }

  // Gather cook events
  const recipeIds = recentRecipes.map(r => r.id)

  const { data: cookEvents } = await supabase
    .from('recipe_cook_events' as any)
    .select('recipe_id, created_at')
    .in('recipe_id', recipeIds)

  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
  const { data: hourlyCookEvents } = await supabase
    .from('recipe_cook_events' as any)
    .select('recipe_id')
    .in('recipe_id', recipeIds)
    .gte('created_at', oneHourAgo)

  const totalCooksMap = (cookEvents || []).reduce((acc: Record<string, number>, e: any) => {
    acc[e.recipe_id] = (acc[e.recipe_id] || 0) + 1
    return acc
  }, {})

  const hourlyCooksMap = (hourlyCookEvents || []).reduce((acc: Record<string, number>, e: any) => {
    acc[e.recipe_id] = (acc[e.recipe_id] || 0) + 1
    return acc
  }, {})

  // Build final list and sort by total cooks desc; fallback to created_at if no cooks at all
  let recipes: TopRecipe[] = recentRecipes.map((r: any) => ({
    recipe_id: r.id,
    title: r.title,
    writer_name: r.users?.[0]?.name || r.users?.name,
    writer_id: r.users?.[0]?.id || r.users?.id,
    writer_avatar: r.users?.[0]?.profile_picture_url || r.users?.profile_picture_url,
    writer_has_day1_badge: r.users?.[0]?.has_day1_badge || r.users?.has_day1_badge,
    writer_signup_number: r.users?.[0]?.signup_number || r.users?.signup_number,
    writer_badge_tier: r.users?.[0]?.badge_tier || r.users?.badge_tier,
    hourly_cooks: hourlyCooksMap[r.id] || 0,
    total_cooks: totalCooksMap[r.id] || 0,
    cover_photo_url: r.cover_photo_url || null,
    created_at: r.created_at,
    is_free: !!r.is_free,
    price_cents: r.price_cents ?? null,
  }))

  // Fetch reactions for these recipes and current user's reaction
  let reactionsMap: Record<string, Record<string, number>> = {}
  let userReactionsMap: Record<string, string> = {}
  const supabaseClient = createSupabaseClient()
  const { data: reacts } = await supabaseClient
    .from('reactions')
    .select('recipe_id, reaction_type, user_id')
    .in('recipe_id', recipeIds)
  const { data: auth } = await supabase.auth.getUser()
  const userId = auth.user?.id
  reacts?.forEach((r: any) => {
    if (!reactionsMap[r.recipe_id]) reactionsMap[r.recipe_id] = {}
    reactionsMap[r.recipe_id][r.reaction_type] = (reactionsMap[r.recipe_id][r.reaction_type] || 0) + 1
    if (userId && r.user_id === userId) userReactionsMap[r.recipe_id] = r.reaction_type
  })

  recipes = recipes.map(r => ({
    ...r,
    reactions: reactionsMap[r.recipe_id] || {},
    userReaction: userReactionsMap[r.recipe_id] || null,
  }))

  const hasAnyCooks = recipes.some(r => r.total_cooks > 0)
  if (hasAnyCooks) {
    recipes = recipes
      .sort((a, b) => b.total_cooks - a.total_cooks || b.hourly_cooks - a.hourly_cooks)
      .slice(0, 20)
  } else {
    // If no cook events yet, show newest recipes so creators still see their work
    recipes = recipes
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 20)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-6">
        {/* Global Search */}
        <div className="mt-4">
          <GlobalSearchBar />
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🍳 Trending Recipes</h1>
          <p className="text-gray-600">The most cooked and talked about recipes from the past week</p>
          <p className="text-sm text-gray-500 mt-1 italic">Rankings update automatically every hour</p>

          {/* Navigation to other trending pages */}
          <div className="mt-4 flex justify-center gap-4">
            <Link href="/trending" className="text-gray-700 hover:text-gray-900 font-medium text-sm">📝 Trending Diaries</Link>
            <Link href="/trending/audio" className="text-purple-600 hover:text-purple-700 font-medium text-sm">🎙️ Trending Audio Posts</Link>
          </div>
        </div>

        {/* Top Recipes Grid */}
        {recipes.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <p className="text-gray-500">No trending recipes right now</p>
            <p className="text-sm text-gray-400 mt-2">Check back later or be the first to cook a recipe!</p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {recipes.map((r, index) => (
              <div key={r.recipe_id} className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                {/* Ranking Badge and Photo */}
                <div className="relative">
                  <div className={`absolute top-3 left-3 z-10 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-orange-500' :
                    'bg-gray-600'
                  }`}>
                    #{index + 1}
                  </div>

                  {/* Photo or Placeholder linking to recipe */}
                  <Link href={`/recipes/${r.recipe_id}`} className="block">
                    {r.cover_photo_url ? (
                      <Image
                        src={r.cover_photo_url}
                        alt={r.title}
                        width={400}
                        height={192}
                        className="w-full h-48 object-cover"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                        <span className="text-4xl text-gray-400">🍽️</span>
                      </div>
                    )}
                  </Link>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="font-bold text-gray-900 mb-2 line-clamp-2">
                    <Link href={`/recipes/${r.recipe_id}`} className="hover:underline">
                      {r.title}
                    </Link>
                  </h3>

	                  <TrendingRecipeActions
	                    recipeId={r.recipe_id}
	                    reactions={r.reactions}
	                    userReaction={r.userReaction}
	                  />


                  {/* Writer Info */}
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                      {r.writer_avatar ? (
                        <Link
                          href={`/u/${r.writer_id}`}
                          className="block w-full h-full focus:outline-none focus:ring-2 focus:ring-blue-300 rounded-full"
                          aria-label={`View ${r.writer_name}'s profile`}
                        >
                          <Image
                            src={r.writer_avatar}
                            alt={r.writer_name}
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        </Link>
                      ) : (
                        <Link
                          href={`/u/${r.writer_id}`}
                          className="w-full h-full bg-gray-300 flex items-center justify-center text-xs text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-300 rounded-full"
                          aria-label={`View ${r.writer_name}'s profile`}
                        >
                          {r.writer_name.charAt(0).toUpperCase()}
                        </Link>
                      )}
                    </div>
                    <div className="min-w-0">

	                      {/* Badge next to name if Day 1 */}
	                      <div className="flex items-center gap-2">
	                        <Link href={`/u/${r.writer_id}`} className="hover:text-blue-600 text-sm font-medium text-gray-900 truncate">
	                          {r.writer_name}
	                        </Link>
	                        {r.writer_has_day1_badge && (
	                          <Day1Badge signupNumber={r.writer_signup_number} badgeTier={r.writer_badge_tier} size="xs" />
	                        )}
	                      </div>

                      <p className="text-sm font-medium text-gray-900 truncate">
                        <Link href={`/u/${r.writer_id}`} className="hover:text-blue-600">
                          {r.writer_name}
                        </Link>
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatTimeAgo(r.created_at)}
                        {typeof r.price_cents === 'number' && (
                          <span className="ml-2 text-gray-600">{r.is_free ? '• Free' : `• $${(r.price_cents / 100).toFixed(2)}`}</span>
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>🍳 {r.total_cooks} cooks</span>
                    {r.hourly_cooks > 0 && <span className="text-purple-600">+{r.hourly_cooks} in last hour</span>}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

