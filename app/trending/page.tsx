import { createSupabaseServerClient } from "@/lib/supabase/client"

import { GlobalSearchBar } from '@/components/GlobalSearchBar'
import Link from "next/link"
import Image from "next/image"
import { AuthorProfileLink } from "@/components/AuthorProfileLink"

interface TopDiaryEntry {
  entry_id: string
  title: string
  writer_name: string
  writer_id: string
  writer_avatar: string | null
  hourly_reactions: number
  total_reactions: number
  first_photo_url: string | null
  video_url: string | null
  video_title: string | null
  created_at: string
  is_free: boolean
}

function formatTimeAgo(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return "Just now"
  if (diffInHours === 1) return "1 hour ago"
  if (diffInHours < 24) return `${diffInHours} hours ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays === 1) return "1 day ago"
  return `${diffInDays} days ago`
}

export default async function TrendingPage({ searchParams }: { searchParams?: Promise<{ search?: string }> }) {
  const supabase = await createSupabaseServerClient()

  // Get recent entries with videos
  const { data: recentEntries } = await supabase
    .from('diary_entries')
    .select(`
      id,
      title,
      created_at,
      is_free,
      users!inner (
        id,
        name,
        profile_picture_url
      ),
      photos (
        url
      ),
      videos (
        r2_public_url,
        title
      )
    `)
    .eq('is_hidden', false)
    .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
    .order('created_at', { ascending: false })
    .limit(50) // Get more entries to calculate reactions for

  // Optional: filter by search query (title contains)
  const sp = (await searchParams) || {}
  const searchQ = sp.search?.trim() || ''
  const filteredEntries = searchQ
    ? (recentEntries || []).filter((e: any) => (e.title || '').toLowerCase().includes(searchQ.toLowerCase()))
    : recentEntries

  if (!filteredEntries || filteredEntries.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              🔥 Trending Diary Entries
            </h1>
            <p className="text-gray-600">
              No entries from the past week
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Get reaction counts for these entries
  const entryIds = (searchQ ? filteredEntries : recentEntries).map((e: any) => e.id)
  const { data: reactionCounts } = await supabase
    .from('reactions')
    .select('diary_entry_id, created_at')
    .in('diary_entry_id', entryIds)

  // Get hourly reaction counts (reactions in the last hour)
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
  const { data: hourlyReactionCounts } = await supabase
    .from('reactions')
    .select('diary_entry_id')
    .in('diary_entry_id', entryIds)
    .gte('created_at', oneHourAgo)

  // Count total reactions per entry
  const totalReactionsMap = reactionCounts?.reduce((acc, reaction) => {
    acc[reaction.diary_entry_id] = (acc[reaction.diary_entry_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  // Count hourly reactions per entry
  const hourlyReactionsMap = hourlyReactionCounts?.reduce((acc, reaction) => {
    acc[reaction.diary_entry_id] = (acc[reaction.diary_entry_id] || 0) + 1
    return acc
  }, {} as Record<string, number>) || {}

  // Map entries with reaction counts and sort by total reactions
  const baseEntries = (searchQ ? filteredEntries : recentEntries)
  const entries: TopDiaryEntry[] = baseEntries
    .map((entry: any) => ({
      entry_id: entry.id,
      title: entry.title,
      writer_name: entry.users?.[0]?.name || entry.users?.name,
      writer_id: entry.users?.[0]?.id || entry.users?.id,
      writer_avatar: entry.users?.[0]?.profile_picture_url || entry.users?.profile_picture_url,
      hourly_reactions: hourlyReactionsMap[entry.id] || 0,
      total_reactions: totalReactionsMap[entry.id] || 0,
      first_photo_url: entry.photos?.[0]?.url || null,
      video_url: entry.videos?.[0]?.r2_public_url || null,
      video_title: entry.videos?.[0]?.title || null,
      created_at: entry.created_at,
      is_free: entry.is_free
    }))
    .sort((a, b) => b.total_reactions - a.total_reactions)
    .slice(0, 20)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-6">
            {/* Global Search */}
            <div className="mt-4">
              <GlobalSearchBar />
            {/* Diary search (local to diaries) */}
            <form action="/trending" method="get" className="mt-4">
              <div className="relative max-w-2xl mx-auto">
                <input
                  type="text"
                  name="search"
                  defaultValue={sp.search || ''}
                  placeholder="Search diary entries…"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm bg-white focus:ring-2 focus:ring-purple-500 focus:border-transparent placeholder-gray-500"
                />
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">🔎</span>
                <button type="submit" className="absolute right-2 top-1/2 -translate-y-1/2 px-3 py-1.5 bg-gray-900 text-white rounded-md text-xs">Search</button>
              </div>
            </form>
            </div>

        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🔥 Trending Diary Entries
          </h1>
          <p className="text-gray-600">
            The most reacted diary entries from the past week
          </p>
          <p className="text-sm text-gray-500 mt-1 italic">
            Rankings update automatically every hour
          </p>



          {/* Call to Action for Supporting Creators */}
          <div className="mt-6">
            <Link
              href="/discover"
              className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              <span>💝</span>
              Explore Creators to Support
            </Link>
            <p className="text-xs text-gray-500 mt-2">
              Discover amazing writers and support their work
            </p>
          </div>
        </div>

        {/* Top Entries Grid */}
        {entries.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <p className="text-gray-500">No trending entries right now</p>
            <p className="text-sm text-gray-400 mt-2">
              Check back later or be the first to create a trending post!
            </p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {entries.map((entry, index) => (
              <div key={entry.entry_id} className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                {/* Ranking Badge */}
                <div className="relative">
                  <div className={`absolute top-3 left-3 z-10 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-orange-500' :
                    'bg-gray-600'
                  }`}>
                    #{index + 1}
                  </div>

                  {/* Video, Photo or Placeholder */}
                  {entry.video_url ? (
                    <div className="relative w-full h-48 bg-black">
                      <video
                        className="w-full h-full object-cover"
                        preload="metadata"
                        muted
                      >
                        <source src={entry.video_url} type="video/mp4" />
                      </video>
                      <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                        <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                          <svg className="w-6 h-6 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                          </svg>
                        </div>
                      </div>
                      <div className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                        🎥 VIDEO
                      </div>
                    </div>
                  ) : entry.first_photo_url ? (
                    <Image
                      src={entry.first_photo_url}
                      alt={entry.title}
                      width={400}
                      height={192}
                      className="w-full h-48 object-cover"
                    />
                  ) : (
                    <div className="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <span className="text-4xl text-gray-400">📝</span>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="font-bold text-gray-900 mb-2 line-clamp-2">
                    {entry.title}
                  </h3>

                  {/* Writer Info */}
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                      {entry.writer_avatar ? (
                        <Link
                          href={`/u/${entry.writer_id}`}
                          className="block w-full h-full focus:outline-none focus:ring-2 focus:ring-blue-300 rounded-full"
                          aria-label={`View ${entry.writer_name}'s profile`}
                        >
                          <Image
                            src={entry.writer_avatar}
                            alt={entry.writer_name}
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        </Link>
                      ) : (
                        <Link
                          href={`/u/${entry.writer_id}`}
                          className="w-full h-full bg-gray-300 flex items-center justify-center text-xs text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-300 rounded-full"
                          aria-label={`View ${entry.writer_name}'s profile`}
                        >
                          {entry.writer_name.charAt(0).toUpperCase()}
                        </Link>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        <AuthorProfileLink userId={entry.writer_id} userName={entry.writer_name} />
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatTimeAgo(entry.created_at)}
                      </p>
                    </div>

                    {entry.is_free && (
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        FREE
                      </span>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>🔥 {entry.hourly_reactions} this hour</span>
                    <span>{entry.total_reactions} total reactions</span>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Link
                      href={`/d/${entry.entry_id}`}
                      className="flex-1 bg-gray-800 text-white text-center py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                    >
                      Read Entry
                    </Link>
                    <Link
                      href={`/u/${entry.writer_id}`}
                      className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                    >
                      Subscribe
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}


      </div>
    </div>
  )
}





