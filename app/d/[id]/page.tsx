import { createSupabaseServerClient } from "@/lib/supabase/client"
import { notFound } from "next/navigation"
import { DiaryInteractions } from "@/components/DiaryInteractions"
import { CommentsSection } from "@/components/CommentsSection"
import { LinkButton } from "@/components/ui/link-button"
import { ReactionSystem } from "@/components/ReactionSystem"
import { StructuredData } from "@/components/StructuredData"
import { ImmersiveReader } from "@/components/ImmersiveReader"
import { VideoPlayer } from "@/components/VideoPlayer"
import { ShareButton } from "@/components/ShareButton"
import { PinPostButton } from "@/components/PinPostButton"
import { PostNavigation, ReadingPositionTracker } from "@/components/PostNavigation"
import { RelatedDiaryCarousel, RelatedDiaryEntry } from '@/components/RelatedDiaryCarousel'

import { StoryMap } from "@/components/StoryMap"
import { getSubscriptionStatus } from "@/lib/paywall"
import { FundingProgress, BackersList } from "@/components/StoryVentures"

interface DiaryPageProps {
  params: Promise<{
    id: string
  }>
}

// function formatPrice(cents: number) {
//   return `$${(cents / 100).toFixed(2)}`
// }

// function formatDate(dateString: string) {
//   return new Date(dateString).toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   })
// }

// function formatDateTime(dateString: string) {
//   return new Date(dateString).toLocaleString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric',
//     hour: 'numeric',
//     minute: '2-digit',
//     hour12: true
//   })
// }

// function getTeaserText(bodyMd: string, maxLength: number = 120) {
//   const plainText = bodyMd.replace(/[#*`_~\[\]()]/g, '').trim()
//   if (plainText.length <= maxLength) return plainText
//   return plainText.slice(0, maxLength) + '...'
// }

export default async function DiaryPage({ params }: DiaryPageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()

  // Get the current user (if any)
  const { data: { user } } = await supabase.auth.getUser()

  // First try to get the entry using the RPC (for published entries)
  let { data: entryData } = await supabase
    .rpc('get_entry_preview', {
      entry_id: id,
      viewer_id: user?.id
    })

  // Get writer profile data for profile picture and badge
  let writerProfile = null
  if (entryData && entryData.length > 0) {
    const { data: profileData } = await supabase
      .from('users')
      .select('id, name, avatar, profile_picture_url, has_day1_badge, signup_number')
      .eq('id', entryData[0].writer_id)
      .single()

    writerProfile = profileData
  }

  // If no data found and user is logged in, check if it's their own hidden entry
  if ((!entryData || entryData.length === 0) && user) {
    const { data: hiddenEntry, error: hiddenError } = await supabase
      .from('diary_entries')
      .select(`
        id,
        title,
        body_md,
        is_free,
        is_hidden,
        is_pinned,
        created_at,
        updated_at,
        user_id,
        users!inner (
          id,
          name,
          custom_url,
          price_monthly,
          avatar,
          profile_picture_url,
          has_day1_badge,
          signup_number
        )
      `)
      .eq('id', id)
      .eq('user_id', user.id)
      .eq('is_hidden', true)
      .single()

    if (hiddenEntry && !hiddenError) {
      // Check if users data exists - handle both array and object formats
      let userData = {}
      if (hiddenEntry.users) {
        if (Array.isArray(hiddenEntry.users) && hiddenEntry.users.length > 0) {
          userData = hiddenEntry.users[0]
        } else if (!Array.isArray(hiddenEntry.users)) {
          userData = hiddenEntry.users
        }
      }

      // Set writer profile for hidden entries
      writerProfile = {
        id: (userData as any)?.id,
        name: (userData as any)?.name,
        avatar: (userData as any)?.avatar,
        profile_picture_url: (userData as any)?.profile_picture_url,
        has_day1_badge: (userData as any)?.has_day1_badge,
        signup_number: (userData as any)?.signup_number
      }

      // Transform to match the RPC response format
      entryData = [{
        id: hiddenEntry.id,
        title: hiddenEntry.title,
        body_md: hiddenEntry.body_md,
        is_free: hiddenEntry.is_free,
        is_hidden: hiddenEntry.is_hidden,
        is_pinned: hiddenEntry.is_pinned,
        created_at: hiddenEntry.created_at,
        updated_at: hiddenEntry.updated_at,
        user_id: hiddenEntry.user_id,
        writer_id: hiddenEntry.user_id,
        writer_name: (userData as any)?.name || 'Unknown Writer',
        writer_custom_url: (userData as any)?.custom_url || null,
        writer_price: (userData as any)?.price_monthly || 999,
        can_read_full: true // Writer can always read their own content
      }]
    }
  }

  if (!entryData || entryData.length === 0) {
    notFound()
  }

  const entry = entryData[0]

  // Check subscription status for paywall
  const hasSubscription = user ? await getSubscriptionStatus(
    supabase,
    user.id,
    entry.writer_id
  ) : false

  const isOwner = user?.id === entry.writer_id
  const hasAccess = entry.is_free || hasSubscription || isOwner
  const canReadFull = entry.can_read_full || hasAccess

  // Get photos for this entry
  const { data: photos } = await supabase
    .from('photos')
    .select('id, url, alt_text')
    .eq('diary_entry_id', id)
    .eq('moderation_status', 'approved')
    .order('created_at', { ascending: true })

  // Get videos for this entry
  const { data: videos } = await supabase
    .from('videos')
    .select('id, r2_file_key, r2_public_url, title, is_free, view_count, file_size')
    .eq('post_id', id)
    .order('created_at', { ascending: true })

  // Get reaction counts and user's reaction
  const { data: reactionCounts } = await supabase
    .rpc('get_reaction_counts', { entry_id: id })

  const { data: userReactionData } = user ? await supabase
    .from('reactions')
    .select('reaction_type')
    .eq('diary_entry_id', id)
    .eq('user_id', user.id)
    .single() : { data: null }

  // Convert reaction counts to object
  const reactions = reactionCounts?.reduce((acc: Record<string, number>, item: any) => {
    acc[item.reaction_type] = item.count
    return acc
  }, {}) || {}

  // Related diary entries (simple keyword-based)
  let related: RelatedDiaryEntry[] = []
  try {
    // naive keyword selection from title; fall back to last word if needed
    const titleWords = (entry.title || '').split(/\s+/).filter(w => w.length > 3)
    const keyword = titleWords[0] || (entry.title || '').split(/\s+/).pop() || ''

    if (keyword) {
      const { data: rel } = await supabase
        .from('diary_entries')
        .select(`id, title, created_at, user:users!user_id ( id, name, avatar, profile_picture_url, has_day1_badge, signup_number, badge_tier )`)
        .ilike('title', `%${keyword}%`)
        .neq('id', entry.id)
        .eq('is_hidden', false)
        .order('created_at', { ascending: false })
        .limit(50)

      related = (rel as any) || []
    } else {
      const { data: rel } = await supabase
        .from('diary_entries')
        .select(`id, title, created_at, user:users!user_id ( id, name, avatar, profile_picture_url, has_day1_badge, signup_number, badge_tier )`)
        .neq('id', entry.id)
        .eq('is_hidden', false)
        .order('created_at', { ascending: false })
        .limit(50)

      related = (rel as any) || []
    }

      // Always fetch a recent pool and fill up to 50, with matches first
      const { data: recentPool } = await supabase
        .from('diary_entries')
        .select(`id, title, created_at, user:users!user_id ( id, name, avatar, profile_picture_url, has_day1_badge, signup_number, badge_tier )`)
        .neq('id', entry.id)
        .eq('is_hidden', false)
        .order('created_at', { ascending: false })
        .limit(50)

      // Merge keyword matches (if any) followed by recents, de-dupe by id, cap at 50
      const seen = new Set<string>()
      const merged: RelatedDiaryEntry[] = []
      ;[...(related || []), ...((recentPool as any) || [])].forEach((item: any) => {
        if (!item || !item.id) return
        if (seen.has(item.id)) return
        seen.add(item.id)
        merged.push(item)
      })
      related = merged.slice(0, 50)

  } catch (e) {
    console.warn('related diary fetch failed', e)
  }



  return (
    <>
      <StructuredData
        type="article"
        data={{
          id: entry.id,
          title: entry.title,
          content: entry.body_md,
          created_at: entry.created_at,
          updated_at: entry.updated_at,
          author: {
            id: entry.writer_id,
            name: entry.writer_name,

            custom_url: entry.writer_custom_url
          },
          photos: photos || undefined
        }}
      />
      <ImmersiveReader
        entry={entry}
        user={user ? { id: user.id, role: user.role ?? '' } : null}
        canReadFull={canReadFull}
        writerProfile={writerProfile}
      >
        {/* Photos */}
        {photos && photos.length > 0 && canReadFull && (
          <div className="mb-8 flex justify-center">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl">
              {photos.map((photo) => (
                <div key={photo.id} className="relative">
                  <img
                    src={photo.url}
                    alt={photo.alt_text}
                    className="rounded-lg object-cover w-full h-auto"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Videos */}
        {videos && videos.length > 0 && (
          <div className="mb-8 space-y-6">
            {videos.map((video) => (
              <VideoPlayer
                key={video.id}
                videoId={video.id}
                r2PublicUrl={video.r2_public_url}
                title={video.title}
                hasAccess={canReadFull || video.is_free}
                writerName={entry.writer_name}
                writerId={entry.writer_id}
                initialViewCount={video.view_count || 0}
              />
            ))}
          </div>
        )}

        {/* Reactions, Share, and Pin Buttons */}
        {canReadFull && (
          <div className="mb-6 flex items-center gap-4 flex-wrap">
            <ReactionSystem
              contentId={entry.id}
              contentType="diary"
              currentUserId={user?.id}
              initialReactions={reactions}
              userReaction={userReactionData?.reaction_type || null}
            />
            <ShareButton
              title={entry.title}
              writerName={entry.writer_name}
              contentType="diary"
              contentId={entry.id}
            />
            <PinPostButton
              entryId={entry.id}
              isPinned={entry.is_pinned || false}
              isOwner={isOwner}
            />
          </div>
        )}

        {/* Reading Position Tracker */}
        <ReadingPositionTracker
          entryId={entry.id}
          writerId={entry.writer_id}
          userId={user?.id}
        />

        {/* Post Navigation */}
        <PostNavigation
          currentEntryId={entry.id}
          writerId={entry.writer_id}
          writerName={entry.writer_name}
        />

        {/* Story Map */}
        <StoryMap
          currentEntryId={entry.id}
          writerId={entry.writer_id}
          writerName={entry.writer_name}
        />

        {/* Story Ventures Section */}
        {entry.is_story_venture && (
          <div className="space-y-6">
            <FundingProgress
              entryId={entry.id}
              goalCents={entry.funding_goal_cents}
              raisedCents={entry.funding_raised_cents}
              deadline={entry.funding_deadline}
              showPublicly={entry.show_funding_publicly}
            />

            {entry.show_funding_publicly && (
              <BackersList
                entryId={entry.id}
                showPublicly={entry.show_funding_publicly}
                limit={10}
              />
            )}

          </div>
        )}

        {/* Interactive Elements */}
        <DiaryInteractions
          canReadFull={canReadFull}
          entryId={entry.id}
          writerId={entry.writer_id}
          isStoryVenture={entry.is_story_venture}
          storyVentureDescription={entry.story_venture_description}
        />

        {/* Comments Section */}
        {canReadFull && (
          <CommentsSection
            entryId={entry.id}
            canComment={canReadFull && !!user}
            userId={user?.id}
          />
        )}

        {/* Login Prompt for Visitors */}
        {!user && (
          <div className="bg-white rounded-lg p-6 shadow-sm text-center">
            <p className="text-gray-600 font-serif mb-4">

              Sign in or create an account to subscribe and unlock all content
            </p>
            <div className="flex gap-3 justify-center">
              <LinkButton href="/login" variant="outline">
                Sign In
              </LinkButton>
              <LinkButton href="/register" className="bg-gray-800 text-white hover:bg-gray-700">
                Create Account
              </LinkButton>
            </div>
          </div>
        )}
      </ImmersiveReader>
      {/* Related entries: small horizontal carousel */}
      {related && related.length > 0 && (
        <div className="mt-10">
          <RelatedDiaryCarousel entries={related} />
        </div>
      )}

    </>
  )
}


