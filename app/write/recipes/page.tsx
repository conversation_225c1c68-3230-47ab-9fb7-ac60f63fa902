"use client"

import { useEffect, useState, useRef } from "react"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"
import RecipePhotoUpload from "@/components/RecipePhotoUpload"
import RecipeVideoUpload from "@/components/RecipeVideoUpload"
import { Button } from "@/components/ui/button"
import { Utensils, Image as ImageIcon, Video as VideoIcon, ListChecks } from "lucide-react"

import dynamic from "next/dynamic"
const IngredientsEditor = dynamic(() => import('@/components/recipes/IngredientsEditor'), { ssr: false })
const StepsEditor = dynamic(() => import('@/components/recipes/StepsEditor'), { ssr: false })
export default function RecipeCreate() {
  const supabase = createSupabaseClient()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [recipeId, setRecipeId] = useState<string | null>(null)
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [isFree, setIsFree] = useState(true)
  const [priceCents, setPriceCents] = useState<number | null>(null)
  const [ingredients, setIngredients] = useState<any[]>([])
  const [steps, setSteps] = useState<any[]>([])

  const titleRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    (async () => {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      if (error || !authUser) {
        window.location.href = '/login'
        return
      }
      setUser(authUser)
      setLoading(false)
    })()
  }, [supabase])

  const ensureRecipeId = async (): Promise<string | null> => {
    if (recipeId) return recipeId
    const { data, error } = await supabase
      .from('recipes' as any)
      .insert({ user_id: user.id, title: title || 'Untitled Recipe', description: description || '', is_free: isFree, price_cents: priceCents, is_hidden: true })
      .select('id')
      .single()
    if (error) { setError(error.message); return null }
    setRecipeId(data.id)
    return data.id
  }

  const handlePublish = async () => {
    try {
      setSaving(true)
      setError(null)
      const rid = await ensureRecipeId()
      if (!rid) throw new Error('Failed to save recipe')

      // Update recipe basics and unhide
      const { error: updErr } = await supabase
        .from('recipes' as any)
        .update({ title, description, is_free: isFree, price_cents: priceCents, is_hidden: false })
        .eq('id', rid)
      if (updErr) throw updErr

      // Persist ingredients
      await supabase.from('recipe_ingredients' as any).delete().eq('recipe_id', rid)
      if (ingredients.length > 0) {
        const rows = ingredients.map((ing, idx) => ({
          recipe_id: rid,
          position: idx + 1,
          section: ing.section || null,
          amount: ing.amount || null,
          unit: ing.unit || null,
          item: ing.item,
          note: ing.note || null
        }))
        const { error: ingErr } = await supabase.from('recipe_ingredients' as any).insert(rows)
        if (ingErr) throw ingErr
      }

      // Persist steps
      await supabase.from('recipe_steps' as any).delete().eq('recipe_id', rid)
      if (steps.length > 0) {
        const rows = steps.map((st, idx) => ({
          recipe_id: rid,
          position: idx + 1,
          instruction: st.instruction,
          time_hint: st.time_hint || null
        }))
        const { error: stepErr } = await supabase.from('recipe_steps' as any).insert(rows)
        if (stepErr) throw stepErr
      }

      window.location.href = `/recipes/${rid}`
    } catch (e: any) {
      setError(e.message || 'Publish failed')
    } finally {
      setSaving(false)
    }
  }

  if (loading) return <div className="min-h-screen flex items-center justify-center"><div className="font-serif text-gray-600">Loading...</div></div>
  if (!user) return null

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <Link href="/write" className="text-sm text-gray-500 hover:text-gray-700">← Back</Link>
          <div className="flex gap-2">
            <Button onClick={handlePublish} disabled={saving} className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50">
              {saving ? 'Publishing…' : 'Publish'}
            </Button>
          </div>
        </div>

        {/* Title + meta */}
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-amber-100 to-orange-100 flex items-center justify-center">
              <Utensils className="w-5 h-5 text-amber-700" />
            </div>
            <h1 className="text-3xl sm:text-4xl font-serif text-gray-900">Create a Recipe</h1>
          </div>
          <p className="text-sm text-gray-600">
            Share your signature dishes with step-by-step instructions. Add one photo OR video per recipe.
          </p>
        </div>

        {error && <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-red-600 text-sm">{error}</div>}

        {/* Basics */}
        <div className="space-y-4 mb-8">
          <div className="rounded-xl border border-gray-200 bg-white p-4 sm:p-5">
            <input
              ref={titleRef}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Recipe title"
              className="w-full border border-gray-200 rounded-lg px-4 py-3 font-serif text-lg focus:ring-2 focus:ring-amber-200 focus:border-amber-300"
            />
            <p className="text-xs text-gray-500 italic mt-1">e.g., The Best Banana Bread</p>

            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your recipe…"
              rows={5}
              className="mt-4 w-full border border-gray-200 rounded-lg px-4 py-3 font-serif focus:ring-2 focus:ring-amber-200 focus:border-amber-300"
            />
            <p className="text-xs text-gray-500 italic mt-1">e.g., Share what makes your recipe special, substitutions, tips.</p>

            <div className="flex items-center gap-4 mt-4">
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={isFree} onChange={(e) => setIsFree(e.target.checked)} />
                <span className="font-serif text-gray-700">Free recipe</span>
              </label>
              {!isFree && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Price:</span>
                  <input
                    type="number"
                    min={0}
                    placeholder="USD cents"
                    value={priceCents ?? ''}
                    onChange={(e) => setPriceCents(e.target.value === '' ? null : Math.max(0, Math.floor(Number(e.target.value))))}
                    className="border border-gray-300 rounded px-3 py-2 w-40"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Media */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
          <div className="rounded-xl border border-gray-200 bg-white p-4">
            <div className="flex items-center gap-2 mb-3 text-gray-800">
              <ImageIcon className="w-4 h-4" />
              <span className="text-sm font-medium">Recipe Photo</span>
            </div>
            <RecipePhotoUpload recipeId={recipeId} onEnsureRecipeId={ensureRecipeId} />
          </div>

          <div className="rounded-xl border border-gray-200 bg-white p-4">
            <div className="flex items-center gap-2 mb-3 text-gray-800">
              <VideoIcon className="w-4 h-4" />
              <span className="text-sm font-medium">Recipe Video</span>
            </div>
            <RecipeVideoUpload recipeId={recipeId} onEnsureRecipeId={ensureRecipeId} />
          </div>
        </div>

        {/* Ingredients & Steps */}
        <div className="rounded-xl border border-gray-200 bg-white p-4 sm:p-5 mb-12">
          <div className="flex items-center gap-2 mb-4 text-gray-800">
            <ListChecks className="w-4 h-4" />
            <span className="text-sm font-medium">Ingredients & Steps</span>
          </div>
          {/* @ts-expect-error: dynamic import will handle client-only components */}
          <IngredientsEditor value={ingredients} onChange={setIngredients} />
          <div className="my-4 h-px bg-gray-100" />
          {/* @ts-expect-error */}
          <StepsEditor value={steps} onChange={setSteps} />
        </div>

      </div>
    </div>
  )
}

