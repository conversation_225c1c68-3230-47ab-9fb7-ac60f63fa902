import { createSupabaseServerClient } from '@/lib/supabase/client'
import Link from 'next/link'

function highlight(text: string, q: string) {
  if (!text) return ''
  const idx = text.toLowerCase().indexOf(q.toLowerCase())
  if (idx === -1) return text
  return text.slice(0, idx) + '<mark>' + text.slice(idx, idx + q.length) + '</mark>' + text.slice(idx + q.length)
}

export default async function SearchPage({ searchParams }: { searchParams: Promise<{ q?: string }> }) {
  const { q = '' } = await searchParams
  const query = (q || '').trim()
  const supabase = await createSupabaseServerClient()

  let people: any[] = []
  let diary: any[] = []
  let books: any[] = []
  let audio: any[] = []

  if (query) {
    // People
    const { data: peopleData } = await supabase
      .from('users')
      .select('id, name, profile_picture_url, avatar, bio')
      .ilike('name', `%${query}%`)
      .limit(20)

    // Diary
    const { data: diaryData } = await supabase
      .from('diary_entries')
      .select('id, title, body_md, created_at, user:users!user_id(id, name)')
      .eq('is_hidden', false)
      .or(`title.ilike.%${query}%,body_md.ilike.%${query}%`)
      .limit(20)

    // Books
    const { data: booksData } = await supabase
      .from('projects')
      .select('id, title, description, slug, user_id, author_name, user:users!user_id(id, name)')
      .eq('is_ebook', true)
      .eq('is_complete', true)
      .eq('is_private', false)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,author_name.ilike.%${query}%`)
      .limit(20)

    // Audio
    const { data: audioData } = await supabase
      .from('audio_posts')
      .select('id, description, created_at, user:users!user_id(id, name)')
      .or(`description.ilike.%${query}%`)
      .limit(20)

    people = peopleData || []
    diary = diaryData || []
    books = booksData || []
    audio = audioData || []
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-2xl font-serif text-gray-900">Search</h1>
          {query && <p className="text-sm text-gray-600">Results for “{query}”</p>}
        </div>

        {!query ? (
          <div className="text-gray-600">Type a query in any search bar to see results here.</div>
        ) : (
          <div className="space-y-8">
            {/* People */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-3">People</h2>
              {people.length === 0 ? (
                <div className="text-gray-500 text-sm">No people found</div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {people.map((p) => (
                    <Link key={p.id} href={`/u/${p.id}`} className="bg-white rounded-lg p-3 border border-gray-200 hover:shadow-sm transition">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden flex items-center justify-center">
                          {p.profile_picture_url || p.avatar ? (
                            // eslint-disable-next-line @next/next/no-img-element
                            <img src={p.profile_picture_url || p.avatar} alt={p.name} className="w-10 h-10 object-cover" />
                          ) : (
                            <span className="text-sm text-gray-500">{p.name?.charAt(0).toUpperCase()}</span>
                          )}
                        </div>
                        <div className="min-w-0">
                          <div className="font-medium text-gray-900 truncate" dangerouslySetInnerHTML={{ __html: highlight(p.name, query) }} />
                          {p.bio && <div className="text-xs text-gray-500 truncate">{p.bio}</div>}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Diary */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-3">Diary</h2>
              {diary.length === 0 ? (
                <div className="text-gray-500 text-sm">No diary entries found</div>
              ) : (
                <div className="space-y-2">
                  {diary.map((d) => (
                    <Link key={d.id} href={`/d/${d.id}`} className="block bg-white rounded-lg p-3 border border-gray-200 hover:shadow-sm transition">
                      <div className="font-medium text-gray-900" dangerouslySetInnerHTML={{ __html: highlight(d.title, query) }} />
                      <div className="text-xs text-gray-500 truncate">by {d.user?.name}</div>
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Books */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-3">Books</h2>
              {books.length === 0 ? (
                <div className="text-gray-500 text-sm">No books found</div>
              ) : (
                <div className="space-y-2">
                  {books.map((b) => (
                    <Link key={b.id} href={`/books/${b.slug || b.id}`} className="block bg-white rounded-lg p-3 border border-gray-200 hover:shadow-sm transition">
                      <div className="font-medium text-gray-900" dangerouslySetInnerHTML={{ __html: highlight(b.title, query) }} />
                      <div className="text-xs text-gray-500 truncate">by {b.author_name || b.user?.name}</div>
                    </Link>
                  ))}
                </div>
              )}
            </div>

            {/* Audio */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-3">Audio</h2>
              {audio.length === 0 ? (
                <div className="text-gray-500 text-sm">No audio posts found</div>
              ) : (
                <div className="space-y-2">
                  {audio.map((a) => (
                    <Link key={a.id} href={`/u/${a.user?.id}`} className="block bg-white rounded-lg p-3 border border-gray-200 hover:shadow-sm transition">
                      <div className="text-sm text-gray-700 truncate">{a.description}</div>
                      <div className="text-xs text-gray-500 truncate">by {a.user?.name}</div>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

