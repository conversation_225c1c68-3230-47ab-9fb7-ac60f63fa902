"use client"

import { useEffect, useMemo, useState } from "react"
import Link from "next/link"
import { createSupabaseClient } from "@/lib/supabase/client"

interface Row {
  rank: number
  sales_count: number
  project_id: string
  projects?: {
    id: string
    title: string
    cover_image_url: string | null
    author_name: string | null
    genre: string | null
    users?: { name: string }
    price_amount?: number | null
  }
}

export default function BestsellersPage() {
  const supabase = createSupabaseClient()
  const [tab, setTab] = useState<'paid' | 'free'>('paid')
  const [genre, setGenre] = useState<string>('all')
  const [rows, setRows] = useState<Row[]>([])
  const [loading, setLoading] = useState(true)
  const [latestDate, setLatestDate] = useState<string | null>(null)

  useEffect(() => {
    fetchBestsellers()
  }, [tab, genre])

  const genres = [
    'all','fiction','non_fiction','memoir','poetry','other'
  ]

  async function fetchBestsellers() {
    setLoading(true)
    try {
      // Get latest date in daily_bestsellers
      const { data: dateRows } = await supabase
        .from('daily_bestsellers')
        .select('date')
        .order('date', { ascending: false })
        .limit(1)

      const latest = dateRows?.[0]?.date || null
      setLatestDate(latest ?? null)

      // Pull bestsellers for that date and tab; join projects for details
      let query = supabase
        .from('daily_bestsellers')
        .select(`rank, sales_count, project_id, projects!inner(id, title, cover_image_url, author_name, genre, price_amount, users!inner(name))`)
        .eq('book_type', tab)

      if (latest) query = query.eq('date', latest)
      if (genre !== 'all') query = query.eq('projects.genre', genre)

      query = query.order('rank', { ascending: true }).limit(60)

      let { data, error } = await query
      if (error) throw error

      // Fallback: if no daily data yet, compute from projects quickly
      if (!data || data.length === 0) {
        const { data: proj } = await supabase
          .from('projects')
          .select('id, title, cover_image_url, author_name, genre, price_amount, sales_count, users!inner(name)')
          .eq('is_ebook', true)
          .eq('is_complete', true)
          .eq('is_private', false)
          .gt(tab === 'paid' ? 'price_amount' : 'sales_count', tab === 'paid' ? 0 : 0) // keep filter present
          .order('sales_count', { ascending: false })
          .limit(60)

        const filtered = (proj || []).filter(p => genre === 'all' || p.genre === genre)
        data = filtered.map((p, i) => ({
          rank: i + 1,
          sales_count: (p as any).sales_count || 0,
          project_id: p.id,
          projects: p as any
        })) as any
      }

      setRows(data as any)
    } catch (e) {
      console.error('bestsellers fetch error', e)
      setRows([])
    } finally {
      setLoading(false)
    }
  }

  const displayRows = useMemo(() => rows.slice(0, 30), [rows])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sticky header */}
      <div className="sticky top-16 z-40 bg-white/95 backdrop-blur border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-4 py-3 flex items-center justify-between">
          <div>
            <h1 className="text-xl font-serif text-gray-900">Bestseller Charts</h1>
            <p className="text-xs text-gray-500">{latestDate ? `Updated ${latestDate}` : 'Live data'}</p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setTab('paid')}
              className={`px-3 py-1.5 rounded-full text-sm border transition-colors ${tab === 'paid' ? '!bg-gray-900 !text-white !border-gray-900' : 'bg-gray-100 text-gray-900 border-gray-200 hover:bg-gray-200'}`}
            >
              Paid
            </button>
            <button
              onClick={() => setTab('free')}
              className={`px-3 py-1.5 rounded-full text-sm border transition-colors ${tab === 'free' ? '!bg-gray-900 !text-white !border-gray-900' : 'bg-gray-100 text-gray-900 border-gray-200 hover:bg-gray-200'}`}
            >
              Free
            </button>
            <select
              value={genre}
              onChange={e => setGenre(e.target.value)}
              className="hidden sm:block px-3 py-1.5 border border-gray-200 rounded-full text-sm bg-white"
            >
              {genres.map(g => (
                <option key={g} value={g}>{g === 'all' ? 'All Genres' : g.replace('_',' ')}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-4">
        {loading ? (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-56 bg-white border border-gray-200 rounded-xl animate-pulse" />
            ))}
          </div>
        ) : displayRows.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">No rankings yet</h3>
            <p className="text-gray-600">Come back soon as sales roll in.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {displayRows.map((row) => (
              <Link key={`${row.project_id}-${row.rank}`} href={`/books/${row.projects?.id || row.project_id}`} className="block">
                <div className="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-md transition-all">
                  {/* Cover */}
                  <div className="aspect-[3/4] bg-white border-b border-gray-100 flex items-center justify-center overflow-hidden">
                    {row.projects?.cover_image_url ? (
                      // eslint-disable-next-line @next/next/no-img-element
                      <img src={row.projects.cover_image_url} alt={row.projects.title} className="w-full h-full object-contain" />
                    ) : (
                      <div className="text-4xl">📖</div>
                    )}
                  </div>
                  {/* Content */}
                  <div className="p-3">
                    {/* Rank pill */}
                    <div className="mb-2">
                      <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full bg-amber-100 text-amber-800 text-[11px] font-semibold border border-amber-200">
                        <span>#{row.rank}</span>
                        <span className="opacity-80">Bestseller</span>
                      </span>
                    </div>
                    <div className="mb-1 min-h-[1.75rem]">
                      <h3 className="font-semibold text-gray-900 text-sm leading-snug line-clamp-2">{row.projects?.title}</h3>
                    </div>
                    <div className="text-[11px] text-gray-600 mb-3">by {row.projects?.author_name || row.projects?.users?.name}</div>
                    {/* Centered sales */}
                    <div className="text-center text-xs text-gray-700 font-medium">
                      {row.sales_count?.toLocaleString()} sales
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

