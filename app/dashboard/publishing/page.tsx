"use client"

import Link from "next/link"
import { useEffect, useState } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"

interface ProjectRow {
  id: string
  title: string
  is_ebook: boolean
  is_complete: boolean
  updated_at: string
}

export default function DashboardPublishingIndex() {
  const [projects, setProjects] = useState<ProjectRow[]>([])
  const [userId, setUserId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()
  const router = useRouter()

  useEffect(() => {
    const init = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/login')
        return
      }
      setUserId(user.id)

      const { data } = await supabase
        .from('projects')
        .select('id, title, is_ebook, is_complete, updated_at')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false })

      setProjects(data || [])
      setLoading(false)
    }
    init()
  }, [router, supabase])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading…</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 py-8">
        <div className="mb-6">
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-900">Publishing</h1>
          <p className="text-gray-600">Manage your books and drafts</p>
        </div>

        {projects.length === 0 ? (
          <div className="text-center py-16">
            <p className="text-gray-600 mb-4">No projects yet.</p>
            <Link href="/write/upload-ebook" className="text-purple-700 hover:underline">Upload Book</Link>
          </div>
        ) : (
          <div className="space-y-3">
            {projects.map((p) => (
              <div key={p.id} className="bg-white border border-gray-200 rounded-xl p-4 flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">{p.title}</div>
                  <div className="text-xs text-gray-500">
                    {p.is_ebook && p.is_complete ? 'Published' : 'Draft'} • Updated {new Date(p.updated_at).toLocaleDateString()}
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Link href={`/dashboard/publishing/books/${p.id}`} className="text-sm text-purple-700 hover:underline">Manage</Link>
                  <Link href={`/books/${p.id}`} className="text-sm text-gray-600 hover:underline">View</Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

