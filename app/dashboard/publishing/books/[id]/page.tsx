"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { BookManager } from "@/components/BookManager"

interface Project {
  id: string
  title: string
  description: string | null
  cover_image_url: string | null
  genre: string | null
  book_type?: string | null
  price_amount: number
  average_rating?: number | null
  review_count?: number | null
  sales_count?: number | null
  is_ebook: boolean
  is_complete: boolean
  is_private: boolean
  created_at: string
  updated_at: string
}

export default function PublishingBookPage({ params }: { params: Promise<{ id: string }> }) {
  const [project, setProject] = useState<Project | null>(null)
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const init = async () => {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      if (!authUser) {
        router.push('/login')
        return
      }
      setUser(authUser)

      const resolved = await params
      const projectId = resolved.id

      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .eq('user_id', authUser.id)
        .single()

      if (error || !data) {
        router.push('/dashboard')
        return
      }

      setProject(data as Project)
      setLoading(false)
    }
    init()
  }, [params, router, supabase])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading publishing…</div>
      </div>
    )
  }

  if (!project) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 py-6 sm:py-10">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-900">Publish: {project.title}</h1>
          <p className="text-gray-600 mt-1">Manage details, pricing, and store availability</p>
        </div>

        {/* Management Card (reuses existing logic) */}
        <div className="mb-6">
          <BookManager book={project as any} onUpdate={async () => {
            // Refresh the project after actions (send to store, privacy, etc.)
            const resolved = await params
            const projectId = resolved.id
            const { data } = await supabase
              .from('projects')
              .select('*')
              .eq('id', projectId)
              .eq('user_id', user.id)
              .single()
            if (data) setProject(data as Project)
          }} />
        </div>

        {/* Quick links to existing editor flows (reuse, not rebuild) */}
        <div className="bg-white border border-gray-200 rounded-xl p-4 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-3">Next steps</h2>
          <ol className="list-decimal list-inside text-sm text-gray-700 space-y-2">
            <li>
              <Link className="text-purple-700 hover:underline" href={`/write/projects/${project.id}`}>Edit details & pricing in editor</Link>
            </li>
            <li>
              <button
                className="text-purple-700 hover:underline"
                onClick={() => router.push(`/books/${project.id}`)}
              >Preview in store</button>
            </li>
          </ol>
        </div>
      </div>
    </div>
  )
}

