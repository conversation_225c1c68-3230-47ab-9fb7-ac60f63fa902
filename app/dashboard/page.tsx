"use client"

import React, { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { WithdrawalSection } from "@/components/WithdrawalSection"
import { StripeConnectButton } from "@/components/StripeConnectButton"
import { Button } from "@/components/ui/button"
import { GlobalSearchBar } from '@/components/GlobalSearchBar'

import { usePaymentEvents } from "@/lib/realtime/RealtimeManager"

import { PriceEditor } from "@/components/PriceEditor"
import { EntriesManager } from "@/components/EntriesManager"
import { AudioPostsManager } from "@/components/AudioPostsManager"
import { RecipesManager } from "@/components/RecipesManager"
import { DashboardDuoPlayer } from "@/components/duo/DashboardDuoPlayer"

import { InvitePrompt } from "@/components/InvitePrompt"

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

// function formatDate(dateString: string) {
//   return new Date(dateString).toLocaleDateString('en-US', {
//     year: 'numeric',
//     month: 'long',
//     day: 'numeric'
//   })
// }

function timeAgo(dateString: string) {
  const then = new Date(dateString).getTime()
  const seconds = Math.max(1, Math.floor((Date.now() - then) / 1000))
  const units: [string, number][] = [
    ['y', 31536000],
    ['mo', 2592000],
    ['d', 86400],
    ['h', 3600],
    ['m', 60]
  ]
  for (const [label, secs] of units) {
    const v = Math.floor(seconds / secs)
    if (v >= 1) return `${v}${label} ago`
  }
  return 'just now'
}

export default function WriterDashboard() {
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [navigating, setNavigating] = useState<string | null>(null)
  const [stripeBalance, setStripeBalance] = useState<{
    available: number
    pending: number
    message?: string
  }>({ available: 0, pending: 0 })
  const [balanceLoading, setBalanceLoading] = useState(false)
  const [paymentNotification, setPaymentNotification] = useState<string | null>(null)
  const [totalReactions, setTotalReactions] = useState(0)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkAuthAndFetchData()
  }, [])

  // Handle hash navigation (e.g., #onlyduos)
  useEffect(() => {
    if (typeof window !== 'undefined' && window.location.hash) {
      const hash = window.location.hash.substring(1)
      setTimeout(() => {
        const element = document.getElementById(hash)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' })
          // Add a subtle highlight effect
          element.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.3)'
          setTimeout(() => {
            element.style.boxShadow = ''
          }, 2000)
        }
      }, 500) // Wait for page to fully load
    }
  }, [profile]) // Run when profile data loads

  // Use centralized real-time system for payment notifications
  usePaymentEvents((event) => {
    console.log('New payment received:', event.data)
    // Show notification about new payment
    const paymentAmount = event.data.amount_cents
    const paymentType = event.data.kind
    setPaymentNotification(`💰 New ${paymentType} payment received: ${formatPrice(paymentAmount)}`)

    // Auto-hide notification after 5 seconds
    setTimeout(() => setPaymentNotification(null), 5000)

    // Refresh balance when a new payment is recorded
    fetchStripeBalance()
  })

  // Real-time subscriptions now handled by centralized RealtimeManager
  // No duplicate subscriptions needed here

  // Load reaction counts for all entries
  useEffect(() => {
    const loadReactionCounts = async () => {
      if (!profile?.entries || profile.entries.length === 0) {
        setTotalReactions(0)
        return
      }

      try {
        let total = 0
        for (const entry of profile.entries) {
          const { data: reactionCounts } = await supabase
            .rpc('get_reaction_counts', { entry_id: entry.id })

          if (reactionCounts) {
            const entryTotal = reactionCounts.reduce((sum: number, reaction: any) => sum + reaction.count, 0)
            total += entryTotal
          }
        }
        setTotalReactions(total)
      } catch (error) {
        console.error('Error loading reaction counts:', error)
        setTotalReactions(0)
      }
    }

    loadReactionCounts()
  }, [profile?.entries, supabase])

  const checkAuthAndFetchData = async () => {
    try {
      // Check if user is authenticated
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError || !user) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from("users")
        .select("*")
        .eq("id", user.id)
        .single()

      if (profileError || !profileData) {
        console.log('Profile error or no profile:', profileError, profileData)
        router.push('/')
        return
      }

      console.log('User profile:', { id: profileData.id, role: profileData.role, name: profileData.name })

      // All authenticated users can access dashboard (unified experience)
      if (!profileData || (profileData.role !== 'user' && profileData.role !== 'admin')) {
        console.log('Access denied - invalid role:', profileData?.role)
        router.push('/login')
        return
      }

      console.log('Dashboard access granted for role:', profileData.role)

      // Get writer's diary entries
      const { data: entries } = await supabase
        .from("diary_entries")
        .select("id, title, body_md, created_at, is_free, is_hidden, view_count")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })

      // Get writer's audio posts (admin sees all, regular users see their own)
      let audioQuery = supabase
        .from("audio_posts")
        .select(`
          id,
          audio_url,
          description,
          duration_seconds,
          love_count,
          reply_count,
          created_at,
          reactions(
            id,
            reaction_type,
            created_at,
            user:users(name, avatar, profile_picture_url)
          ),
          audio_replies:audio_replies(
            id,
            audio_url,
            duration_seconds,
            love_count,
            created_at,
            user:users(name, avatar, profile_picture_url)
          )
        `)
        .order("created_at", { ascending: false })

      // Regular users only see their own posts
      if (profileData.role !== 'admin') {
        audioQuery = audioQuery.eq("user_id", user.id)
      }

      const { data: audioPosts, error: audioError } = await audioQuery

      console.log('Audio posts fetch result:', {
        audioPosts,
        audioError,
        userRole: profileData.role,
        userIdUsed: profileData.role === 'admin' ? 'ALL' : user.id
      })

      // Get writer's book projects
      const { data: bookProjects } = await supabase
        .from("projects")
        .select("id, title, description, created_at, is_private, total_chapters, total_words")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })

      // Get subscription count
      const { data: subscriptions } = await supabase
        .from("subscriptions")
        .select("id, reader_id, current_period_end")
        .eq("writer_id", user.id)
        .eq("status", "active")
        .gte("current_period_end", new Date().toISOString())

      // Get payments for earnings calculation
      const { data: payments } = await supabase
        .from("payments")
        .select("amount_cents, kind, created_at, stripe_payment_id")
        .eq("writer_id", user.id)
        .order("created_at", { ascending: false })

      console.log('All payments:', payments)

      // Get withdrawals for balance calculation
      const { data: withdrawals } = await supabase
        .from("withdrawals")
        .select("amount_cents, status")
        .eq("writer_id", user.id)
        .neq("status", "rejected")

      // Get ALL OnlyDuos you initiated (including incomplete ones)
      // This shows the real status of each OnlyDuo in the process
      const { data: duos } = await supabase
        .from('duo_posts' as any)
        .select(`
          id, status, created_at, responder_user_id, title, body,
          responder:users!duo_posts_responder_user_id_fkey(name, profile_picture_url, avatar)
        `)
        .eq('initiator_user_id', user.id)
        .in('status', ['pending', 'awaiting_approval'])
        .not('responder_user_id', 'is', null)
        .order('created_at', { ascending: false })

      // Get all parts for these duos to determine progress
      let duoParts: any[] = []
      let duoHasPartBIds: string[] = []
      if (duos && duos.length > 0) {
        const { data: parts } = await supabase
          .from('duo_parts' as any)
          .select('duo_post_id, part_number, author_user_id, media, created_at')
          .in('duo_post_id', (duos as any[]).map((d: any) => d.id))
          .order('part_number')

        duoParts = parts || []
        console.log('Fetched duo parts:', duoParts)

        // Determine which duos have Part B uploaded by the responder
        const duoResponder = new Map((duos as any[]).map((d: any) => [d.id, d.responder_user_id]))
        duoHasPartBIds = duoParts
          .filter(p => {
            const responderId = duoResponder.get(p.duo_post_id)
            const hasValidMedia = !!(p?.media && (p.media.src_r2_key || p.media.hls_manifest))
            const isPartB = p.part_number === 2 && responderId && p.author_user_id === responderId && hasValidMedia
            if (isPartB) {
              console.log('Found Part B for duo:', p.duo_post_id, p)
            }
            return isPartB
          })
          .map(p => p.duo_post_id)

        console.log('Duos with Part B:', duoHasPartBIds)
      }

      console.log('Setting profile with duos:', duos?.map((d: any) => ({ id: d.id, status: d.status })))

      setProfile({
        ...profileData,
        entries: entries || [],
        audioPosts: audioPosts || [],
        bookProjects: bookProjects || [],
        subscriptions: subscriptions || [],
        payments: payments || [],
        withdrawals: withdrawals || [],
        duos: duos || [],
        duoParts: duoParts || [],
        duoHasPartBIds
      })

      // Fetch real Stripe Connect balance
      await fetchStripeBalance()
    } catch (error) {
      console.error('Error in checkAuthAndFetchData:', error)
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  const fetchStripeBalance = async () => {
    try {
      setBalanceLoading(true)
      console.log('Fetching Stripe balance...')
      const response = await fetch('/api/stripe/balance')
      const data = await response.json()

      console.log('Stripe balance response:', { status: response.status, data })

      if (response.ok) {
        setStripeBalance({
          available: data.available,
          pending: data.pending,
          message: data.message
        })
        console.log('Stripe balance set:', data.available)
      } else {
        console.error('Error fetching Stripe balance:', data.error)
        setStripeBalance({ available: 0, pending: 0, message: data.error })
      }
    } catch (error) {
      console.error('Error fetching Stripe balance:', error)
      setStripeBalance({ available: 0, pending: 0, message: 'Failed to fetch balance' })
    } finally {
      setBalanceLoading(false)
    }
  }

  const handleNavigation = async (path: string) => {
    setNavigating(path)
    router.push(path)
  }

  // Signed preview URLs for Duo Part A (so previews work even if bucket is private)
  const [duoPreviewUrls, setDuoPreviewUrls] = useState<Record<string, string>>({})
  useEffect(() => {
    const duos = (profile as any)?.duos || []
    const duoParts = (profile as any)?.duoParts || []
    let cancelled = false
    async function run() {
      const next: Record<string, string> = {}
      for (const d of duos) {
        // Find Part A for this duo
        const partA = duoParts.find((p: any) => p.duo_post_id === d.id && p.part_number === 1)
        const key = partA?.media?.src_r2_key
        if (!key) continue
        try {
          const res = await fetch(`/api/media/sign?key=${encodeURIComponent(key)}`)
          if (res.ok) {
            const j = await res.json()
            next[d.id] = j.url
          } else {
            next[d.id] = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${key}`
          }
        } catch {
          next[d.id] = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${key}`
        }
      }
      if (!cancelled) setDuoPreviewUrls(next)
    }
    run()
    return () => { cancelled = true }
  }, [profile?.duos, profile?.duoParts])

  const [remindingId, setRemindingId] = useState<string | null>(null)
  const [requestingRedoId, setRequestingRedoId] = useState<string | null>(null)
  const [downloadingId, setDownloadingId] = useState<string | null>(null)
  const [approvingId, setApprovingId] = useState<string | null>(null)

  const handleRemind = async (id: string, inviteeId: string) => {
    setRemindingId(id)
    try {
      const res = await fetch('/api/duo/invite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId: id, invitee_user_id: inviteeId, reserve: false })
      })
      if (!res.ok) {
        const j = await res.json().catch(() => ({} as any))
        throw new Error(j?.error || 'Failed to send reminder')
      }
      alert('Reminder sent')
    } catch (e: any) {
      alert(e?.message || 'Failed to send reminder')
    } finally {
      setRemindingId(null)
    }
  }

  const handleRequestRedo = async (duoId: string) => {
    if (!confirm('Request a redo? This will ask your friend to record a new Part B video.')) return

    setRequestingRedoId(duoId)
    try {
      const res = await fetch('/api/duo/request-redo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId: duoId })
      })
      if (!res.ok) {
        const j = await res.json().catch(() => ({} as any))
        throw new Error(j?.error || 'Failed to request redo')
      }
      const result = await res.json()
      console.log('Redo request result:', result)
      alert('Redo request sent! Your friend will be notified to record a new Part B.')
      // Refresh the data by calling checkAuthAndFetchData again
      console.log('Refreshing dashboard data after redo...')
      await checkAuthAndFetchData()
      console.log('Dashboard data refreshed')
    } catch (e: any) {
      alert(e?.message || 'Failed to request redo')
    } finally {
      setRequestingRedoId(null)
    }
  }

  const handleDownload = async (duoId: string) => {
    setDownloadingId(duoId)
    try {
      const response = await fetch('/api/duo/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          duoPostId: duoId,
          includeWatermark: true,
          includeAttribution: true
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Download failed')
      }

      // Get the video file as a blob
      const blob = await response.blob()

      // Create download link
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `onlyduo-part-a-${duoId}.mp4`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      // Show success message
      alert('🎉 OnlyDuo download complete! This includes both videos merged with watermarks and author attribution - perfect for sharing on other platforms!')
    } catch (error) {
      console.error('Download failed:', error)
      alert(`Download failed: ${error instanceof Error ? error.message : 'Please try again.'}`)
    } finally {
      setDownloadingId(null)
    }
  }

  const handleApprove = async (duoId: string) => {
    console.log('🎯 handleApprove called for duo:', duoId)

    if (!confirm('✅ Approve and send this OnlyDuo to the timeline?\n\nThis will make it live and visible to all users.')) {
      console.log('❌ User cancelled approval')
      return
    }

    console.log('✅ User confirmed approval, proceeding...')
    setApprovingId(duoId)

    try {
      console.log('📡 Calling /api/duo/approve...')
      const response = await fetch('/api/duo/approve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ duoPostId: duoId })
      })

      console.log('📡 API response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('❌ API error:', errorData)
        throw new Error(errorData.error || 'Approval failed')
      }

      console.log('✅ Approval successful!')
      alert('🎉 OnlyDuo approved and sent to timeline!')

      // Refresh the data to show the new "Sent" state
      console.log('🔄 Refreshing dashboard data...')
      await checkAuthAndFetchData()
      console.log('✅ Dashboard data refreshed')
    } catch (error) {
      console.error('❌ Approval failed:', error)
      alert(`Approval failed: ${error instanceof Error ? error.message : 'Please try again.'}`)
    } finally {
      setApprovingId(null)
    }
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Dashboard...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Unable to load dashboard</p>
        </div>
      </div>
    )
  }

  const activeSubscriberCount = profile.subscriptions?.length || 0
  const monthlyRevenue = activeSubscriberCount * (profile.price_monthly || 0)

  // Count ALL content types
  const diaryEntriesCount = profile.entries?.length || 0
  const audioPostsCount = profile.audioPosts?.length || 0
  const bookProjectsCount = profile.bookProjects?.length || 0
  const totalEntries = diaryEntriesCount + audioPostsCount + bookProjectsCount

  // Use real Stripe Connect balance instead of manual calculations
  const availableBalance = stripeBalance.available // This is the actual withdrawable amount from Stripe

  return (
    <div className="min-h-screen bg-gray-50">{/* Dashboard container */}
      {/* Payment Notification */}
      {paymentNotification && (
        <div className="fixed top-4 right-4 z-50 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg animate-in slide-in-from-right-5">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">{paymentNotification}</span>
            <button
              onClick={() => setPaymentNotification(null)}
              className="ml-3 text-green-500 hover:text-green-700"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

        {/* Global Search */}
        <div className="mb-6">
          <GlobalSearchBar placeholder="Search your content and creators…" />
        </div>


      <div className="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Mobile-First Header */}
        <div className="mb-6" data-tutorial="dashboard-header">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg sm:text-xl font-serif text-gray-900 mb-1 leading-tight">
                Welcome back, {profile.name || 'Writer'}
              </h1>
              <p className="text-gray-600 text-sm">
                Manage your writing and track your progress
              </p>
            </div>
            <Button
              onClick={() => checkAuthAndFetchData()}
              disabled={loading}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              {loading ? '⟳' : '↻'} Refresh
            </Button>
          </div>
        </div>

        {/* Compact Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-2 mb-4">
          <div className="bg-white rounded-lg p-2 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1">Subscribers</h3>
            <p className="text-lg font-bold text-gray-900">{activeSubscriberCount}</p>
          </div>

          <div className="bg-white rounded-lg p-2 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1">Monthly Revenue</h3>
            <p className="text-lg font-bold text-gray-900">{formatPrice(monthlyRevenue)}</p>
          </div>

          <div className="bg-white rounded-lg p-2 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1">All Content</h3>
            <p className="text-lg font-bold text-gray-900">{totalEntries}</p>
            <p className="text-xs text-gray-400 mt-0.5">
              {diaryEntriesCount}d • {audioPostsCount}a • {bookProjectsCount}b
            </p>
          </div>

          <div className="bg-white rounded-lg p-2 shadow-sm border border-gray-100">
            <h3 className="text-xs font-medium text-gray-500 mb-1">Reactions</h3>
            <p className="text-lg font-bold text-gray-900">{totalReactions.toLocaleString()}</p>
          </div>

          {/* Prominent Financial Info */}
          <div className="rounded-xl p-4 bg-gradient-to-br from-green-50 to-blue-50 border border-green-200 shadow-sm xs:col-span-2 md:col-span-3 lg:col-span-1">
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide">Total Earnings</h3>
                <button
                  onClick={fetchStripeBalance}
                  disabled={balanceLoading}
                  className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
                  title="Refresh balance"
                >
                  <svg
                    className={`w-3 h-3 ${balanceLoading ? 'animate-spin' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {balanceLoading ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-24 mx-auto rounded"></div>
                ) : (
                  formatPrice(availableBalance + stripeBalance.pending)
                )}
              </div>

              <div className="flex justify-center space-x-4 text-sm mb-4">
                <div className="text-center">
                  <div className="font-semibold text-green-700">
                    {balanceLoading ? (
                      <div className="animate-pulse bg-green-200 h-4 w-16 mx-auto rounded"></div>
                    ) : (
                      formatPrice(availableBalance)
                    )}
                  </div>
                  <div className="text-xs text-green-600">Available</div>
                </div>
                {(stripeBalance.pending > 0 || balanceLoading) && (
                  <>
                    <div className="text-gray-300">|</div>
                    <div className="text-center">
                      <div className="font-semibold text-orange-700">
                        {balanceLoading ? (
                          <div className="animate-pulse bg-orange-200 h-4 w-16 mx-auto rounded"></div>
                        ) : (
                          formatPrice(stripeBalance.pending)
                        )}
                      </div>
                      <div className="text-xs text-orange-600">Pending</div>
                    </div>
                  </>
                )}
              </div>

              {availableBalance >= 1000 ? (
                <button
                  onClick={() => document.getElementById('withdrawal-section')?.scrollIntoView({ behavior: 'smooth' })}
                  className="w-full bg-green-600 text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors min-h-[44px] flex items-center justify-center shadow-sm"
                >
                  💰 Withdraw Funds
                </button>
              ) : (
                <div className="w-full py-2.5 px-4 rounded-lg text-sm bg-gray-100 text-gray-600 min-h-[44px] flex items-center justify-center">
                  {availableBalance > 0
                    ? `$${((1000 - availableBalance) / 100).toFixed(2)} more needed`
                    : '⏳ Processing payments...'
                  }
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Mobile-First Content Sections */}
        <div className="space-y-4 sm:space-y-6">
          {/* Quick Actions - Mobile-First Full Width */}
          <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
            <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-3 px-1">Quick Actions</h2>

            {/* Primary Actions - Mobile-First Full Width */}
            <div className="space-y-2 mb-3 sm:mb-4">
              <Button
                onClick={() => handleNavigation('/write')}
                disabled={navigating === '/write'}
                className="w-full bg-blue-600 text-white py-2.5 px-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors min-h-[44px] flex items-center justify-center"
              >
                {navigating === '/write' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span className="text-xs sm:text-sm">Creating...</span>
                  </>
                ) : (
                  <span className="text-xs sm:text-sm">📝 Create New Entry</span>
                )}
              </Button>

              <Button
                onClick={() => handleNavigation('/dashboard/publishing')}
                disabled={navigating === '/dashboard/publishing'}
                className="w-full bg-purple-600 text-white py-2.5 px-3 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors min-h-[44px] flex items-center justify-center"
              >
                {navigating === '/dashboard/publishing' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span className="text-xs sm:text-sm">Loading...</span>
                  </>
                ) : (
                  <span className="text-xs sm:text-sm">📚 Publishing Center</span>
                )}
              </Button>
            </div>

            {/* Secondary Actions - Mobile-First Stacked */}
            <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
              <Button
                onClick={() => handleNavigation('/profile')}
                disabled={navigating === '/profile'}
                className="w-full bg-gray-100 text-gray-700 py-2.5 px-3 rounded-md text-xs sm:text-sm hover:bg-gray-200 transition-colors border border-gray-200 min-h-[44px] flex items-center justify-center"
              >
                {navigating === '/profile' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span>Loading...</span>
                  </>
                ) : (
                  <span>Edit Profile</span>
                )}
              </Button>

              <Button
                onClick={() => handleNavigation(`/u/${profile.id}`)}
                disabled={navigating === `/u/${profile.id}`}
                className="w-full bg-gray-100 text-gray-700 py-2.5 px-3 rounded-md text-xs sm:text-sm hover:bg-gray-200 transition-colors border border-gray-200 min-h-[44px] flex items-center justify-center"
              >
                {navigating === `/u/${profile.id}` ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span>Loading...</span>
                  </>
                ) : (
                  <span>View Profile</span>
                )}
              </Button>
            </div>
          </div>

          {/* Your OnlyDuos - Improved Progress Tracking */}
          <div id="onlyduos" className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
            <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-1 px-1">Your OnlyDuos</h2>
            <p className="text-xs text-gray-500 px-1 mb-3">Track the progress of your collaborative video stories.</p>
            {(() => {
              const duos = (profile as any).duos || []
              const hasPartBSet = new Set(((profile as any).duoHasPartBIds || []) as string[])

              if (duos.length === 0) {
                return (
                  <div className="text-sm text-gray-600">
                    No active OnlyDuos yet.{' '}
                    <Link href="/duo/new" className="text-blue-600 hover:underline">Start an OnlyDuo</Link>
                  </div>
                )
              }

              return (
                <div className="space-y-3">
                  {duos.map((d: any) => {
                    const hasPartB = hasPartBSet.has(d.id)
                    const responderName = d.responder?.name || 'your friend'

                    // Get Part A and Part B info from duoParts (passed from server)
                    const duoParts = (profile as any).duoParts || []
                    const partA = duoParts.find((p: any) => p.duo_post_id === d.id && p.part_number === 1)
                    const partB = duoParts.find((p: any) => p.duo_post_id === d.id && p.part_number === 2)

                    // Determine the current stage and UI elements
                    let statusText: string
                    let statusColor: string
                    let actionButton: React.ReactNode

                    // Debug: Show actual status
                    console.log(`Duo ${d.id} - Status: ${d.status}, HasPartB: ${hasPartB}, PartA: ${!!partA}, PartB: ${!!partB}`)

                    if (!partA) {
                      statusText = `⚠️ Video upload incomplete - ${responderName} was invited but can't respond yet`
                      statusColor = 'text-amber-700 bg-amber-50 border-amber-200'
                      actionButton = (
                        <Link href={`/duo/${d.id}`} className="text-xs sm:text-sm text-white bg-amber-600 hover:bg-amber-700 px-3 py-1.5 rounded">
                          Complete Upload
                        </Link>
                      )
                    } else if (d.status === 'awaiting_approval') {
                      statusText = `✅ ${responderName} responded! Ready for your approval`
                      statusColor = 'text-blue-700 bg-blue-50 border-blue-200'
                      actionButton = (
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleApprove(d.id)}
                            disabled={approvingId === d.id}
                            className="text-xs sm:text-sm text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-60 px-3 py-1.5 rounded"
                          >
                            {approvingId === d.id ? 'Approving...' : '✅ Approve & Send'}
                          </button>
                          <button
                            onClick={() => handleRequestRedo(d.id)}
                            disabled={requestingRedoId === d.id}
                            className="text-xs sm:text-sm text-white bg-orange-600 hover:bg-orange-700 disabled:opacity-60 px-3 py-1.5 rounded"
                          >
                            {requestingRedoId === d.id ? 'Requesting...' : 'Request Redo'}
                          </button>
                        </div>
                      )
                    } else if (d.status === 'approved' || d.status === 'published') {
                      statusText = `✅ OnlyDuo completed with ${responderName}`
                      statusColor = 'text-green-700 bg-green-50 border-green-200'
                      actionButton = (
                        <Link href={`/duo/${d.id}`} className="text-xs sm:text-sm text-white bg-green-600 hover:bg-green-700 px-3 py-1.5 rounded">
                          View OnlyDuo
                        </Link>
                      )
                    } else {
                      statusText = `⏳ Waiting for ${responderName} to record their response`
                      statusColor = 'text-gray-700 bg-gray-50 border-gray-200'
                      actionButton = (
                        <button
                          onClick={() => handleRemind(d.id, d.responder_user_id)}
                          disabled={remindingId === d.id}
                          className="text-xs sm:text-sm text-white bg-purple-600 hover:bg-purple-700 disabled:opacity-60 px-3 py-1.5 rounded"
                        >
                          {remindingId === d.id ? 'Sending…' : 'Remind'}
                        </button>
                      )
                    }

                    return (
                      <div key={d.id} className={`rounded-lg border p-4 ${statusColor}`}>
                        <div className="flex flex-col lg:flex-row items-start gap-6">
                          {/* Construction View - Individual Parts */}
                          <div className="flex gap-3">
                            {/* Part A */}
                            <div className="w-20 sm:w-24 flex-shrink-0">
                              {partA?.media?.src_r2_key ? (
                                <div className="space-y-1">
                                  <video
                                    className="w-full aspect-[9/16] bg-black rounded-lg shadow-sm"
                                    src={duoPreviewUrls[d.id] || `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partA.media.src_r2_key}`}
                                    controls
                                    preload="metadata"
                                  />
                                  <div className="text-xs text-center text-gray-600 font-medium">Part A</div>
                                  <div className="text-xs text-center text-gray-500">You</div>
                                </div>
                              ) : (
                                <div className="w-full aspect-[9/16] bg-gray-200 rounded-lg flex items-center justify-center">
                                  <div className="text-center">
                                    <div className="text-xs text-gray-500 mb-1">No Video</div>
                                    <div className="text-xs text-gray-400">Upload needed</div>
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* Part B */}
                            {(d.status === 'awaiting_approval' || d.status === 'approved' || d.status === 'published') && partB?.media?.src_r2_key ? (
                              <div className="w-20 sm:w-24 flex-shrink-0">
                                <div className="space-y-1">
                                  <video
                                    className="w-full aspect-[9/16] bg-black rounded-lg shadow-sm"
                                    src={`${process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''}/${partB.media.src_r2_key}`}
                                    controls
                                    preload="metadata"
                                  />
                                  <div className="text-xs text-center text-gray-600 font-medium">Part B</div>
                                  <div className="text-xs text-center text-gray-500">{responderName}</div>
                                </div>
                              </div>
                            ) : partA?.media?.src_r2_key ? (
                              <div className="w-20 sm:w-24 flex-shrink-0">
                                <div className="w-full aspect-[9/16] bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                                  <div className="text-center">
                                    <div className="text-xs text-gray-500 mb-1">Part B</div>
                                    <div className="text-xs text-gray-400">Waiting...</div>
                                  </div>
                                </div>
                              </div>
                            ) : null}
                          </div>

                          {/* Equals Sign / Arrow */}
                          {(d.status === 'awaiting_approval' || d.status === 'approved' || d.status === 'published') && partB?.media?.src_r2_key && (
                            <div className="flex items-center justify-center lg:py-8">
                              <div className="text-purple-600 font-bold text-lg">=</div>
                            </div>
                          )}

                          {/* Final OnlyDuo Experience - Only show for awaiting approval */}
                          {d.status === 'awaiting_approval' && partA?.media?.src_r2_key && partB?.media?.src_r2_key && (
                            <div className="flex-1 max-w-xs">
                              <div className="bg-gradient-to-br from-purple-50 to-pink-50 border-2 border-purple-200 rounded-xl p-4 space-y-3">
                                <div className="text-center">
                                  <div className="text-sm font-medium text-purple-900 mb-1">OnlyDuo Final Experience</div>
                                  <div className="text-xs text-purple-600">How it appears on timeline</div>
                                </div>

                                {/* Unified Video Player */}
                                <div className="relative">
                                  <DashboardDuoPlayer
                                    partA={{
                                      type: 'video',
                                      src_r2_key: partA.media.src_r2_key,
                                      hls_manifest: partA.media.hls_manifest
                                    }}
                                    partB={{
                                      type: 'video',
                                      src_r2_key: partB.media.src_r2_key,
                                      hls_manifest: partB.media.hls_manifest
                                    }}
                                    duoId={d.id}
                                    isInitiator={d.initiator_user_id === profile?.id}
                                    onDelete={() => {
                                      window.location.reload()
                                    }}
                                    onRequestRedo={() => handleRequestRedo(d.id)}
                                    signedPlayback={false}
                                    initiatorName={profile?.name || 'You'}
                                    responderName={responderName}
                                    initiatorPhoto={profile?.profile_picture_url}
                                    responderPhoto={d.responder?.profile_picture_url}
                                  />
                                </div>

                                {/* Social Interaction Buttons */}
                                <div className="space-y-2">
                                  {/* Reaction, Comment & Download Buttons */}
                                  <div className="flex items-center justify-between text-xs">
                                    <div className="flex gap-3">
                                      <button className="flex items-center gap-1 text-red-500 hover:text-red-600 transition-colors">
                                        <span>❤️</span>
                                        <span>React</span>
                                      </button>
                                      <button className="flex items-center gap-1 text-blue-500 hover:text-blue-600 transition-colors">
                                        <span>💬</span>
                                        <span>Comment</span>
                                      </button>
                                      <button
                                        onClick={() => handleDownload(d.id)}
                                        disabled={downloadingId === d.id}
                                        className="flex items-center gap-1 text-green-500 hover:text-green-600 disabled:opacity-50 transition-colors"
                                      >
                                        <span>⬇️</span>
                                        <span>{downloadingId === d.id ? 'Processing...' : 'Download'}</span>
                                      </button>
                                    </div>
                                  </div>

                                  {/* Engagement Counts */}
                                  <div className="flex items-center gap-4 text-xs text-gray-500">
                                    <span className="flex items-center gap-1">
                                      <span>❤️</span>
                                      <span>0 reacts</span>
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <span>💬</span>
                                      <span>0 comments</span>
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <span>⬇️</span>
                                      <span>0 downloads</span>
                                    </span>
                                  </div>
                                </div>

                                {/* Approve Button */}
                                <button
                                  onClick={(e) => {
                                    e.preventDefault()
                                    e.stopPropagation()
                                    handleApprove(d.id)
                                  }}
                                  disabled={approvingId === d.id}
                                  className="relative z-10 w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-medium hover:from-purple-700 hover:to-pink-700 disabled:opacity-60 transition-all shadow-sm cursor-pointer"
                                  style={{ pointerEvents: 'auto' }}
                                >
                                  {approvingId === d.id ? '⏳ Approving...' : '✅ Approve & Send to Timeline'}
                                </button>
                              </div>
                            </div>
                          )}

                          {/* Clean "Sent" Confirmation for Approved Duos */}
                          {(d.status === 'approved' || d.status === 'published') && (
                            <div className="flex-1 max-w-xs">
                              <div className="bg-gradient-to-br from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl p-4 space-y-3">
                                <div className="text-center">
                                  <div className="text-2xl mb-2">✅</div>
                                  <div className="text-sm font-medium text-green-900 mb-1">Duo Sent to Timeline!</div>
                                  <div className="text-xs text-green-600">Live and earning engagement</div>
                                </div>

                                {/* Archive Actions */}
                                <div className="space-y-2">
                                  <div className="flex gap-2">
                                    <Link
                                      href="/timeline"
                                      className="flex-1 text-xs text-center bg-green-600 text-white py-2 px-3 rounded-lg hover:bg-green-700 transition-colors"
                                    >
                                      🎬 Watch on Timeline
                                    </Link>
                                  </div>
                                  <div className="flex gap-2">
                                    <button
                                      onClick={() => handleDownload(d.id)}
                                      disabled={downloadingId === d.id}
                                      className="flex-1 text-xs text-center bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                                    >
                                      {downloadingId === d.id ? '⬇️ Processing...' : '⬇️ Download'}
                                    </button>
                                    <button
                                      onClick={() => {
                                        if (confirm('Delete this OnlyDuo? This cannot be undone.')) {
                                          // Add delete handler here
                                          console.log('Delete duo:', d.id)
                                        }
                                      }}
                                      className="flex-1 text-xs text-center bg-red-600 text-white py-2 px-3 rounded-lg hover:bg-red-700 transition-colors"
                                    >
                                      🗑️ Delete
                                    </button>
                                  </div>
                                  <button
                                    onClick={() => {
                                      // Add remix functionality
                                      console.log('Remix duo:', d.id)
                                      alert('🔁 Remix feature coming soon! This will let you send Part A to a new collaborator.')
                                    }}
                                    className="w-full text-xs text-center bg-purple-600 text-white py-2 px-3 rounded-lg hover:bg-purple-700 transition-colors"
                                  >
                                    🔁 Remix This Duo
                                  </button>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Status & Info */}
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium mb-1">
                            OnlyDuo with {responderName}
                          </div>
                          <div className="text-sm mb-2">
                            {statusText}
                          </div>
                          <div className="text-xs opacity-75">
                            Started {timeAgo(d.created_at)}
                          </div>
                        </div>

                        {/* Action Button */}
                        <div className="flex-shrink-0">
                          {actionButton}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )
            })()}
          </div>

          {/* Profile Settings */}
          <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
            <div className="flex items-center gap-2 mb-4">
              <h2 className="text-base sm:text-lg font-serif text-gray-800 flex-1 min-w-0 px-1">Profile Settings</h2>
              <Link
                href="/profile"
                className="bg-purple-600 text-white px-2 py-2 rounded-md text-xs sm:text-sm font-medium hover:bg-purple-700 transition-colors min-h-[40px] flex items-center justify-center shrink-0"
              >
                Edit
              </Link>
            </div>

            <div className="space-y-4">
              <div>
                <PriceEditor
                  initialPrice={profile.price_monthly || 999}
                />
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 block mb-2">Payment Processing</label>
                <StripeConnectButton
                  isConnected={!!profile.stripe_account_id}
                  onboardingComplete={profile.stripe_onboarding_complete || false}
                />
                {!profile.stripe_account_id && (
                  <p className="text-xs text-gray-600 mt-2">
                    Connect Stripe to receive payments from subscribers.
                  </p>
                )}
              </div>

              <div className="w-full overflow-hidden">
                <label className="text-sm font-medium text-gray-700 block mb-2">Your Profile URL</label>
                <div className="bg-gray-50 p-2 rounded border w-full overflow-hidden">
                  <code className="text-xs text-gray-800 block w-full break-words overflow-wrap-anywhere">
                    onlydiary.app/{profile.custom_url || `u/${profile.id}`}
                  </code>
                </div>
                <div className="grid grid-cols-1 gap-2 mt-2 sm:grid-cols-2">
                  <Link
                    href={profile.custom_url ? `/${profile.custom_url}` : `/u/${profile.id}`}
                    className="bg-blue-600 text-white px-1 py-2 rounded text-xs font-medium hover:bg-blue-700 transition-colors text-center min-h-[40px] flex items-center justify-center"
                  >
                    <span className="truncate">View Profile</span>
                  </Link>
                  {!profile.custom_url ? (
                    <Link
                      href="/profile/edit"
                      className="bg-gray-100 text-gray-700 px-1 py-2 rounded text-xs font-medium hover:bg-gray-200 transition-colors text-center min-h-[40px] flex items-center justify-center border border-gray-200"
                    >
                      <span className="truncate">Set Custom URL</span>
                    </Link>
                  ) : (
                    <div className="bg-gray-50 px-1 py-2 rounded text-xs text-gray-500 text-center min-h-[40px] flex items-center justify-center border border-gray-200">
                      <span className="truncate">Custom URL Set</span>
                    </div>
                  )}
                </div>
              </div>

              {profile.bio && (
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-2">Bio</label>
                  <p className="text-gray-700 text-sm bg-gray-50 p-2 rounded break-words">{profile.bio}</p>
                </div>
              )}
            </div>
          </div>

        </div>

        {/* Marketing & Revenue Section - Combined */}
        <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 rounded-lg p-3 sm:p-4 border border-purple-100">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm">📧</span>
            </div>
            <div>
              <h2 className="text-base font-serif text-gray-800">Marketing & Revenue</h2>
              <p className="text-xs text-gray-600">Grow your audience and boost earnings</p>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
            {/* Mailing List */}
            <div className="bg-white rounded-lg p-3 border border-gray-100 flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm">📬</span>
                <h3 className="text-sm font-medium text-gray-800">Mailing List</h3>
              </div>
              <div className="flex-1 mb-3">
                <p className="text-xs text-gray-600">Build your email audience</p>
              </div>
              <Button
                onClick={() => handleNavigation('/dashboard/mailing')}
                disabled={navigating === '/dashboard/mailing'}
                className="w-full bg-blue-600 text-white py-2 px-3 rounded text-xs font-medium hover:bg-blue-700 transition-colors"
              >
                {navigating === '/dashboard/mailing' ? 'Loading...' : 'Manage List'}
              </Button>
            </div>

            {/* Push Notifications */}
            <div className="bg-white rounded-lg p-3 border border-gray-100 flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm">🔔</span>
                <h3 className="text-sm font-medium text-gray-800">Push Notifications</h3>
              </div>
              <div className="flex-1 mb-3">
                <p className="text-xs text-gray-600">$0.05 per notification</p>
                <p className="text-xs text-purple-600 font-medium">Boost revenue instantly</p>
              </div>
              <Button
                onClick={() => handleNavigation('/dashboard/notifications')}
                disabled={navigating === '/dashboard/notifications'}
                className="w-full bg-purple-600 text-white py-2 px-3 rounded text-xs font-medium hover:bg-purple-700 transition-colors"
              >
                {navigating === '/dashboard/notifications' ? 'Loading...' : 'Get Started'}
              </Button>
            </div>
          </div>
        </div>

        {/* Withdrawal Section */}
        <div id="withdrawal-section">
          <WithdrawalSection
            availableBalance={availableBalance}
            pendingBalance={stripeBalance.pending}
            userId={profile.id}
          />
        </div>

        {/* Invite Friends Prompt */}
        <InvitePrompt variant="card" userName={profile.name} />

        {/* Manage Entries */}
        <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
          <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-4 px-1">Manage Entries</h2>
          <EntriesManager initialEntries={profile.entries || []} />
        </div>

        {/* Manage Audio Posts */}
        <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
          <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-4 px-1">Manage Audio Posts</h2>
          <AudioPostsManager initialPosts={profile.audioPosts || []} />
        </div>

        {/* Manage Recipes */}
        <div className="bg-white rounded-lg p-2 sm:p-4 shadow-sm border border-gray-100">
          <h2 className="text-base sm:text-lg font-serif text-gray-800 mb-4 px-1">Manage Recipes</h2>
          <RecipesManager userId={profile.id} />
        </div>

      </div>
    </div>
  )
}
