import { createSupabaseServerClient } from "@/lib/supabase/client"
import Image from "next/image"
import CookedItUploader from "@/components/CookedItUploader"
import { FollowButton } from "@/components/FollowButton"
import { Day1Badge } from "@/components/Day1Badge"
import { RecipeStickyHeader } from "@/components/RecipeStickyHeader"

export default async function RecipePage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()

  const { data: recipe } = await supabase
    .from('recipes' as any)
    .select('id, user_id, title, description, is_free, price_cents, cover_photo_url, cooked_count, created_at, users!inner(id, name, profile_picture_url, follower_count, has_day1_badge, signup_number, badge_tier)')
    .eq('id', id)
    .single()

  if (!recipe) {
    return <div className="min-h-screen flex items-center justify-center"><div className="text-gray-600 font-serif">Recipe not found</div></div>
  }

  const { data: { user } } = await supabase.auth.getUser()

  const { data: photos } = await supabase
    .from('recipe_photos' as any)
    .select('id, url, alt_text')
    .eq('recipe_id', id)
    .order('created_at', { ascending: true })

  const { data: videos } = await supabase
    .from('recipe_videos' as any)
    .select('id, r2_public_url, title')
    .eq('recipe_id', id)
    .order('created_at', { ascending: false })

  const { data: ingredients } = await supabase
    .from('recipe_ingredients' as any)
    .select('position, section, amount, unit, item, note')
    .eq('recipe_id', id)
    .order('position', { ascending: true })

  const { data: steps } = await supabase
    .from('recipe_steps' as any)
    .select('position, instruction, time_hint')
    .eq('recipe_id', id)
    .order('position', { ascending: true })

  const { data: cookEvents } = await supabase
    .from('recipe_cook_events' as any)
    .select('id, user_id, created_at')
    .eq('recipe_id', id)
    .order('created_at', { ascending: false })
    .limit(10)


  // Compute dynamic trending rank among recent recipes (last 7 days)
  let trendingRank: number | null = null
  let rankBadgeClass = ''
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
  const { data: recentRecipes } = await supabase
    .from('recipes' as any)
    .select('id, created_at')
    .gte('created_at', oneWeekAgo)
    .order('created_at', { ascending: false })
    .limit(50)


  const RankBadge = () => (
    trendingRank ? (
      <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold border ${rankBadgeClass}`}>
        {trendingRank === 1 ? '#1 Recipe' : `#${trendingRank} in Recipes`}
      </span>
    ) : null
  )

  if (recentRecipes && recentRecipes.length > 0) {
    const ids = recentRecipes.map((r: any) => r.id)
    const { data: cookEventsAll } = await supabase
      .from('recipe_cook_events' as any)
      .select('recipe_id, created_at')
      .in('recipe_id', ids)

    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()

    const { data: hourlyCooks } = await supabase
      .from('recipe_cook_events' as any)
      .select('recipe_id')
      .in('recipe_id', ids)
      .gte('created_at', oneHourAgo)

    const totalMap = (cookEventsAll || []).reduce((acc: Record<string, number>, e: any) => {
      acc[e.recipe_id] = (acc[e.recipe_id] || 0) + 1
      return acc
    }, {})
    const hourlyMap = (hourlyCooks || []).reduce((acc: Record<string, number>, e: any) => {
      acc[e.recipe_id] = (acc[e.recipe_id] || 0) + 1
      return acc
    }, {})

    let items = recentRecipes.map((r: any) => ({
      id: r.id,
      total: totalMap[r.id] || 0,
      hourly: hourlyMap[r.id] || 0,
      created_at: r.created_at,
    }))

    const hasAny = items.some(x => x.total > 0)
    if (hasAny) {
      items = items.sort((a, b) => (b.total - a.total) || (b.hourly - a.hourly)).slice(0, 20)
    } else {
      items = items.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()).slice(0, 20)
    }

    const idx = items.findIndex(x => x.id === id)
    if (idx !== -1) {
      trendingRank = idx + 1
      rankBadgeClass = trendingRank === 1
        ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
        : trendingRank === 2
        ? 'bg-gray-100 text-gray-700 border-gray-200'
        : trendingRank === 3
        ? 'bg-amber-100 text-amber-800 border-amber-200'
        : 'bg-blue-50 text-blue-700 border-blue-200'
    }
  }


  return (
    <div className="min-h-screen bg-white py-8">
      <RecipeStickyHeader title={recipe.title} rank={trendingRank} writerId={recipe.user_id} writerName={recipe.users?.name} />
      <div className="max-w-3xl mx-auto px-4">
        {recipe.cover_photo_url && (
          <div className="mb-6">
            <div className="relative rounded-2xl overflow-hidden border border-gray-200 bg-gray-50">
              <Image src={recipe.cover_photo_url} alt={recipe.title} width={1200} height={675} className="w-full h-auto object-cover" />
            </div>
          </div>
        )}

        <h1 className="text-4xl font-serif text-gray-900 mb-2">{recipe.title}</h1>
        <div className="flex items-center gap-2 text-gray-600 mb-4">
          {recipe.users?.profile_picture_url && (
            <Image src={recipe.users.profile_picture_url} alt={recipe.users?.name || 'Author'} width={36} height={36} className="rounded-full border border-gray-200" />
          )}
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span>
                By <a href={`/u/${recipe.user_id}`} className="font-medium text-gray-900 hover:text-blue-600">{recipe.users?.name}</a>
              </span>
              {recipe.users?.has_day1_badge && (
                <Day1Badge signupNumber={recipe.users?.signup_number} badgeTier={recipe.users?.badge_tier} size="sm" clickable={false} />
              )}
              <span className="text-gray-500">• {new Date(recipe.created_at).toLocaleDateString()}</span>
            </div>

            {(!user || user.id === recipe.user_id) && (
              <div className="text-xs text-gray-600">{(recipe.users as any)?.follower_count || 0} followers</div>
            )}
          </div>

          {/* Right-aligned actions: Follow only for non-authors */}
          {user && user.id !== recipe.user_id && (
            <div className="ml-auto">
              <FollowButton writerId={recipe.user_id} writerName={recipe.users?.name} size="sm" />
            </div>
          )}
        </div>

        {/* Purple Accent Badges */}
        <div className="flex flex-wrap items-center gap-2 mb-6">
          <RankBadge />
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-700 border border-purple-200">
            {recipe.is_free ? 'Free' : `Price: $${((recipe.price_cents || 0) / 100).toFixed(2)}`}
          </span>
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-50 text-purple-700 border border-purple-200">
            🍳 Cooked It: {recipe.cooked_count || 0}
          </span>
        </div>

        {recipe.description && (
          <div className="prose max-w-none text-gray-800 mb-6 whitespace-pre-wrap">{recipe.description}</div>
        )}

        {photos && photos.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6">
            {photos.map((p) => (
              <div key={p.id} className="relative border border-gray-200 rounded-2xl overflow-hidden bg-gray-50">
                <Image src={p.url} alt={p.alt_text} width={800} height={600} className="w-full h-auto object-cover" />
              </div>
            ))}
          </div>
        )}

        {videos && videos.length > 0 && (
          <div className="space-y-3 mb-6">
            {videos.map((v) => (
              <div key={v.id} className="relative overflow-hidden rounded-2xl border border-gray-200 bg-black">
                <video controls className="w-full h-full">
                  <source src={v.r2_public_url} />
                </video>
              </div>
            ))}
          </div>
        )}

        {/* Ingredients */}
        {ingredients && ingredients.length > 0 && (
          <div className="mb-8 border border-gray-200 rounded-2xl p-5 bg-white">
            <div className="mb-2">
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-semibold tracking-wide uppercase bg-purple-100 text-purple-700 border border-purple-200">Ingredients</span>
            </div>
            <h2 className="text-2xl font-serif text-gray-900 mb-3">Ingredients</h2>
            {/* Group by section */}
            {(() => {
              const bySection = ingredients.reduce((acc: Record<string, any[]>, it: any) => {
                const key = it.section || ' '
                acc[key] = acc[key] || []
                acc[key].push(it)
                return acc
              }, {})
              return (
                <div className="space-y-4">
                  {Object.entries(bySection).map(([section, list]) => (
                    <div key={section}>
                      {section.trim() !== '' && (
                        <h3 className="font-medium text-gray-800 mb-2">{section}</h3>
                      )}
                      <ul className="list-disc pl-6 space-y-1">
                        {list.map((it: any, idx: number) => (
                          <li key={idx} className="text-gray-800">
                            <span className="font-serif">
                              {it.amount ? `${it.amount} ` : ''}
                              {it.unit ? `${it.unit} ` : ''}
                              {it.item}
                            </span>
                            {it.note ? <span className="text-gray-500"> — {it.note}</span> : null}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              )
            })()}
          </div>
        )}

        {/* Instructions */}
        {steps && steps.length > 0 && (
          <div className="mb-8 border border-gray-200 rounded-2xl p-5 bg-white">
            <div className="mb-2">
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-semibold tracking-wide uppercase bg-purple-100 text-purple-700 border border-purple-200">Instructions</span>
            </div>
            <h2 className="text-2xl font-serif text-gray-900 mb-3">Instructions</h2>
            <ol className="list-decimal pl-6 space-y-4">
              {steps.map((st: any, i: number) => (
                <li key={i} className="text-gray-800">
                  <div className="whitespace-pre-wrap font-serif">{st.instruction}</div>
                  {st.time_hint && (
                    <div className="text-sm text-gray-500 mt-1 italic">{st.time_hint}</div>
                  )}
                </li>
              ))}
            </ol>
          </div>
        )}

        <h2 className="text-2xl font-serif text-gray-900 mb-1">COOKED IT Stories</h2>
        <p className="text-sm text-gray-600 mb-3">Share a quick 9-second clip of your results. Start a COOKED IT to post your mini story.</p>
        <div className="mb-4">
          <CookedItUploader recipeId={recipe.id} />
          <div className="mt-2 text-sm text-gray-700">COOKED IT ({recipe.cooked_count || 0})</div>
        </div>
        {cookEvents && cookEvents.length > 0 ? (
          <div className="space-y-3">
            {cookEvents.map((e) => (
              <div key={e.id} className="border border-gray-200 rounded-lg p-3 text-sm text-gray-700">{new Date(e.created_at).toLocaleString()}</div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No cook stories yet. Be the first!</p>
        )}
      </div>
    </div>
  )
}

