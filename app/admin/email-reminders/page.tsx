'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'

interface UnconfirmedUser {
  id: string
  email: string
  name: string
  created_at: string
  confirmation_sent_at: string
  days_since_signup: number
}

interface EmailStats {
  unconfirmedUsers: number
  recentReminders: number
  lastRemindersSent: string
}

export default function EmailRemindersPage() {
  const [stats, setStats] = useState<EmailStats | null>(null)
  const [users, setUsers] = useState<UnconfirmedUser[]>([])
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set())
  const [results, setResults] = useState<any>(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      
      // Get stats
      const statsResponse = await fetch('/api/send-confirmation-reminders')
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      }

      // Get unconfirmed users directly from Supabase
      const supabase = createSupabaseClient()
      const { data: authUsers, error } = await supabase
        .from('auth.users')
        .select('id, email, raw_user_meta_data, created_at, confirmation_sent_at')
        .is('email_confirmed_at', null)
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) {
        console.error('Error fetching users:', error)
        return
      }

      // Transform the data
      const transformedUsers: UnconfirmedUser[] = authUsers.map(user => {
        const createdAt = new Date(user.created_at)
        const daysSince = Math.floor((Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24))
        
        return {
          id: user.id,
          email: user.email,
          name: user.raw_user_meta_data?.name || 'Unknown',
          created_at: user.created_at,
          confirmation_sent_at: user.confirmation_sent_at,
          days_since_signup: daysSince
        }
      })

      setUsers(transformedUsers)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSelectUser = (userId: string) => {
    const newSelected = new Set(selectedUsers)
    if (newSelected.has(userId)) {
      newSelected.delete(userId)
    } else {
      newSelected.add(userId)
    }
    setSelectedUsers(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedUsers.size === users.length) {
      setSelectedUsers(new Set())
    } else {
      setSelectedUsers(new Set(users.map(u => u.id)))
    }
  }

  const sendReminders = async (dryRun = false) => {
    try {
      setSending(true)
      setResults(null)

      const response = await fetch('/api/send-confirmation-reminders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          dryRun,
          maxEmails: selectedUsers.size > 0 ? selectedUsers.size : 20,
          minHoursSinceLastReminder: 24,
          selectedUserIds: selectedUsers.size > 0 ? Array.from(selectedUsers) : undefined
        })
      })

      const result = await response.json()
      setResults(result)

      if (!dryRun && result.success) {
        // Reload data to update the list
        await loadData()
        setSelectedUsers(new Set())
      }
    } catch (error) {
      console.error('Error sending reminders:', error)
      setResults({ success: false, error: 'Failed to send reminders' })
    } finally {
      setSending(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading unconfirmed users...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Email Confirmation Reminders</h1>
          <p className="text-gray-600">Manually send gentle reminders to users who haven't confirmed their accounts</p>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Unconfirmed Users</h3>
              <p className="text-3xl font-bold text-blue-600">{stats.unconfirmedUsers}</p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Recent Reminders (24h)</h3>
              <p className="text-3xl font-bold text-green-600">{stats.recentReminders}</p>
            </div>
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Selected Users</h3>
              <p className="text-3xl font-bold text-purple-600">{selectedUsers.size}</p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex gap-4">
              <button
                onClick={() => sendReminders(true)}
                disabled={sending}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
              >
                {sending ? 'Processing...' : 'Preview (Dry Run)'}
              </button>
              <button
                onClick={() => sendReminders(false)}
                disabled={sending || selectedUsers.size === 0}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {sending ? 'Sending...' : `Send Reminders (${selectedUsers.size})`}
              </button>
            </div>
            <button
              onClick={handleSelectAll}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              {selectedUsers.size === users.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>
        </div>

        {/* Results */}
        {results && (
          <div className={`rounded-lg p-6 mb-8 ${results.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <h3 className={`font-semibold mb-2 ${results.success ? 'text-green-800' : 'text-red-800'}`}>
              {results.success ? '✅ Success' : '❌ Error'}
            </h3>
            <p className={results.success ? 'text-green-700' : 'text-red-700'}>
              {results.message}
            </p>
            {results.emailsSent > 0 && (
              <p className="text-green-700 mt-1">
                Sent {results.emailsSent} emails, {results.emailsFailed || 0} failed
              </p>
            )}
          </div>
        )}

        {/* Users Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Unconfirmed Users ({users.length})</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedUsers.size === users.length && users.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Signed Up
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Days Ago
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user.id} className={selectedUsers.has(user.id) ? 'bg-blue-50' : ''}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedUsers.has(user.id)}
                        onChange={() => handleSelectUser(user.id)}
                        className="rounded border-gray-300"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{user.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{user.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(user.created_at)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.days_since_signup <= 1 ? 'bg-green-100 text-green-800' :
                        user.days_since_signup <= 7 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {user.days_since_signup} days
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
