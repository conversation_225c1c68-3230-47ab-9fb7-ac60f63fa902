import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { sendEmail, EmailTemplates } from '@/lib/email/resend'

// Use service role key for cron jobs
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    // Verify cron token for security
    const authHeader = request.headers.get('authorization')
    if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('🔄 Processing engagement email notifications...')

    // Get all unsent engagement notifications
    const { data: notifications, error: notificationsError } = await supabase
      .from('user_engagement_notifications')
      .select(`
        id,
        user_id,
        notification_type,
        milestone_count,
        users!inner(
          id,
          name,
          email,
          email_notifications
        )
      `)
      .eq('email_sent', false)
      .eq('users.email_notifications', true) // Only send to users who want emails
      .limit(50) // Process in batches

    if (notificationsError) {
      console.error('Error fetching notifications:', notificationsError)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    if (!notifications || notifications.length === 0) {
      console.log('✅ No engagement notifications to send')
      return NextResponse.json({ message: 'No notifications to send', sent: 0 })
    }

    console.log(`📧 Found ${notifications.length} engagement notifications to send`)

    let sentCount = 0
    let errorCount = 0

    // Process each notification
    for (const notification of notifications) {
      try {
        const user = notification.users
        if (!user.email) {
          console.log(`⚠️ User ${user.id} has no email address`)
          continue
        }

        // Get additional data based on notification type
        let emailData: any = null
        let templateName = ''

        switch (notification.notification_type) {
          case 'followers':
            // Get recent followers
            const { data: followers } = await supabase
              .from('follows')
              .select(`
                users!follows_follower_id_fkey(name, avatar_url)
              `)
              .eq('following_id', user.id)
              .order('created_at', { ascending: false })
              .limit(notification.milestone_count)

            const followerList = followers?.map(f => ({
              name: f.users?.name || 'Anonymous',
              avatar: f.users?.avatar_url
            })) || []

            if (notification.milestone_count === 5) {
              templateName = 'first_followers'
            } else if (notification.milestone_count <= 25) {
              templateName = 'growing_followers'
            } else {
              templateName = 'popular_creator'
            }

            emailData = EmailTemplates.first_followers(
              user.name || 'Creator',
              notification.milestone_count,
              followerList
            )
            break

          case 'comments':
            if (notification.milestone_count === 5) {
              templateName = 'first_comments'
            } else {
              templateName = 'engaging_content'
            }

            emailData = EmailTemplates.first_comments(
              user.name || 'Creator',
              notification.milestone_count
            )
            break

          case 'likes':
            if (notification.milestone_count === 20) {
              templateName = 'first_likes'
            } else {
              templateName = 'loved_content'
            }

            emailData = EmailTemplates.first_likes(
              user.name || 'Creator',
              notification.milestone_count
            )
            break

          default:
            console.log(`⚠️ Unknown notification type: ${notification.notification_type}`)
            continue
        }

        if (!emailData) {
          console.log(`⚠️ No email template for ${notification.notification_type}`)
          continue
        }

        // Send the email
        const emailResult = await sendEmail({
          to: user.email,
          subject: emailData.subject,
          html: emailData.html,
          text: emailData.text,
          tag: emailData.tag
        })

        if (emailResult.success) {
          // Mark as sent
          await supabase
            .from('user_engagement_notifications')
            .update({ email_sent: true })
            .eq('id', notification.id)

          console.log(`✅ Sent ${notification.notification_type} milestone email to ${user.name} (${notification.milestone_count})`)
          sentCount++
        } else {
          console.error(`❌ Failed to send email to ${user.email}:`, emailResult.error)
          errorCount++
        }

        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`❌ Error processing notification ${notification.id}:`, error)
        errorCount++
      }
    }

    console.log(`📊 Engagement email batch complete: ${sentCount} sent, ${errorCount} errors`)

    return NextResponse.json({
      message: 'Engagement emails processed',
      sent: sentCount,
      errors: errorCount,
      total: notifications.length
    })

  } catch (error) {
    console.error('❌ Error in engagement email cron:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint for manual testing
export async function GET() {
  return NextResponse.json({
    message: 'Engagement email cron endpoint',
    status: 'ready',
    timestamp: new Date().toISOString()
  })
}
