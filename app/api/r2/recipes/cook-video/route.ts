import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/client";
import r2Client from "@/lib/r2-client";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { checkRateLimit, createRateLimitResponse, createRateLimitHeaders } from '@/lib/utils/rate-limit'

export async function POST(request: NextRequest) {
  try {
    const rateLimitResult = checkRateLimit(request, 'UPLOAD', 'recipe-cook-video-upload')
    if (!rateLimitResult.allowed) return createRateLimitResponse('UPLOAD', rateLimitResult)

    const { recipeId, eventId, clipIndex, filename, fileSize, mimeType, duration } = await request.json();
    if (!recipeId || !eventId || !clipIndex || !filename) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }
    if (clipIndex < 1 || clipIndex > 3) {
      return NextResponse.json({ error: 'clipIndex must be 1..3' }, { status: 400 })
    }
    if (duration && duration > 9.5) {
      return NextResponse.json({ error: 'Max duration is 9 seconds' }, { status: 400 })
    }

    const MAX_SIZE = 500 * 1024 * 1024
    if (fileSize && fileSize > MAX_SIZE) return NextResponse.json({ error: 'File size must be less than 500MB' }, { status: 400 })

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    // Ensure event belongs to user
    const { data: event } = await supabase
      .from('recipe_cook_events' as any)
      .select('id, user_id, recipe_id')
      .eq('id', eventId)
      .single()

    if (!event || event.user_id !== user.id || event.recipe_id !== recipeId) {
      return NextResponse.json({ error: 'Not authorized for this event' }, { status: 403 })
    }

    const sanitizedFilename = (filename as string).replace(/[^a-zA-Z0-9._-]/g, '_')
    const ts = Date.now()
    const key = `recipe-cook/${user.id}/${recipeId}/${eventId}/${clipIndex}-${ts}-${sanitizedFilename}`

    const command = new PutObjectCommand({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      Key: key,
      ContentType: mimeType || 'video/webm',
      ContentLength: fileSize || undefined,
      Metadata: {
        'recipe-id': recipeId,
        'event-id': eventId,
        'clip-index': String(clipIndex),
        'duration': String(duration || ''),
        'validated': 'true'
      }
    })

    const uploadUrl = await getSignedUrl(r2Client, command, { expiresIn: 3600 })
    const publicUrl = `${process.env.CLOUDFLARE_R2_PUBLIC_URL}/${key}`

    // Insert or upsert the record
    const { error: upErr } = await supabase
      .from('recipe_cook_videos' as any)
      .insert({ cook_event_id: eventId, clip_index: clipIndex, r2_file_key: key, r2_public_url: publicUrl, file_size: fileSize || null, duration_seconds: duration || null })
    if (upErr) return NextResponse.json({ error: upErr.message }, { status: 500 })

    const headers = createRateLimitHeaders(rateLimitResult)
    return NextResponse.json({ uploadUrl, publicUrl }, { headers })
  } catch (e) {
    console.error('cook-video error', e)
    return NextResponse.json({ error: 'Failed to create upload URL' }, { status: 500 })
  }
}

