import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/client";
import r2Client from "@/lib/r2-client";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { checkRateLimit, createRateLimitResponse, createRateLimitHeaders } from '@/lib/utils/rate-limit'
import { logFileEvent, logRateLimitEvent } from '@/lib/utils/security-logger'

export async function POST(request: NextRequest) {
  try {
    const rateLimitResult = checkRateLimit(request, 'UPLOAD', 'recipe-video-upload')
    if (!rateLimitResult.allowed) {
      logRateLimitEvent(request, 'UPLOAD', { endpoint: 'recipe-video-upload' })
      return createRateLimitResponse('UPLOAD', rateLimitResult)
    }

    const { filename, recipeId, fileSize, mimeType } = await request.json();

    if (!filename || !recipeId) {
      return NextResponse.json({ error: "Filename and recipeId are required" }, { status: 400 });
    }

    if (typeof filename !== 'string' || filename.length > 255) {
      return NextResponse.json({ error: "Invalid filename format" }, { status: 400 });
    }

    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9._-]/g, '_');

    const allowedExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm'];
    const fileExtension = sanitizedFilename.toLowerCase().substring(sanitizedFilename.lastIndexOf('.'));
    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return NextResponse.json({ error: "Only MP4, MOV, AVI, MKV, and WebM files are allowed" }, { status: 400 });
    }

    const allowedMimeTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska', 'video/webm'];
    if (mimeType && !allowedMimeTypes.includes(mimeType)) {
      return NextResponse.json({ error: "Invalid file type. Only video files are allowed." }, { status: 400 });
    }

    const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
    if (fileSize && fileSize > MAX_FILE_SIZE) {
      return NextResponse.json({ error: "File size must be less than 500MB" }, { status: 400 });
    }
    if (fileSize && fileSize < 1024) {
      return NextResponse.json({ error: "File appears to be empty or corrupted" }, { status: 400 });
    }

    if (!process.env.CLOUDFLARE_R2_BUCKET_NAME || !process.env.CLOUDFLARE_R2_PUBLIC_URL) {
      return NextResponse.json({ error: "R2 configuration missing" }, { status: 500 });
    }

    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    // Ensure user owns recipe
    const { data: recipe } = await supabase
      .from('recipes')
      .select('id, user_id')
      .eq('id', recipeId)
      .single();

    if (!recipe || recipe.user_id !== user.id) {
      return NextResponse.json({ error: "Not authorized to upload for this recipe" }, { status: 403 });
    }

    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileKey = `recipe-videos/${user.id}/${recipeId}/${timestamp}-${randomString}-${sanitizedFilename}`;

    const contentTypeMap: Record<string, string> = {
      '.mp4': 'video/mp4',
      '.mov': 'video/quicktime',
      '.avi': 'video/x-msvideo',
      '.mkv': 'video/x-matroska',
      '.webm': 'video/webm'
    };
    const contentType = (mimeType && allowedMimeTypes.includes(mimeType)) ? mimeType : (contentTypeMap[fileExtension] || 'video/mp4');

    const command = new PutObjectCommand({
      Bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      Key: fileKey,
      ContentType: contentType,
      ContentLength: fileSize || undefined,
      Metadata: {
        'original-filename': filename,
        'upload-user-id': user.id,
        'upload-timestamp': new Date().toISOString(),
        'file-size': fileSize?.toString() || 'unknown',
        'validated': 'true',
        'recipe-id': recipeId
      }
    });

    const uploadUrl = await getSignedUrl(r2Client, command, { expiresIn: 3600 });
    const publicUrl = `${process.env.CLOUDFLARE_R2_PUBLIC_URL}/${fileKey}`;

    // Insert recipe video record
    const { data: video, error: dbError } = await supabase
      .from('recipe_videos')
      .insert({
        recipe_id: recipeId,
        r2_file_key: fileKey,
        r2_public_url: publicUrl,
        file_size: fileSize || null,
        title: filename.replace(/\.[^/.]+$/, "")
      })
      .select()
      .single();

    if (dbError) {
      return NextResponse.json({ error: "Failed to save video record" }, { status: 500 });
    }

    logFileEvent('upload', request, user.id, fileKey, { filename: sanitizedFilename, fileSize, mimeType, recipeId })
    const headers = createRateLimitHeaders(rateLimitResult)

    return NextResponse.json({ uploadUrl, publicUrl, video }, { headers });
  } catch (error) {
    console.error("Recipe video upload URL error:", error);
    return NextResponse.json({ error: "Failed to generate upload URL" }, { status: 500 });
  }
}

