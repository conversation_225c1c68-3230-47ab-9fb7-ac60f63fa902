import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { stripe, calculateApplicationFee } from '@/lib/stripe'
import { checkApiMonetizationAccess } from '@/lib/stripe/api-monetization-guard'

export async function POST(request: NextRequest) {
  // Check if the book author can monetize
  const monetizationCheck = await checkApiMonetizationAccess(request)
  if (!monetizationCheck.allowed) {
    return NextResponse.json(
      {
        error: 'Book author monetization not enabled',
        reason: monetizationCheck.reason,
        action_required: 'connect_stripe'
      },
      { status: monetizationCheck.statusCode || 403 }
    )
  }
  try {
    const { bookId, priceAmount } = await request.json()

    console.log('Purchase API called with:', { bookId, priceAmount })

    if (!bookId) {
      console.log('Missing bookId:', { bookId, priceAmount })
      return NextResponse.json(
        { error: 'Missing book ID' },
        { status: 400 }
      )
    }

    if (!priceAmount || priceAmount <= 0) {
      console.log('Invalid price amount:', { bookId, priceAmount })
      return NextResponse.json(
        { error: 'Invalid price amount' },
        { status: 400 }
      )
    }

    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get book details
    const { data: book, error: bookError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        price_amount,
        user_id,
        users!inner(name, stripe_account_id)
      `)
      .eq('id', bookId)
      .eq('is_ebook', true)
      .single()

    if (bookError || !book) {
      return NextResponse.json(
        { error: 'Book not found' },
        { status: 404 }
      )
    }

    // Check if user already purchased this book
    const { data: existingPurchase } = await supabase
      .from('book_purchases')
      .select('id')
      .eq('user_id', user.id)
      .eq('project_id', bookId)
      .single()

    if (existingPurchase) {
      return NextResponse.json(
        { error: 'Book already purchased' },
        { status: 400 }
      )
    }

    // Create Stripe checkout session
    console.log('Creating Stripe session for book:', book.title, 'Price:', book.price_amount)
    console.log('Author Stripe account:', book.users.stripe_account_id)

    // Check if author has completed Stripe Connect onboarding
    if (!book.users.stripe_account_id) {
      return NextResponse.json(
        { error: 'Author has not set up payment processing. Please contact the author.' },
        { status: 400 }
      )
    }

    // Validate Stripe Connect account
    let useStripeConnect = false
    try {
      const account = await stripe.accounts.retrieve(book.users.stripe_account_id)
      console.log('Stripe account status:', account.id, account.charges_enabled, account.payouts_enabled)
      useStripeConnect = account.charges_enabled && account.payouts_enabled

      if (!useStripeConnect) {
        return NextResponse.json(
          { error: 'Author payment processing is not fully set up. Please contact the author.' },
          { status: 400 }
        )
      }
    } catch (accountError) {
      console.error('Invalid Stripe Connect account:', book.users.stripe_account_id, accountError.message)
      return NextResponse.json(
        { error: 'Author payment processing is not available. Please contact the author.' },
        { status: 400 }
      )
    }

    // Calculate application fee using new "Percentage + Fixed Fee" model (30% + $0.35 for books)
    const { applicationFeeAmount, writerAmount } = calculateApplicationFee(book.price_amount, 'book')

    console.log('Book purchase fee calculation:', {
      totalAmount: book.price_amount,
      applicationFeeAmount,
      writerAmount,
      applicationFeeInDollars: applicationFeeAmount / 100,
      writerAmountInDollars: writerAmount / 100
    })

    try {
      const sessionConfig = {
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: book.title,
                description: `Digital book by ${book.users.name}`,
                images: [], // Add book cover if available
              },
              unit_amount: book.price_amount,
            },
            quantity: 1,
          },
        ],
        mode: 'payment' as const,
        success_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/books/${bookId}/read?purchase=success`,
        cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/books/${bookId}?purchase=cancelled`,
        metadata: {
          type: 'book_purchase',
          book_id: bookId,
          buyer_id: user.id,
          author_id: book.user_id,
          payment_type: 'book',
          product_type: 'book',
          platform_fee: applicationFeeAmount.toString(),
          writer_amount: writerAmount.toString(),
        },
        payment_intent_data: {
          application_fee_amount: applicationFeeAmount,
          transfer_data: {
            destination: book.users.stripe_account_id,
          },
          metadata: {
            type: 'book_purchase',
            book_id: bookId,
            buyer_id: user.id,
            author_id: book.user_id,
            payment_type: 'book',
            product_type: 'book',
            platform_fee: applicationFeeAmount.toString(),
            writer_amount: writerAmount.toString(),
          }
        }
      }

      const session = await stripe.checkout.sessions.create(sessionConfig)

    console.log('Stripe session created successfully:', session.id, 'URL:', session.url)
    return NextResponse.json({ url: session.url })

    } catch (stripeError) {
      console.error('Stripe session creation failed:', stripeError)
      return NextResponse.json(
        { error: 'Failed to create payment session' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error creating checkout session:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
