import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(req: NextRequest, { params }: { params: Promise<{ bookId: string }> }) {
  const supabase = await createServerSupabaseClient()
  const { bookId } = await params

  // For privacy, hash IP lightly if available (optional; can be null)
  // We won't block on getting IP; we count the view regardless.
  const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || req.ip || null
  const ua = req.headers.get('user-agent') || null

  // Try to detect a session id from cookies to reduce double counts (optional)
  // Not enforcing uniqueness, just storing for future analysis.
  let sessionId: string | null = null
  try {
    const cookieHeader = req.headers.get('cookie') || ''
    const match = /_od_sesh=([^;]+)/.exec(cookieHeader)
    sessionId = match ? decodeURIComponent(match[1]) : null
  } catch {}

  try {
    const { data: { user } } = await supabase.auth.getUser()
    const viewer_id = user?.id ?? null

    const { error } = await supabase.from('book_views' as any).insert({
      project_id: bookId,
      viewer_id,
      session_id: sessionId,
      ip_hash: ip, // in production, consider a hash function
      user_agent: ua,
    })
    if (error) throw error

    return NextResponse.json({ ok: true })
  } catch (err) {
    console.error('track-view error', err)
    // Do not fail hard for view tracking; return 200 to avoid retry storms
    return NextResponse.json({ ok: true })
  }
}

