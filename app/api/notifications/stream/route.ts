import { NextResponse } from 'next/server'

// Kill-switch for legacy SSE endpoint that caused long-lived serverless executions
// We intentionally close immediately to avoid timeouts and costs.
// If any old clients still call this, they will get a short response and stop keeping
// a Serverless Function open for ~60s (which was billed even when idle).
export const dynamic = 'force-dynamic'
export const maxDuration = 1

export async function GET() {
  const res = NextResponse.json({
    ok: false,
    message: 'Notifications stream is deprecated. Use Supabase Realtime instead.'
  }, { status: 410 }) // 410 Gone

  // Ensure the platform closes the connection promptly
  res.headers.set('Connection', 'close')
  res.headers.set('Cache-Control', 'no-store, max-age=0')
  res.headers.set('Retry-After', '120') // hint for naive clients
  return res
}

export async function POST() {
  // Some old clients might POST; respond the same way
  return GET()
}

