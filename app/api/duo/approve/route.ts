import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/utils/rate-limit'

// POST /api/duo/approve
// body: { duoPostId: string }
// Only the initiator can approve; sets status=completed and unhides timeline
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-approve')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId } = await request.json()
    if (!duoPostId) return NextResponse.json({ error: 'duoPostId required' }, { status: 400 })

    const { data: duo } = await supabase
      .from('duo_posts')
      .select('id, initiator_user_id, status')
      .eq('id', duoPostId)
      .single()

    if (!duo) return NextResponse.json({ error: 'Duo not found' }, { status: 404 })
    if (duo.initiator_user_id !== user.id) return NextResponse.json({ error: 'Only initiator can approve' }, { status: 403 })

    // Require that part 2 exists before approving
    const { data: partB } = await supabase
      .from('duo_parts')
      .select('id')
      .eq('duo_post_id', duoPostId)
      .eq('part_number', 2)
      .maybeSingle()

    if (!partB) return NextResponse.json({ error: 'Part B not found' }, { status: 400 })

    const { error: updErr } = await supabase
      .from('duo_posts')
      .update({ status: 'completed' })
      .eq('id', duoPostId)
    if (updErr) return NextResponse.json({ error: 'Failed to complete duo' }, { status: 500 })

    // Unhide timeline row if present
    const { error: showErr } = await supabase
      .from('timeline_posts')
      .update({ is_hidden: false })
      .eq('duo_post_id', duoPostId)
    if (showErr) console.warn('Failed to unhide timeline for duo', duoPostId, showErr)

    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ ok: true }, { headers })
  } catch (e: any) {
    console.error('duo/approve error', e)
    return NextResponse.json({ error: 'Failed to approve duo' }, { status: 500 })
  }
}

