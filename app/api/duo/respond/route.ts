import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/utils/rate-limit'

// POST /api/duo/respond
// body: { duoPostId: string }
// returns: { duoPostId: string, part: 2 }
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-respond')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId } = await request.json()
    if (!duoPostId) return NextResponse.json({ error: 'duoPostId required' }, { status: 400 })

    // Ensure duo exists and is pending
    const { data: duo } = await supabase
      .from('duo_posts')
      .select('id, status')
      .eq('id', duoPostId)
      .single()

    if (!duo) return NextResponse.json({ error: 'Duo not found' }, { status: 404 })
    if (duo.status === 'completed') return NextResponse.json({ error: 'Duo already completed' }, { status: 400 })

    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ duoPostId, part: 2 }, { headers })
  } catch (e: any) {
    console.error('duo/respond error', e)
    return NextResponse.json({ error: 'Failed to respond' }, { status: 500 })
  }
}

