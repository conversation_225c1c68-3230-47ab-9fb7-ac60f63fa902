import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()

    // Fetch latest visible duo timeline posts
    const { data: tl, error: tlErr } = await supabase
      .from('timeline_posts')
      .select(`id, user_id, title, description, is_free, is_hidden, created_at, duo_post_id,
        user:users!user_id ( id, name, avatar, profile_picture_url )
      `)
      .eq('content_type', 'duo')
      .eq('is_hidden', false)
      .order('created_at', { ascending: false })
      .limit(20)

    if (tlErr) return NextResponse.json({ error: tlErr.message }, { status: 500 })
    if (!tl || tl.length === 0) return NextResponse.json({ posts: [] })

    const duoIds = tl.map(p => p.duo_post_id as string)

    // Load duo_posts
    const { data: duos, error: dErr } = await supabase
      .from('duo_posts')
      .select('id, initiator_user_id, responder_user_id, status')
      .in('id', duoIds)
    if (dErr) return NextResponse.json({ error: dErr.message }, { status: 500 })

    // Load parts
    const { data: parts, error: pErr } = await supabase
      .from('duo_parts')
      .select('duo_post_id, part_number, author_user_id, media')
      .in('duo_post_id', duoIds)
    if (pErr) return NextResponse.json({ error: pErr.message }, { status: 500 })

    // Resolve initiator/responder profiles
    const userIds = new Set<string>()
    duos?.forEach(d => { userIds.add(d.initiator_user_id); if (d.responder_user_id) userIds.add(d.responder_user_id) })

    const { data: users, error: uErr } = await supabase
      .from('users')
      .select('id, name, avatar, profile_picture_url')
      .in('id', Array.from(userIds))
    if (uErr) return NextResponse.json({ error: uErr.message }, { status: 500 })

    const userMap = new Map(users?.map(u => [u.id, u]))

    // Compose posts
    const posts = tl.map(item => {
      const d = duos!.find(x => x.id === item.duo_post_id)
      const pA = parts?.find(p => p.duo_post_id === item.duo_post_id && p.part_number === 1)
      const pB = parts?.find(p => p.duo_post_id === item.duo_post_id && p.part_number === 2)
      const initiator = d ? userMap.get(d.initiator_user_id) : undefined
      const responder = d?.responder_user_id ? userMap.get(d.responder_user_id) : undefined
      return {
        id: item.id,
        content_type: 'duo',
        title: item.title,
        description: item.description,
        is_free: item.is_free,
        created_at: item.created_at,
        user: item.user,
        duo_post: {
          id: item.duo_post_id,
          status: d?.status || 'pending',
          initiator_user: initiator ? { id: initiator.id, name: initiator.name, profile_picture_url: initiator.profile_picture_url } : { id: d?.initiator_user_id, name: 'User', profile_picture_url: null },
          responder_user: responder ? { id: responder.id, name: responder.name, profile_picture_url: responder.profile_picture_url } : undefined,
          partA: pA ? { part_number: 1 as const, author_user_id: pA.author_user_id, media: pA.media } : undefined,
          partB: pB ? { part_number: 2 as const, author_user_id: pB.author_user_id, media: pB.media } : undefined,
        }
      }
    })

    return NextResponse.json({ posts })
  } catch (e: any) {
    console.error('duo/timeline error', e)
    return NextResponse.json({ error: 'Failed to load duo timeline' }, { status: 500 })
  }
}

