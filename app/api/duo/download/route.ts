import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'
import { analytics, trackOnlyDuoDownloaded, measureApiCall } from '@/lib/analytics'
import { videoQueue } from '@/lib/video-processing'

// POST /api/duo/download
// body: { duoPostId: string, includeWatermark?: boolean, includeAttribution?: boolean }
// Returns a combined video file with watermark and attribution
export async function POST(request: NextRequest) {
  return measureApiCall('/api/duo/download', 'POST', async () => {
    let duoPostId: string | undefined
    let user: any

    try {
      const rate = checkRateLimit(request, 'UPLOAD', 'duo-download')
      if (!rate.allowed) return createRateLimitResponse('UPLOAD', rate)

      const supabase = await createServerSupabaseClient()
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()
      if (authError || !authUser) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

      user = authUser

      const { duoPostId: requestDuoPostId, includeWatermark = true, includeAttribution = true } = await request.json()
      duoPostId = requestDuoPostId
      if (!duoPostId) return NextResponse.json({ error: 'duoPostId required' }, { status: 400 })

    // Get the duo and verify access
    const { data: duo, error: duoError } = await supabase
      .from('duo_posts')
      .select(`
        id, status, initiator_user_id, responder_user_id,
        initiator:users!duo_posts_initiator_user_id_fkey(name, profile_picture_url),
        responder:users!duo_posts_responder_user_id_fkey(name, profile_picture_url)
      `)
      .eq('id', duoPostId)
      .single()

    if (duoError || !duo) {
      return NextResponse.json({ error: 'Duo not found' }, { status: 404 })
    }

    // Verify user has access (initiator or responder)
    if (duo.initiator_user_id !== user.id && duo.responder_user_id !== user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Get both parts
    const { data: parts, error: partsError } = await supabase
      .from('duo_parts')
      .select('part_number, media, author_user_id')
      .eq('duo_post_id', duoPostId)
      .order('part_number')

    if (partsError || !parts || parts.length < 2) {
      return NextResponse.json({ error: 'Incomplete duo - missing parts' }, { status: 400 })
    }

    const partA = parts.find(p => p.part_number === 1)
    const partB = parts.find(p => p.part_number === 2)

    if (!partA || !partB) {
      return NextResponse.json({ error: 'Missing required parts' }, { status: 400 })
    }

    // Process the full OnlyDuo with concatenation, watermarks, and attribution
    const baseUrl = process.env.NEXT_PUBLIC_R2_PUBLIC_URL || ''
    const partAUrl = `${baseUrl}/${partA.media.src_r2_key}`
    const partBUrl = `${baseUrl}/${partB.media.src_r2_key}`

    try {
      // Process the video with full OnlyDuo experience
      const processedVideo = await videoQueue.addJob({
        duoId: duoPostId,
        partAUrl,
        partBUrl,
        initiatorName: (duo.initiator as any)?.name || 'User',
        responderName: (duo.responder as any)?.name || 'User',
        initiatorPhoto: (duo.initiator as any)?.profile_picture_url,
        responderPhoto: (duo.responder as any)?.profile_picture_url,
        includeWatermark: includeWatermark,
        includeAttribution: includeAttribution
      })

      // Track successful download
      trackOnlyDuoDownloaded(duoPostId, user.id)

      // Return the processed video file
      return new NextResponse(processedVideo.buffer, {
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Disposition': `attachment; filename="${processedVideo.filename}"`,
          'Content-Length': processedVideo.buffer.byteLength.toString(),
          'X-Video-Duration': processedVideo.duration.toString(),
          'X-Video-Size': processedVideo.size.toString(),
        },
      })
    } catch (processingError) {
      console.error('Failed to process video:', processingError)

      // Fallback to Part A only if processing fails
      try {
        const videoResponse = await fetch(partAUrl)
        if (!videoResponse.ok) throw new Error('Fallback failed')

        const videoBuffer = await videoResponse.arrayBuffer()
        return new NextResponse(videoBuffer, {
          headers: {
            'Content-Type': 'video/mp4',
            'Content-Disposition': `attachment; filename="onlyduo-part-a-${duoPostId}.mp4"`,
            'Content-Length': videoBuffer.byteLength.toString(),
          },
        })
      } catch (fallbackError) {
        return NextResponse.json({ error: 'Video processing failed and fallback unavailable' }, { status: 500 })
      }
    }

    } catch (error) {
      analytics.trackError(error as Error, {
        endpoint: '/api/duo/download',
        duoPostId,
        userId: user?.id
      })
      console.error('Error in duo download:', error)
      return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
  })
}

// TODO: Implement proper video concatenation with FFmpeg
// async function generateCombinedVideo(partA: any, partB: any, options: any): Promise<Buffer> {
//   // 1. Download both video files from R2
//   // 2. Use FFmpeg to concatenate videos
//   // 3. Add watermark overlay
//   // 4. Add author attribution overlays
//   // 5. Return combined video buffer
// }
