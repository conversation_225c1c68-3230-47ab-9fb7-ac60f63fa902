import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient as createSupabaseServerClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'

// POST /api/duo/submit-response
// body: { duoPostId: string }
// Changes status from 'pending' to 'awaiting_approval' and sends notification
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-submit-response')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createSupabaseServerClient()
    const s = supabase as any
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId } = await request.json()
    if (!duoPostId) return NextResponse.json({ error: 'duoPostId required' }, { status: 400 })

    // Verify the user is the responder for this duo
    const { data: duo, error: duoError } = await s
      .from('duo_posts')
      .select('id, initiator_user_id, responder_user_id, status')
      .eq('id', duoPostId)
      .single()

    if (duoError || !duo) {
      return NextResponse.json({ error: 'Duo not found' }, { status: 404 })
    }

    if (duo.responder_user_id !== user.id) {
      return NextResponse.json({ error: 'Not authorized to submit response for this duo' }, { status: 403 })
    }

    if (duo.status !== 'pending') {
      return NextResponse.json({ error: 'Duo is not in pending status' }, { status: 400 })
    }

    // Verify Part B exists and was uploaded by the responder
    const { data: partB, error: partBError } = await s
      .from('duo_parts')
      .select('id, author_user_id, media')
      .eq('duo_post_id', duoPostId)
      .eq('part_number', 2)
      .eq('author_user_id', user.id)
      .single()

    if (partBError || !partB) {
      return NextResponse.json({ error: 'Part B not found or not uploaded by responder' }, { status: 400 })
    }

    // Update duo status to awaiting_approval
    const { error: updateError } = await s
      .from('duo_posts')
      .update({ 
        status: 'awaiting_approval',
        updated_at: new Date().toISOString()
      })
      .eq('id', duoPostId)

    if (updateError) {
      return NextResponse.json({ error: 'Failed to update duo status' }, { status: 500 })
    }

    // Get initiator info for notification
    const { data: initiator } = await supabase
      .from('users')
      .select('name, email')
      .eq('id', duo.initiator_user_id)
      .single()

    const { data: responder } = await supabase
      .from('users')
      .select('name')
      .eq('id', user.id)
      .single()

    // Send notification to initiator
    if (initiator && responder) {
      await s
        .from('notifications')
        .insert({
          user_id: duo.initiator_user_id,
          type: 'duo_response',
          title: `${responder.name} responded to your OnlyDuo!`,
          body: 'Review and approve their Part B to publish your collaboration.',
          data: {
            duo_post_id: duoPostId,
            url: `/duo/${duoPostId}`
          }
        })

      // Send email notification
      try {
        await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/send-email`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            to: initiator.email,
            subject: `${responder.name} responded to your OnlyDuo`,
            html: `
              <div style="font-family: system-ui, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #1f2937;">Great news! 🎉</h2>
                <p><strong>${responder.name}</strong> has responded to your OnlyDuo with their Part B video.</p>
                <p>You can now review both parts and approve the collaboration to publish it to your timelines.</p>
                <div style="margin: 24px 0;">
                  <a href="${process.env.NEXT_PUBLIC_BASE_URL}/duo/${duoPostId}" 
                     style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block;">
                    Review & Approve OnlyDuo
                  </a>
                </div>
                <p style="color: #6b7280; font-size: 14px;">
                  This is an automated message from OnlyDiary. The collaboration will appear on both timelines once approved.
                </p>
              </div>
            `
          })
        })
      } catch (emailError) {
        console.error('Failed to send email notification:', emailError)
        // Don't fail the request if email fails
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Response submitted successfully. Awaiting initiator approval.' 
    })

  } catch (error) {
    console.error('Error in submit-response:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
