import { NextRequest, NextResponse } from 'next/server'
import { PutObjectCommand } from '@aws-sdk/client-s3'
import r2VideoClient from '@/lib/r2-video-client'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/utils/rate-limit'

// POST /api/duo/direct-upload
// form-data: file: File (mp4/aac), duoPostId: string, partNumber: 1|2
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'UPLOAD', 'duo-direct-upload')
    if (!rate.allowed) return createRateLimitResponse('UPLOAD', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const form = await request.formData()
    const file = form.get('file') as unknown as File | null
    const duoPostId = String(form.get('duoPostId') || '')
    const partRaw = form.get('partNumber')
    const partNumber = typeof partRaw === 'string' ? parseInt(partRaw, 10) : Number(partRaw)

    if (!file || !duoPostId || ![1, 2].includes(partNumber)) {
      return NextResponse.json({ error: 'Invalid input' }, { status: 400 })
    }

    const MAX_SIZE = 120 * 1024 * 1024 // 120MB
    if (file.size > MAX_SIZE) {
      return NextResponse.json({ error: 'Max file size is 120MB' }, { status: 400 })
    }

    const allowedMime = ['video/mp4', 'audio/mp4', 'audio/aac', 'audio/m4a']
    const mimeType = (file as any).type || 'video/mp4'
    if (mimeType && !allowedMime.includes(mimeType)) {
      return NextResponse.json({ error: 'Invalid mimeType. Use MP4/AAC' }, { status: 400 })
    }

    // Verify duo and authorization
    const { data: duo } = await supabase
      .from('duo_posts' as any)
      .select('id, initiator_user_id, responder_user_id, status')
      .eq('id', duoPostId)
      .maybeSingle()

    if (!duo) return NextResponse.json({ error: 'Duo not found' }, { status: 404 })

    const isPart1 = partNumber === 1
    const allowedUser = isPart1 ? (duo as any).initiator_user_id : ((duo as any).responder_user_id ?? user.id)
    if (user.id !== allowedUser && !(partNumber === 2 && !(duo as any).responder_user_id)) {
      return NextResponse.json({ error: 'Not authorized for this part' }, { status: 403 })
    }

    const safeName = (file as any).name ? String((file as any).name).replace(/[^a-zA-Z0-9._-]/g, '_') : 'upload.mp4'
    const key = `u/${user.id}/posts/${duoPostId}/part${partNumber}/src.mp4`

    const targetBucket = process.env.CLOUDFLARE_R2_VIDEO_BUCKET_NAME || process.env.CLOUDFLARE_R2_BUCKET_NAME
    if (!targetBucket) {
      console.error('R2 bucket env var missing (CLOUDFLARE_R2_VIDEO_BUCKET_NAME or CLOUDFLARE_R2_BUCKET_NAME)')
      return NextResponse.json({ error: 'Storage not configured' }, { status: 500 })
    }

    const arrayBuffer = await file.arrayBuffer()
    const body = Buffer.from(arrayBuffer)

    const put = new PutObjectCommand({
      Bucket: targetBucket,
      Key: key,
      Body: body,
      ContentType: mimeType,
      ContentLength: body.byteLength,
      Metadata: {
        'duo-post-id': duoPostId,
        'part-number': String(partNumber),
        'upload-user-id': user.id,
        'original-filename': safeName,
      },
    })

    await r2VideoClient.send(put)

    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ ok: true, r2Key: key, bucket: targetBucket }, { headers })
  } catch (e: any) {
    console.error('duo/direct-upload error', e)
    return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 })
  }
}
