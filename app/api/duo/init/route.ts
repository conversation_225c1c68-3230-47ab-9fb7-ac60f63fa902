import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/utils/rate-limit'

// POST /api/duo/init
// body: { title?: string, body?: string, invite_code?: string, invitee_user_id?: string }
// returns: { duoPostId: string, part: 1 }
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-init')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { title, body, invite_code, invitee_user_id } = await request.json() as {
      title?: string
      body?: string
      invite_code?: string | null
      invitee_user_id?: string | null
    }

    // If invite_code provided, ensure it exists (FK will also enforce)
    if (invite_code) {
      const { data: invite } = await supabase
        .from('invites')
        .select('invite_code')
        .eq('invite_code', invite_code)
        .maybeSingle()
      if (!invite) {
        return NextResponse.json({ error: 'Invalid invite_code' }, { status: 400 })
      }
    }

    // Create duo_posts row (status defaults to pending)
    const { data: duo, error: duoErr } = await supabase
      .from('duo_posts')
      .insert({
        initiator_user_id: user.id,
        responder_user_id: invitee_user_id ?? null,
        title: title ?? null,
        body: body ?? null,
        invite_code: invite_code ?? null,
        status: 'pending'
      })
      .select('id')
      .single()

    if (duoErr || !duo) {
      return NextResponse.json({ error: 'Failed to create duo', details: duoErr?.message }, { status: 500 })
    }

    // If on-platform invitee specified, create a correspondence notification
    if (invitee_user_id) {
      // Get initiator name
      const { data: initiator } = await supabase
        .from('users')
        .select('id, name')
        .eq('id', user.id)
        .single()

      await supabase
        .from('notifications')
        .insert({
          user_id: invitee_user_id,
          type: 'duo_invite',
          title: 'You\'ve been invited to a Duo',
          body: `${initiator?.name || 'Someone'} invited you to continue their Duo`,
          data: {
            duo_post_id: duo.id,
            url: `/duo/respond/${duo.id}`
          }
        })
    }

    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ duoPostId: duo.id, part: 1 }, { headers })
  } catch (e: any) {
    console.error('duo/init error', e)
    return NextResponse.json({ error: 'Failed to init duo' }, { status: 500 })
  }
}

