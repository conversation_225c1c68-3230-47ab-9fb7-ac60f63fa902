import { NextRequest, NextResponse } from 'next/server'
import { PutObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import r2VideoClient from '@/lib/r2-video-client'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/utils/rate-limit'

// POST /api/duo/presign-upload
// body: { duoPostId: string, partNumber: 1|2, filename: string, sizeBytes: number, mimeType: string }
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'UPLOAD', 'duo-presign-upload')
    if (!rate.allowed) return createRateLimitResponse('UPLOAD', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId, partNumber, filename, sizeBytes, mimeType } = await request.json()
    if (!duoPostId || ![1,2].includes(partNumber) || !filename || !sizeBytes) {
      return NextResponse.json({ error: 'Invalid input' }, { status: 400 })
    }

    // Constraints
    const MAX_SIZE = 120 * 1024 * 1024 // 120MB
    if (sizeBytes > MAX_SIZE) return NextResponse.json({ error: 'Max file size is 120MB' }, { status: 400 })

    const allowedMime = ['video/mp4', 'audio/mp4', 'audio/aac', 'audio/m4a']
    if (mimeType && !allowedMime.includes(mimeType)) {
      return NextResponse.json({ error: 'Invalid mimeType. Use MP4/AAC' }, { status: 400 })
    }

    // Verify duo exists and auth is allowed to upload this part
    const { data: duo } = await supabase
      .from('duo_posts')
      .select('id, initiator_user_id, responder_user_id, status')
      .eq('id', duoPostId)
      .single()
    if (!duo) return NextResponse.json({ error: 'Duo not found' }, { status: 404 })

    const isPart1 = partNumber === 1
    const allowedUser = isPart1 ? duo.initiator_user_id : (duo.responder_user_id ?? user.id)
    if (user.id !== allowedUser && !(partNumber === 2 && !duo.responder_user_id)) {
      return NextResponse.json({ error: 'Not authorized for this part' }, { status: 403 })
    }

    // Key pattern
    const safeName = String(filename).replace(/[^a-zA-Z0-9._-]/g, '_')
    const key = `u/${user.id}/posts/${duoPostId}/part${partNumber}/src.mp4`

    const targetBucket = process.env.CLOUDFLARE_R2_VIDEO_BUCKET_NAME || process.env.CLOUDFLARE_R2_BUCKET_NAME

    const command = new PutObjectCommand({
      Bucket: targetBucket,
      Key: key,
      ContentType: mimeType || 'video/mp4',
      ContentLength: sizeBytes,
      Metadata: {
        'duo-post-id': duoPostId,
        'part-number': String(partNumber),
        'upload-user-id': user.id,
        'original-filename': safeName,
      },
    })

    const uploadUrl = await getSignedUrl(r2VideoClient, command, { expiresIn: 900 }) // 15 min
    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ uploadUrl, r2Key: key, bucket: targetBucket }, { headers })
  } catch (e: any) {
    console.error('duo/presign-upload error', e)
    return NextResponse.json({ error: 'Failed to prepare upload' }, { status: 500 })
  }
}
