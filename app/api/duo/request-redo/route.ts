import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'

// POST /api/duo/request-redo
// body: { duoPostId: string }
// Sends a notification to the responder asking them to redo their Part B
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-request-redo')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId } = await request.json()
    if (!duoPostId) return NextResponse.json({ error: 'duoPostId required' }, { status: 400 })

    // Verify the user is the initiator for this duo
    const { data: duo, error: duoError } = await supabase
      .from('duo_posts')
      .select('id, initiator_user_id, responder_user_id, status')
      .eq('id', duoPostId)
      .single()

    if (duoError || !duo) {
      return NextResponse.json({ error: 'Duo not found' }, { status: 404 })
    }

    if (duo.initiator_user_id !== user.id) {
      return NextResponse.json({ error: 'Only the initiator can request a redo' }, { status: 403 })
    }

    if (!duo.responder_user_id) {
      return NextResponse.json({ error: 'No responder to send redo request to' }, { status: 400 })
    }

    if (duo.status !== 'awaiting_approval') {
      return NextResponse.json({ error: 'Can only request redo for duos awaiting approval' }, { status: 400 })
    }

    // Get user names for the notification
    const { data: initiator } = await supabase
      .from('users')
      .select('name')
      .eq('id', user.id)
      .single()

    const { data: responder } = await supabase
      .from('users')
      .select('name, email')
      .eq('id', duo.responder_user_id)
      .single()

    if (!initiator || !responder) {
      return NextResponse.json({ error: 'User data not found' }, { status: 404 })
    }

    // Delete the current Part B and any related data to allow for redo
    const { error: deleteError } = await supabase
      .from('duo_parts')
      .delete()
      .eq('duo_post_id', duoPostId)
      .eq('part_number', 2)

    if (deleteError) {
      console.error('Error deleting Part B:', deleteError)
      return NextResponse.json({ error: 'Failed to reset Part B' }, { status: 500 })
    }

    // Also delete any final combined video that might exist
    const { error: deleteVideoError } = await supabase
      .from('duo_posts')
      .update({
        final_video_url: null,
        final_video_r2_key: null
      })
      .eq('id', duoPostId)

    if (deleteVideoError) {
      console.error('Error clearing final video:', deleteVideoError)
      // Don't fail the request for this, just log it
    }

    // Reset status to pending for fresh cycle (keep responder_user_id for notification)
    const { error: updateError } = await supabase
      .from('duo_posts')
      .update({
        status: 'pending',
        updated_at: new Date().toISOString()
      })
      .eq('id', duoPostId)

    if (updateError) {
      console.error('Error updating duo status:', updateError)
      return NextResponse.json({ error: 'Failed to update duo status' }, { status: 500 })
    }

    // Send notification to responder
    await supabase
      .from('notifications')
      .insert({
        user_id: duo.responder_user_id,
        type: 'duo_redo_request',
        title: `${initiator.name} requested a redo`,
        body: 'They would like you to record a new Part B for your OnlyDuo collaboration.',
        data: {
          duo_post_id: duoPostId,
          url: `/duo/respond/${duoPostId}`
        }
      })

    // Send email notification
    try {
      await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/send-email`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: responder.email,
          subject: `${initiator.name} requested a redo for your OnlyDuo`,
          html: `
            <div style="font-family: system-ui, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #1f2937;">OnlyDuo Redo Request 🔄</h2>
              <p><strong>${initiator.name}</strong> has requested that you redo your Part B video for your OnlyDuo collaboration.</p>
              <p>Don't worry - this happens! They'd like you to record a new response video.</p>
              <div style="margin: 24px 0;">
                <a href="${process.env.NEXT_PUBLIC_BASE_URL}/duo/respond/${duoPostId}" 
                   style="background: #f97316; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block;">
                  Record New Part B
                </a>
              </div>
              <p style="color: #6b7280; font-size: 14px;">
                This is an automated message from OnlyDiary. Your previous Part B has been removed so you can upload a new one.
              </p>
            </div>
          `
        })
      })
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError)
      // Don't fail the request if email fails
    }

    console.log(`✅ Redo request processed for duo ${duoPostId}:`)
    console.log(`   - Part B deleted`)
    console.log(`   - Status reset to 'pending'`)
    console.log(`   - Notification sent to ${responder.email}`)

    return NextResponse.json({
      success: true,
      message: 'Redo request sent successfully. The responder will be notified to record a new Part B.',
      duoId: duoPostId,
      status: 'pending'
    })

  } catch (error) {
    console.error('Error in request-redo:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
