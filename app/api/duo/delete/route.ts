import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/utils/rate-limit'

// POST /api/duo/delete
// body: { duoPostId: string }
// Only the initiator can delete. We soft-delete (status = 'deleted'),
// hide any timeline rows, and attempt to remove duo_parts rows.
// Completed duos cannot be deleted via this endpoint.
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-delete')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId } = await request.json()
    if (!duoPostId) return NextResponse.json({ error: 'duoPostId required' }, { status: 400 })

    // Verify duo exists and the caller is the initiator
    const { data: duo, error: duoErr } = await supabase
      .from('duo_posts' as any)
      .select('id, initiator_user_id, status')
      .eq('id', duoPostId)
      .maybeSingle()

    if (duoErr) {
      console.error('duo/delete: fetch duo error', duoErr)
      return NextResponse.json({ error: 'Failed to fetch duo' }, { status: 500 })
    }
    if (!duo) return NextResponse.json({ error: 'Duo not found' }, { status: 404 })

    if ((duo as any).initiator_user_id !== user.id) {
      return NextResponse.json({ error: 'Only initiator can delete this duo' }, { status: 403 })
    }

    if ((duo as any).status === 'completed') {
      return NextResponse.json({ error: 'Cannot delete a completed duo' }, { status: 400 })
    }

    // Soft-delete: mark duo as deleted (keeps referential integrity)
    const { error: updErr } = await supabase
      .from('duo_posts' as any)
      .update({ status: 'deleted' })
      .eq('id', duoPostId)
    if (updErr) {
      console.error('duo/delete: failed to mark deleted', updErr)
      return NextResponse.json({ error: 'Failed to delete duo' }, { status: 500 })
    }

    // Best-effort: hide any timeline rows (if present)
    const { error: hideErr } = await supabase
      .from('timeline_posts' as any)
      .update({ is_hidden: true })
      .eq('duo_post_id', duoPostId)
    if (hideErr) {
      console.warn('duo/delete: failed to hide timeline post', hideErr)
    }

    // Best-effort: remove parts for this duo (policies may restrict; ignore failures)
    const { error: delPartsErr } = await supabase
      .from('duo_parts' as any)
      .delete()
      .eq('duo_post_id', duoPostId)
    if (delPartsErr) {
      console.warn('duo/delete: failed to delete duo parts', delPartsErr)
    }

    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ ok: true }, { headers })
  } catch (e: any) {
    console.error('duo/delete error', e)
    return NextResponse.json({ error: 'Failed to delete duo' }, { status: 500 })
  }
}
