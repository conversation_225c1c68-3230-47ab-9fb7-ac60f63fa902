import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { checkRateLimit, createRateLimitHeaders, createRateLimitResponse } from '@/lib/utils/rate-limit'
import { sendEmail } from '@/lib/email/resend'

// POST /api/duo/commitPart
// body: { duoPostId: string, partNumber: 1|2, media: jsonb }
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-commit-part')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoPostId, partNumber, media } = await request.json()
    if (!duoPostId || ![1,2].includes(partNumber)) {
      return NextResponse.json({ error: 'duoPostId and valid partNumber required' }, { status: 400 })
    }
    // Require meaningful media for Part B to avoid phantom responses
    if (partNumber === 2 && (!media || Object.keys(media || {}).length === 0)) {
      return NextResponse.json({ error: 'media required for Part B' }, { status: 400 })
    }

    // Fetch duo and determine author for this part
    const { data: duo } = await supabase
      .from('duo_posts')
      .select('id, initiator_user_id, responder_user_id, status')
      .eq('id', duoPostId)
      .single()

    if (!duo) return NextResponse.json({ error: 'Duo not found' }, { status: 404 })

    // Enforce role-based authorship for parts to prevent initiator uploading Part B
    if (partNumber === 1) {
      if (user.id !== duo.initiator_user_id) {
        return NextResponse.json({ error: 'Only the initiator can upload Part A' }, { status: 403 })
      }
    } else {
      // Part 2 must be uploaded by the responder, not the initiator
      if (user.id === duo.initiator_user_id) {
        return NextResponse.json({ error: 'Only your invited friend can upload Part B' }, { status: 403 })
      }
      // If no responder is bound yet, bind the first responder
      if (!duo.responder_user_id) {
        const { error: updErr } = await supabase
          .from('duo_posts')
          .update({ responder_user_id: user.id })
          .eq('id', duoPostId)
        if (updErr) return NextResponse.json({ error: 'Failed to bind responder' }, { status: 500 })
      } else if (user.id !== duo.responder_user_id) {
        return NextResponse.json({ error: 'Not authorized to upload Part B' }, { status: 403 })
      }
    }

    // Upsert the part with media
    const { error: upErr } = await supabase
      .from('duo_parts')
      .upsert({
        duo_post_id: duoPostId,
        part_number: partNumber,
        author_user_id: user.id,
        media
      }, {
        onConflict: 'duo_post_id,part_number'
      })

    if (upErr) return NextResponse.json({ error: 'Failed to save part', details: upErr.message }, { status: 500 })

    // If part 2 saved, require initiator approval before going live
    if (partNumber === 2) {
      const { error: updErr2 } = await supabase
        .from('duo_posts')
        .update({ status: 'awaiting_approval' })
        .eq('id', duoPostId)
      if (updErr2) return NextResponse.json({ error: 'Failed to set awaiting approval' }, { status: 500 })

      // Hide timeline card until approval (no-op if row absent)
      await supabase
        .from('timeline_posts')
        .update({ is_hidden: true })
        .eq('duo_post_id', duoPostId)

      // Notify initiator via Correspondence notifications
      await supabase.from('notifications').insert({
        user_id: duo.initiator_user_id,
        type: 'duo_response_ready',
        title: 'Your Duo has a new response',
        body: 'Review and approve your Duo to publish it to your timeline',
        data: { duo_post_id: duoPostId, url: `/duo/${duoPostId}` }
      })

      // Email initiator (best-effort) to approve
      try {
        const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://onlydiary.app'
        const manageUrl = `${siteUrl}/duo/${duoPostId}`
        const { data: initiator } = await supabase
          .from('users' as any)
          .select('email, name')
          .eq('id', duo.initiator_user_id)
          .maybeSingle()
        const initiatorRow = initiator as any
        if (initiatorRow?.email) {
          await sendEmail({
            to: initiatorRow.email,
            subject: 'Your OnlyDuo has a new response — approve to publish',
            html: `<p>Your OnlyDuo is ready for approval.</p><p><a href="${manageUrl}">Review & Approve</a></p>`,
            text: `Your OnlyDuo is ready for approval. Review: ${manageUrl}`,
            tag: 'duo-response-ready'
          })
        }
      } catch (e) {
        console.warn('Email send (duo response ready) failed:', e)
      }
    }

    const headers = createRateLimitHeaders(rate)
    return NextResponse.json({ ok: true }, { headers })
  } catch (e: any) {
    console.error('duo/commitPart error', e)
    return NextResponse.json({ error: 'Failed to commit part' }, { status: 500 })
  }
}
