import { NextRequest, NextResponse } from 'next/server'

// Securely call Facebook Conversions API from the server
// Expects a POST body compatible with FB's /events endpoint shape
// Docs: https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/
export async function POST(req: NextRequest) {
  try {
    const token = process.env.FB_CAPI_ACCESS_TOKEN
    const pixelId = process.env.FB_PIXEL_ID

    if (!token || !pixelId) {
      return NextResponse.json({ error: 'Server is missing FB_CAPI_ACCESS_TOKEN or FB_PIXEL_ID' }, { status: 500 })
    }

    const body = await req.json()

    // Basic validation: require data array
    if (!body || !Array.isArray(body.data)) {
      return NextResponse.json({ error: 'Invalid payload: expected { data: [...] }' }, { status: 400 })
    }

    // Optionally enrich with server-side context
    const ip = req.ip ||
      req.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
      req.headers.get('x-real-ip') || undefined
    const userAgent = req.headers.get('user-agent') || undefined

    // Add action_source if missing and pass through ip/ua hints at top-level per-event
    const events = body.data.map((evt: any) => {
      // Sanitize user_data arrays (remove null/empty)
      let user_data = evt.user_data
      if (user_data && typeof user_data === 'object') {
        const cleaned: Record<string, any> = {}
        for (const key of Object.keys(user_data)) {
          const val = (user_data as any)[key]
          if (Array.isArray(val)) {
            const arr = val.filter((v) => v != null && String(v).trim() !== '')
            if (arr.length) cleaned[key] = arr
          } else if (val != null && String(val).trim() !== '') {
            cleaned[key] = val
          }
        }
        user_data = cleaned
      }

      return {
        action_source: evt.action_source || 'website',
        ...evt,
        event_time: evt.event_time ? Math.trunc(Number(evt.event_time)) : Math.trunc(Date.now() / 1000),
        user_data,
        client_user_agent: evt.client_user_agent || userAgent,
        client_ip_address: evt.client_ip_address || ip,
      }
    })

    const url = new URL(`https://graph.facebook.com/v18.0/${pixelId}/events`)
    url.searchParams.set('access_token', token)
    if (body.test_event_code) {
      url.searchParams.set('test_event_code', String(body.test_event_code))
    }

    const res = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: events }),
    })

    const json = await res.json()

    if (!res.ok) {
      return NextResponse.json({ error: 'FB error', details: json }, { status: res.status })
    }

    return NextResponse.json(json)
  } catch (err) {
    console.error('CAPI proxy error', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

