import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { recipeId } = await request.json()
    if (!recipeId) return NextResponse.json({ error: 'recipeId required' }, { status: 400 })

    // Create cook event record
    const { data, error } = await supabase
      .from('recipe_cook_events' as any)
      .insert({ recipe_id: recipeId, user_id: user.id })
      .select('id')
      .single()

    if (error) return NextResponse.json({ error: error.message }, { status: 500 })

    return NextResponse.json({ eventId: data.id })
  } catch (e) {
    console.error('cook event error', e)
    return NextResponse.json({ error: 'Failed to create cook event' }, { status: 500 })
  }
}

