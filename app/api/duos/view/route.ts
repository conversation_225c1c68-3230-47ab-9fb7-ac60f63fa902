import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'

// POST /api/duos/view
// Track a view for a duo
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-view')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    const { duoId } = await request.json()

    if (!duoId) {
      return NextResponse.json({ error: 'Duo ID is required' }, { status: 400 })
    }

    // Increment view count in timeline_posts
    const { error } = await supabase.rpc('increment_duo_views', {
      target_duo_post_id: duoId
    })

    if (error) {
      console.error('Error incrementing duo views:', error)
      return NextResponse.json({ error: 'Failed to track view' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in duos/view:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
