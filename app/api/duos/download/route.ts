import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'

// POST /api/duos/download
// Track a download for a duo
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-download')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    const { duoId } = await request.json()

    if (!duoId) {
      return NextResponse.json({ error: 'Duo ID is required' }, { status: 400 })
    }

    // Increment download count in timeline_posts
    const { error } = await supabase.rpc('increment_duo_downloads', {
      target_duo_post_id: duoId
    })

    if (error) {
      console.error('Error incrementing duo downloads:', error)
      return NextResponse.json({ error: 'Failed to track download' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in duos/download:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
