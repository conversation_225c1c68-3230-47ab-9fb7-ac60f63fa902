import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'

// POST /api/duos/comment
// Add a comment to a duo
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-comment')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoId, comment } = await request.json()
    if (!duoId || !comment) return NextResponse.json({ error: 'duoId and comment required' }, { status: 400 })

    // Add comment
    const { data: newComment, error: commentError } = await supabase
      .from('timeline_comments')
      .insert({
        user_id: user.id,
        duo_post_id: duoId,
        content: comment.trim()
      })
      .select()
      .single()

    if (commentError) {
      console.error('Error creating comment:', commentError)
      return NextResponse.json({ error: 'Failed to create comment' }, { status: 500 })
    }

    // Increment comment count
    await supabase
      .from('timeline_posts')
      .update({ comments_count: supabase.raw('comments_count + 1') })
      .eq('duo_post_id', duoId)

    return NextResponse.json({ comment: newComment })
  } catch (error) {
    console.error('Error in duos/comment:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
