import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'

// POST /api/duos/react
// Toggle reaction on a duo
export async function POST(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'WRITE', 'duo-react')
    if (!rate.allowed) return createRateLimitResponse('WRITE', rate)

    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const { duoId } = await request.json()
    if (!duoId) return NextResponse.json({ error: 'duoId required' }, { status: 400 })

    // Check if user already reacted
    const { data: existingReaction } = await supabase
      .from('timeline_reactions')
      .select('id')
      .eq('user_id', user.id)
      .eq('duo_post_id', duoId)
      .maybeSingle()

    if (existingReaction) {
      // Remove reaction
      await supabase
        .from('timeline_reactions')
        .delete()
        .eq('id', existingReaction.id)

      // Decrement count using RPC function
      await supabase.rpc('decrement_timeline_reactions', {
        target_duo_post_id: duoId
      })

      return NextResponse.json({ reacted: false })
    } else {
      // Add reaction
      await supabase
        .from('timeline_reactions')
        .insert({
          user_id: user.id,
          duo_post_id: duoId,
          reaction_type: 'heart'
        })

      // Increment count using RPC function
      await supabase.rpc('increment_timeline_reactions', {
        target_duo_post_id: duoId
      })

      return NextResponse.json({ reacted: true })
    }
  } catch (error) {
    console.error('Error in duos/react:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
