import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'
import { checkRateLimit, createRateLimitResponse } from '@/lib/rate-limit'

// GET /api/duos/timeline
// Returns all published duos for timeline display
export async function GET(request: NextRequest) {
  try {
    const rate = checkRateLimit(request, 'READ', 'duos-timeline')
    if (!rate.allowed) return createRateLimitResponse('READ', rate)

    const supabase = await createServerSupabaseClient()

    // Get current user
    const { data: { user } } = await supabase.auth.getUser()

    // Get all published duos with their parts and user info, including view counts from timeline_posts
    const { data: duos, error: duosError } = await supabase
      .from('duo_posts')
      .select(`
        id,
        created_at,
        final_video_url,
        final_video_r2_key,
        initiator:users!duo_posts_initiator_user_id_fkey(name, profile_picture_url),
        responder:users!duo_posts_responder_user_id_fkey(name, profile_picture_url),
        timeline_posts!inner(view_count, love_count)
      `)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })

    if (duosError) {
      console.error('Error fetching duos:', duosError)
      return NextResponse.json({ error: 'Failed to fetch duos' }, { status: 500 })
    }

    if (!duos || duos.length === 0) {
      return NextResponse.json({ duos: [] })
    }

    // Get duo parts for each duo
    const duoIds = duos.map(d => d.id)
    const { data: parts, error: partsError } = await supabase
      .from('duo_parts')
      .select('duo_post_id, part_number, media')
      .in('duo_post_id', duoIds)
      .order('part_number')

    if (partsError) {
      console.error('Error fetching duo parts:', partsError)
      return NextResponse.json({ error: 'Failed to fetch duo parts' }, { status: 500 })
    }

    // Get engagement counts (love, comments, views)
    const { data: timeline, error: timelineError } = await supabase
      .from('timeline_posts')
      .select('duo_post_id, love_count, comment_count, view_count, download_count')
      .in('duo_post_id', duoIds)
      .eq('is_hidden', false)

    if (timelineError) {
      console.error('Error fetching timeline data:', timelineError)
    }

    // Combine data
    const enrichedDuos = duos.map(duo => {
      const duoParts = parts?.filter(p => p.duo_post_id === duo.id) || []
      const partA = duoParts.find(p => p.part_number === 1)
      const partB = duoParts.find(p => p.part_number === 2)
      const timelineData = timeline?.find(t => t.duo_post_id === duo.id)

      // Skip duos without both parts
      if (!partA?.media?.src_r2_key || !partB?.media?.src_r2_key) {
        return null
      }

      return {
        id: duo.id,
        created_at: duo.created_at,
        final_video_url: duo.final_video_url,
        final_video_r2_key: duo.final_video_r2_key,
        initiator: duo.initiator,
        responder: duo.responder,
        partA: {
          src_r2_key: partA.media.src_r2_key,
          hls_manifest: partA.media.hls_manifest
        },
        partB: {
          src_r2_key: partB.media.src_r2_key,
          hls_manifest: partB.media.hls_manifest
        },
        reactions_count: timelineData?.love_count || 0,
        comments_count: timelineData?.comment_count || 0,
        downloads_count: timelineData?.download_count || 0,
        view_count: timelineData?.view_count || 0
      }
    }).filter(Boolean) // Remove null entries

    // Fetch reactions for duos (same pattern as diary entries)
    let duoReactions: Record<string, Record<string, number>> = {}
    let userDuoReactions: Record<string, string> = {}

    if (duoIds.length > 0) {
      const { data: reactions } = await supabase
        .from('reactions')
        .select('duo_post_id, reaction_type, user_id')
        .in('duo_post_id', duoIds)

      reactions?.forEach(reaction => {
        if (!duoReactions[reaction.duo_post_id]) {
          duoReactions[reaction.duo_post_id] = {}
        }
        duoReactions[reaction.duo_post_id][reaction.reaction_type] =
          (duoReactions[reaction.duo_post_id][reaction.reaction_type] || 0) + 1

        if (user && reaction.user_id === user.id) {
          userDuoReactions[reaction.duo_post_id] = reaction.reaction_type
        }
      })
    }

    // Fetch comment counts for duos
    let duoCommentCounts: Record<string, number> = {}
    if (duoIds.length > 0) {
      const { data: commentData } = await supabase
        .from('comments')
        .select('duo_post_id')
        .in('duo_post_id', duoIds)
        .eq('is_deleted', false)

      commentData?.forEach(comment => {
        duoCommentCounts[comment.duo_post_id] = (duoCommentCounts[comment.duo_post_id] || 0) + 1
      })
    }

    // Add reaction and comment data to duos
    const duosWithReactions = enrichedDuos.map(duo => ({
      ...duo,
      reactions: duoReactions[duo.id] || {},
      userReaction: userDuoReactions[duo.id] || null,
      comments_count: duoCommentCounts[duo.id] || 0,
      // Ensure counts are properly included
      downloads_count: duo.downloads_count || 0,
      view_count: duo.view_count || 0
    }))

    const response = NextResponse.json({ duos: duosWithReactions })

    // Prevent caching of user-specific reaction data
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')

    return response
  } catch (error) {
    console.error('Error in duos/timeline:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
