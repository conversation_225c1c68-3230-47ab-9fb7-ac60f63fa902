import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import { processEbook, processEbookBuffer } from '@/lib/ebook-processor'

export async function POST(request: NextRequest) {
  try {
    const { projectId, fileUrl, fileType, filePath, bucketName } = await request.json()

    if (!projectId || !fileType || (!fileUrl && !filePath)) {
      return NextResponse.json(
        { error: 'Project ID, file type, and either fileUrl or filePath are required' },
        { status: 400 }
      )
    }

    if (!['epub', 'docx', 'doc', 'rtf'].includes(fileType)) {
      return NextResponse.json(
        { error: 'File type must be epub, docx, doc, or rtf' },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id, ebook_file_type, author_name, meta_description')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    try {
      console.log(`Processing ${fileType.toUpperCase()} file for project:`, projectId)

      // Determine bucket and path from either fileUrl or filePath
      let downloadBucket = bucketName || 'ebooks'
      let pathToDownload: string | undefined = filePath

      if (fileUrl) {
        const afterPublic = fileUrl.split('/storage/v1/object/public/')[1]
        if (!afterPublic) {
          throw new Error('Invalid file URL format')
        }
        const [bucketFromUrl, ...rest] = afterPublic.split('/')
        downloadBucket = bucketFromUrl
        pathToDownload = rest.join('/')
      }

      if (!pathToDownload) {
        throw new Error('No file path provided for download')
      }

      console.log('Downloading file from Supabase storage:', { bucket: downloadBucket, path: pathToDownload })

      // Download the file using Supabase client
      const { data: fileData, error: downloadError } = await supabase.storage
        .from(downloadBucket)
        .download(pathToDownload)

      if (downloadError || !fileData) {
        console.error('Download error:', downloadError)
        throw new Error(`Failed to download file: ${downloadError?.message || 'Unknown error'}`)
      }

      console.log('File downloaded successfully, size:', fileData.size)

      // Convert blob to buffer
      const arrayBuffer = await fileData.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      // Process all supported file types
      console.log(`Processing ${fileType} file`)
      const result = await processEbookBuffer(buffer, fileType as 'epub' | 'docx' | 'doc' | 'rtf', false)
      
      console.log('Processing completed:', {
        chapters: result.chapters.length,
        wordCount: result.wordCount,
        pageCount: result.pageCount,
        readingTime: result.readingTimeMinutes
      })
      
      // Prepare update data with extracted metadata
      const updateData: any = {
        total_chapters: result.chapters.length,
        total_words: result.wordCount,
        reading_time_minutes: result.readingTimeMinutes,
        page_count: result.pageCount || Math.ceil(result.wordCount / 250), // Add page count
        is_complete: true,
        is_private: false, // Make sure it appears in book store
        status: 'published' // Mark as published when processing completes
      }

      // Update metadata if extracted and not already set (EPUB only)
      if (result.metadata?.author && !project.author_name) {
        updateData.author_name = result.metadata.author
      }

      // Tags are not provided by processor; skip
      if (result.metadata?.description && !project.meta_description) {
        updateData.meta_description = result.metadata.description.substring(0, 300)
      }

      // Update EPUB-specific metadata
      // No additional EPUB-specific metadata supported by processor types currently

      const { error: updateError } = await supabase
        .from('projects')
        .update(updateData)
        .eq('id', projectId)

      if (updateError) {
        console.error('Error updating project stats:', updateError)
        throw new Error('Failed to update project')
      }

      // Handle chapters for all file types that produce chapters
      if (result.chapters.length > 0) {
        console.log('Clearing existing chapters...')
        await supabase
          .from('chapters')
          .delete()
          .eq('project_id', projectId)

        console.log(`Creating ${result.chapters.length} chapters...`)

        const chaptersToInsert = result.chapters.map((chapter, index) => {
          console.log(`Chapter ${index + 1}:`, {
            title: chapter.title,
            contentLength: chapter.content?.length || 0,
            contentPreview: chapter.content?.substring(0, 100) + '...'
          })

          return {
            project_id: projectId,
            user_id: user.id,
            title: chapter.title,
            content: chapter.content,
            chapter_number: index + 1,
            word_count: chapter.word_count,
            is_published: true
          }
        })

        const { error: chaptersError, data: insertedChapters } = await supabase
          .from('chapters')
          .insert(chaptersToInsert)
          .select()

        if (chaptersError) {
          console.error('Error creating chapters:', chaptersError)
          throw new Error(`Failed to create chapters: ${chaptersError.message}`)
        } else {
          console.log('Chapters created successfully:', insertedChapters?.length)

          // Verify chapters were created with content
          const { data: verifyChapters } = await supabase
            .from('chapters')
            .select('id, title, content, chapter_number')
            .eq('project_id', projectId)
            .order('chapter_number')

          console.log('Verification - chapters in DB:', verifyChapters?.map(c => ({
            chapter: c.chapter_number,
            title: c.title,
            hasContent: !!c.content,
            contentLength: c.content?.length || 0
          })))
        }
      } else {
        // No chapters extracted - this is an error for all supported file types
        console.warn(`No chapters extracted from the ${fileType.toUpperCase()} file!`)
        throw new Error(`No chapters could be extracted from the uploaded ${fileType.toUpperCase()} file`)
      }

      return NextResponse.json({
        success: true,
        message: `${fileType.toUpperCase()} processed successfully`,
        data: {
          chaptersCreated: result.chapters.length,
          totalWords: result.wordCount,
          readingTime: result.readingTimeMinutes,
          pageCount: result.pageCount,
          metadata: result.metadata
        }
      })

    } catch (processingError) {
      console.error('Ebook processing error:', processingError)
      const err: any = processingError as any
      return NextResponse.json(
        { error: `Failed to process ${fileType}: ${err?.message || 'Unknown error'}` },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
