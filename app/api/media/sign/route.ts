import { NextRequest, NextResponse } from 'next/server'
import r2Client from '@/lib/r2-client'
import r2VideoClient from '@/lib/r2-video-client'
import { GetObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'

// GET /api/media/sign?key=...&bucket=optional
// Returns a short TTL signed URL to GET an object from R2 (private bucket)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key') || ''
    const overrideBucket = searchParams.get('bucket') || undefined
    const bucket = overrideBucket || process.env.CLOUDFLARE_R2_VIDEO_BUCKET_NAME || process.env.CLOUDFLARE_R2_BUCKET_NAME

    if (!key || !bucket) {
      return NextResponse.json({ error: 'Missing params or R2 config' }, { status: 400 })
    }

    const TTL = 900 // 15 minutes
    const command = new GetObjectCommand({ Bucket: bucket, Key: key })

    // Use the correct R2 client based on bucket
    const client = bucket === 'onlydiary-videos' ? r2VideoClient : r2Client
    const signedUrl = await getSignedUrl(client, command, { expiresIn: TTL })

    return NextResponse.json({ url: signedUrl, expiresIn: TTL })
  } catch (e: any) {
    console.error('media/sign error', e)
    return NextResponse.json({ error: 'Failed to sign media' }, { status: 500 })
  }
}

