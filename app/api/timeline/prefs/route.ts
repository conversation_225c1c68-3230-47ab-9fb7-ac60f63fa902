import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return NextResponse.json({ enabled: null, realm_order: null }, { status: 200 })

    const { data, error } = await supabase.rpc('get_timeline_prefs_for_user')
    if (error) throw error

    // Function returns a single row-like object with fields: enabled, realm_order
    return NextResponse.json({ enabled: data?.enabled ?? null, realm_order: data?.realm_order ?? null })
  } catch (err) {
    console.error('Timeline prefs GET error:', err)
    return NextResponse.json({ error: 'Failed to get timeline prefs' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })

    const body = await request.json().catch(() => ({}))
    const enabled = body?.enabled ?? null
    const realm_order = body?.realm_order ?? null

    const { error } = await supabase.rpc('set_timeline_prefs', {
      p_enabled: enabled,
      p_realm_order: realm_order,
    })
    if (error) throw error

    // Return updated prefs after save
    const { data } = await supabase.rpc('get_timeline_prefs_for_user')
    return NextResponse.json({ enabled: data?.enabled ?? null, realm_order: data?.realm_order ?? null })
  } catch (err) {
    console.error('Timeline prefs POST error:', err)
    return NextResponse.json({ error: 'Failed to update timeline prefs' }, { status: 500 })
  }
}

