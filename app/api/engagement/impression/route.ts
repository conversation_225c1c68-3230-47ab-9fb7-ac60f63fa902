import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'

// Keep this hot path short to avoid serverless timeouts from spikes
export const maxDuration = 3
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  const started = Date.now()
  try {
    const { contentType, contentId, source = 'timeline' } = await request.json()

    if (!contentType || !contentId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const supabase = await createSupabaseServerClient()

    // Get user if authenticated (non-blocking)
    let user = null
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      user = authUser
    } catch {}

    // Minimal insert, no joins
    const { error } = await supabase
      .from('post_impressions')
      .insert({
        user_id: user?.id || null,
        content_type: contentType,
        content_id: contentId,
        source
      })

    if (error) {
      // Ignore duplicates/constraints to keep request cheap
      if (error.message?.includes('duplicate key') || (error as any).code === '23514') {
        return NextResponse.json({ success: true }, { status: 202 })
      }
      console.warn('impression insert error', error)
      return NextResponse.json({ success: false }, { status: 202 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.warn('impression handler error after', Date.now() - started, 'ms')
    // Always respond quickly; clients don’t need strict confirmation
    return NextResponse.json({ success: false }, { status: 202 })
  }
}
