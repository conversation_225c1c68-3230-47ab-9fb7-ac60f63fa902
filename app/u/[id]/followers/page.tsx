import { createSupabaseServerClient } from '@/lib/supabase/client'
import Image from 'next/image'
import Link from 'next/link'

interface FollowersPageProps {
  params: Promise<{ id: string }>
}

export default async function FollowersPage({ params }: FollowersPageProps) {
  const { id } = await params
  const supabase = await createSupabaseServerClient()

  // Get the profile owner
  const { data: user } = await supabase
    .from('users')
    .select('id, name, profile_picture_url, avatar')
    .eq('id', id)
    .single()

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-600">User not found</div>
      </div>
    )
  }

  // Get followers (people who follow this user)
  const { data: followers } = await supabase
    .from('follows')
    .select(`
      id,
      follower:users!follows_follower_id_fkey(
        id,
        name,
        profile_picture_url,
        avatar
      )
    `)
    .eq('writer_id', id)
    .order('created_at', { ascending: false })

  const followersList = (followers || []).map((f: any) => f.follower).filter(Boolean)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Link href={`/u/${user.id}`} className="text-blue-600 hover:text-blue-700 font-medium">← Back to {user.name}</Link>
          <h1 className="text-2xl font-serif text-gray-900 mt-3">Followers</h1>
          <p className="text-gray-600 text-sm">People who follow {user.name}</p>
        </div>

        {followersList.length === 0 ? (
          <div className="bg-white rounded-xl p-8 text-center border border-gray-200">
            <p className="text-gray-600">No followers yet.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {followersList.map((follower: any) => (
              <div key={follower.id} className="bg-white rounded-xl p-4 border border-gray-200 hover:shadow-sm transition-all">
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => window.location.href = `/u/${follower.id}`}
                    className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-blue-300"
                    aria-label={`View ${follower.name}'s profile`}
                  >
                    {follower.profile_picture_url || follower.avatar ? (
                      <Image src={follower.profile_picture_url || follower.avatar} alt={follower.name} width={40} height={40} className="w-10 h-10 object-cover" />
                    ) : (
                      <span className="text-sm text-gray-500">{follower.name?.charAt(0).toUpperCase()}</span>
                    )}
                  </button>

                  <div className="min-w-0 flex-1">
                    <h3 className="font-medium text-gray-900 truncate">
                      <Link href={`/u/${follower.id}`} className="hover:text-blue-600 transition-colors">{follower.name}</Link>
                    </h3>
                    <div className="text-xs text-gray-500">Follower</div>
                  </div>

                  <Link href={`/u/${follower.id}`} className="text-xs text-blue-600 hover:text-blue-700">View</Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

