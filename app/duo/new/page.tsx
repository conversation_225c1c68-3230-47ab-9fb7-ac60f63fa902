import { createServerSupabaseClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { DuoNewClient } from '@/components/duo/DuoNewClient'

export default async function DuoNewPage() {
  const supabase = await createServerSupabaseClient()
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login?next=' + encodeURIComponent('/duo/new'))

  // Create an empty duo post for the initiator
  const { data: duo, error } = await supabase
    .from('duo_posts')
    .insert({ initiator_user_id: user.id, status: 'pending' })
    .select('id')
    .single()

  if (error || !duo) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <h1 className="text-2xl font-serif font-semibold mb-2">Could not start a Duo</h1>
        <p className="text-gray-600">Please try again.</p>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-serif text-gray-900 mb-2">Start an OnlyDuo</h1>
        <p className="text-gray-600 font-serif">Two short videos. One conversation. A new way to tell a story together.</p>
      </div>

      {/* Client flow: Tag friend first, then upload */}
      <DuoNewClient duoPostId={duo.id} />

      {/* OnlyDuos You May Like (carousel placeholder removed per request) */}
      <div className="mt-8">
        <h2 className="text-lg font-serif font-semibold text-gray-900 mb-2">OnlyDuos You May Like</h2>
      </div>
    </div>
  )
}

