import { createServerSupabaseClient } from '@/lib/supabase/server'
import { DuoApproveClient } from '@/components/duo/DuoApproveClient'
import { DuoVideoPlayer } from '@/components/duo/DuoVideoPlayer'
import Image from 'next/image'
import Link from 'next/link'
import { redirect } from 'next/navigation'

export default async function DuoApprovePage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = await createServerSupabaseClient()
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login?next=' + encodeURIComponent(`/duo/${id}`))

  // Load duo, parts, and users
  const { data: duo } = await supabase
    .from('duo_posts' as any)
    .select(`
      id, initiator_user_id, responder_user_id, status, title, body,
      initiator:users!duo_posts_initiator_user_id_fkey(id, name, profile_picture_url, avatar),
      responder:users!duo_posts_responder_user_id_fkey(id, name, profile_picture_url, avatar)
    `)
    .eq('id', id)
    .maybeSing<PERSON>()
  const duoAny = duo as any

  if (!duo) {
    return (
      <div className="max-w-xl mx-auto p-6">
        <h1 className="text-xl font-semibold mb-2">Duo not found</h1>
        <p className="text-gray-600">The Duo you are trying to view does not exist.</p>
      </div>
    )
  }

  const { data: parts } = await supabase
    .from('duo_parts' as any)
    .select('duo_post_id, part_number, author_user_id, media')
    .eq('duo_post_id', id)
  const partsAny = (parts as any[]) || []

  const partA = partsAny.find((p: any) => p.part_number === 1)?.media || null
  const partB = partsAny.find((p: any) => p.part_number === 2)?.media || null
  const hasPartB = !!(partB && ((partB as any).src_r2_key || (partB as any).hls_manifest))

  const isInitiator = duoAny.initiator_user_id === user.id

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* Top bar: Back to Dashboard */}
      <div className="mb-4">
        <Link href="/dashboard" className="text-sm text-blue-600 hover:underline">← Back to Dashboard</Link>
      </div>

      <div className="flex items-center gap-3 mb-3">
        <Link href={`/u/${duoAny.initiator?.id || duoAny.initiator_user_id}`} className="w-10 h-10 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
          {(duoAny.initiator?.profile_picture_url || duoAny.initiator?.avatar) ? (
            <Image src={(duoAny.initiator?.profile_picture_url || duoAny.initiator?.avatar) as string} alt={duoAny.initiator?.name || 'User'} width={40} height={40} className="w-full h-full object-cover" />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-500">{(duoAny.initiator?.name || 'U')[0]}</div>
          )}
        </Link>
        <div>
          <div className="text-sm text-gray-600">A Duo by</div>
          <div className="flex items-center gap-1 text-sm">
            <Link href={`/u/${duoAny.initiator?.id || duoAny.initiator_user_id}`} className="font-medium text-gray-900 hover:text-blue-600">
              {duoAny.initiator?.name || 'User'}
            </Link>
            <span className="text-gray-400">&</span>
            {duoAny.responder?.id || duoAny.responder_user_id ? (
              <Link href={`/u/${duoAny.responder?.id || duoAny.responder_user_id}`} className="font-medium text-gray-900 hover:text-blue-600">
                {duoAny.responder?.name || 'User'}
              </Link>
            ) : (
              <span className="font-medium text-gray-400">________</span>
            )}
          </div>
        </div>
      </div>

      <h1 className="text-xl font-semibold mb-1">{duoAny.title || 'A Duo'}</h1>
      {duoAny.body && <p className="text-gray-600 mb-3">{duoAny.body}</p>}

      {/* Guidance banner */}
      {hasPartB ? (
        <div className="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3 text-sm text-yellow-900">
          {isInitiator
            ? 'Part B is ready — You can review and approve to publish this Duo to the timeline.'
            : 'Part B is ready — Waiting for the initiator to approve and publish this Duo.'}
        </div>
      ) : (
        <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-3 text-sm text-blue-900">
          Invite sent. Waiting for your friend to record Part B.
        </div>
      )}

      {partA ? (
        <DuoVideoPlayer partA={partA} partB={hasPartB ? partB : undefined} />
      ) : (
        isInitiator ? (
          <div className="rounded bg-blue-50 border border-blue-200 text-blue-900 p-4 mb-4">Your Part A is processing. This can take up to a minute.</div>
        ) : (
          <div className="rounded bg-gray-50 border border-gray-200 p-4 mb-4">Waiting for the initiator to upload Part A.</div>
        )
      )}

      <DuoApproveClient
        duoPostId={duoAny.id}
        isInitiator={isInitiator}
        hasPartB={hasPartB}
        status={duoAny.status}
      />
    </div>
  )
}
