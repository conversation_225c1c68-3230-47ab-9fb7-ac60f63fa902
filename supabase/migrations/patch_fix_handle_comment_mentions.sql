-- Patch: fix comment mentions function to use NEW.body (comments) instead of NEW.body_md
-- Safe to run multiple times (CREATE OR REPLACE), no trigger changes required

CREATE OR REPLACE FUNCTION handle_comment_mentions()
RETURNS TRIGGER 
SET search_path = ''
AS $$
DECLARE
  mention_user_id UUID;
  at_username TEXT;
  mentioned_user_id UUID;
BEGIN
  -- Parse simple @mentions from comment body (text)
  -- NOTE: This is a simplified parser; assumes usernames are alphanumeric/underscore
  FOR at_username IN
    SELECT DISTINCT LOWER(m[1]) AS uname
    FROM regexp_matches(COALESCE(NEW.body, ''), '@([A-Za-z0-9_]+)', 'g') AS m
  LOOP
    -- Look up the mentioned user by custom_url or username field if you have it
    SELECT id INTO mentioned_user_id
    FROM public.users
    WHERE custom_url = at_username
       OR LOWER(name) = LOWER(at_username)
    LIMIT 1;

    IF mentioned_user_id IS NOT NULL AND mentioned_user_id != NEW.user_id THEN
      -- Insert a notification for the mentioned user
      INSERT INTO public.notifications (user_id, type, title, message, data, created_at)
      VALUES (
        mentioned_user_id,
        'mention',
        'You were mentioned in a comment',
        (SELECT name FROM public.users WHERE id = NEW.user_id) || ' mentioned you',
        jsonb_build_object(
          'comment_id', NEW.id,
          'content_type', CASE WHEN NEW.diary_entry_id IS NOT NULL THEN 'diary_entry' ELSE 'book' END,
          'content_id', COALESCE(NEW.diary_entry_id, NEW.book_id)
        ),
        NOW()
      );
    END IF;
  END LOOP;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

