-- engagement_metrics
ALTER TABLE public.engagement_metrics
  DROP CONSTRAINT IF EXISTS engagement_metrics_content_type_check;
ALTER TABLE public.engagement_metrics
  ADD CONSTRAINT engagement_metrics_content_type_check
  CHECK (content_type IN ('diary','audio','book','recipe'));

-- post_impressions
ALTER TABLE public.post_impressions
  DROP CONSTRAINT IF EXISTS post_impressions_content_type_check;
ALTER TABLE public.post_impressions
  ADD CONSTRAINT post_impressions_content_type_check
  CHECK (content_type IN ('diary','audio','book','recipe'));

-- post_clicks
ALTER TABLE public.post_clicks
  DROP CONSTRAINT IF EXISTS post_clicks_content_type_check;
ALTER TABLE public.post_clicks
  ADD CONSTRAINT post_clicks_content_type_check
  CHECK (content_type IN ('diary','audio','book','recipe'));

-- share_tracking
ALTER TABLE public.share_tracking
  DROP CONSTRAINT IF EXISTS share_tracking_content_type_check;
ALTER TABLE public.share_tracking
  ADD CONSTRAINT share_tracking_content_type_check
  CHECK (content_type IN ('diary','audio','book','recipe'));