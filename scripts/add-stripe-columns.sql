-- Add Stripe Connect columns to users table if they don't exist
-- Run this to ensure all users have the necessary columns for monetization

-- Add stripe_account_id column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'stripe_account_id') THEN
        ALTER TABLE users ADD COLUMN stripe_account_id TEXT;
        COMMENT ON COLUMN users.stripe_account_id IS 'Stripe Connect account ID for monetization';
    END IF;
END $$;

-- Add stripe_onboarding_complete column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'stripe_onboarding_complete') THEN
        ALTER TABLE users ADD COLUMN stripe_onboarding_complete BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN users.stripe_onboarding_complete IS 'Whether Stripe Connect onboarding is complete';
    END IF;
END $$;

-- Add email_notifications column if it doesn't exist (for engagement emails)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'email_notifications') THEN
        ALTER TABLE users ADD COLUMN email_notifications BOOLEAN DEFAULT TRUE;
        COMMENT ON COLUMN users.email_notifications IS 'Whether user wants to receive email notifications';
    END IF;
END $$;

-- Create index on stripe_account_id for performance
CREATE INDEX IF NOT EXISTS idx_users_stripe_account_id ON users(stripe_account_id);

-- Create index on stripe_onboarding_complete for monetization checks
CREATE INDEX IF NOT EXISTS idx_users_stripe_onboarding ON users(stripe_onboarding_complete);

-- Grant permissions
GRANT SELECT, UPDATE ON users TO authenticated;
