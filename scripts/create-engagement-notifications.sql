-- Create engagement notifications system
-- This tracks when users hit certain milestones and sends them emails

-- Table to track notification milestones
CREATE TABLE IF NOT EXISTS user_engagement_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL, -- 'followers', 'comments', 'likes', 'subscribers'
    milestone_count INTEGER NOT NULL, -- 5, 10, 20, 50, 100, etc.
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    email_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_user_engagement_notifications_user_type 
ON user_engagement_notifications(user_id, notification_type);

-- Table to define milestone thresholds
CREATE TABLE IF NOT EXISTS engagement_milestones (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    notification_type TEXT NOT NULL,
    milestone_count INTEGER NOT NULL,
    email_template TEXT NOT NULL, -- template name to use
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(notification_type, milestone_count)
);

-- Insert default milestones
INSERT INTO engagement_milestones (notification_type, milestone_count, email_template) VALUES
('followers', 5, 'first_followers'),
('followers', 10, 'growing_followers'),
('followers', 25, 'popular_creator'),
('followers', 50, 'rising_star'),
('followers', 100, 'community_favorite'),
('comments', 5, 'first_comments'),
('comments', 20, 'engaging_content'),
('comments', 50, 'conversation_starter'),
('likes', 20, 'first_likes'),
('likes', 50, 'loved_content'),
('likes', 100, 'viral_content'),
('subscribers', 3, 'first_subscribers'),
('subscribers', 10, 'growing_audience'),
('subscribers', 25, 'successful_creator')
ON CONFLICT (notification_type, milestone_count) DO NOTHING;

-- Function to check and queue engagement notifications
CREATE OR REPLACE FUNCTION check_engagement_milestone(
    p_user_id UUID,
    p_notification_type TEXT,
    p_current_count INTEGER
) RETURNS VOID AS $$
DECLARE
    milestone_record RECORD;
    already_sent BOOLEAN;
BEGIN
    -- Find the highest milestone this user has reached for this type
    FOR milestone_record IN 
        SELECT milestone_count, email_template
        FROM engagement_milestones 
        WHERE notification_type = p_notification_type 
        AND milestone_count <= p_current_count
        AND is_active = TRUE
        ORDER BY milestone_count DESC
    LOOP
        -- Check if we've already sent this milestone notification
        SELECT EXISTS(
            SELECT 1 FROM user_engagement_notifications 
            WHERE user_id = p_user_id 
            AND notification_type = p_notification_type 
            AND milestone_count = milestone_record.milestone_count
        ) INTO already_sent;
        
        -- If not sent, queue it
        IF NOT already_sent THEN
            INSERT INTO user_engagement_notifications (
                user_id, 
                notification_type, 
                milestone_count,
                email_sent
            ) VALUES (
                p_user_id, 
                p_notification_type, 
                milestone_record.milestone_count,
                FALSE
            );
            
            -- Only send the highest milestone reached
            EXIT;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function for followers
CREATE OR REPLACE FUNCTION trigger_follower_milestone() RETURNS TRIGGER AS $$
BEGIN
    -- Get current follower count
    PERFORM check_engagement_milestone(
        NEW.following_id,
        'followers',
        (SELECT COUNT(*) FROM follows WHERE following_id = NEW.following_id)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for comments
CREATE OR REPLACE FUNCTION trigger_comment_milestone() RETURNS TRIGGER AS $$
DECLARE
    entry_owner_id UUID;
    comment_count INTEGER;
BEGIN
    -- Get the owner of the diary entry
    SELECT user_id INTO entry_owner_id 
    FROM diary_entries 
    WHERE id = NEW.diary_entry_id;
    
    IF entry_owner_id IS NOT NULL THEN
        -- Get total comments on all their entries
        SELECT COUNT(*) INTO comment_count
        FROM comments c
        JOIN diary_entries de ON c.diary_entry_id = de.id
        WHERE de.user_id = entry_owner_id;
        
        PERFORM check_engagement_milestone(
            entry_owner_id,
            'comments',
            comment_count
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger function for likes/reactions
CREATE OR REPLACE FUNCTION trigger_reaction_milestone() RETURNS TRIGGER AS $$
DECLARE
    entry_owner_id UUID;
    reaction_count INTEGER;
BEGIN
    -- Get the owner of the diary entry
    SELECT user_id INTO entry_owner_id 
    FROM diary_entries 
    WHERE id = NEW.diary_entry_id;
    
    IF entry_owner_id IS NOT NULL THEN
        -- Get total reactions on all their entries
        SELECT COUNT(*) INTO reaction_count
        FROM reactions r
        JOIN diary_entries de ON r.diary_entry_id = de.id
        WHERE de.user_id = entry_owner_id;
        
        PERFORM check_engagement_milestone(
            entry_owner_id,
            'likes',
            reaction_count
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS follower_milestone_trigger ON follows;
CREATE TRIGGER follower_milestone_trigger
    AFTER INSERT ON follows
    FOR EACH ROW
    EXECUTE FUNCTION trigger_follower_milestone();

DROP TRIGGER IF EXISTS comment_milestone_trigger ON comments;
CREATE TRIGGER comment_milestone_trigger
    AFTER INSERT ON comments
    FOR EACH ROW
    EXECUTE FUNCTION trigger_comment_milestone();

DROP TRIGGER IF EXISTS reaction_milestone_trigger ON reactions;
CREATE TRIGGER reaction_milestone_trigger
    AFTER INSERT ON reactions
    FOR EACH ROW
    EXECUTE FUNCTION trigger_reaction_milestone();

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON user_engagement_notifications TO authenticated;
GRANT SELECT ON engagement_milestones TO authenticated;
