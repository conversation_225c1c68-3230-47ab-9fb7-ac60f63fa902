#!/bin/bash

# Setup script for FFmpeg installation
# Supports macOS, Ubuntu/Debian, and other Linux distributions

echo "🎬 Setting up FFmpeg for OnlyDiary video processing..."

# Detect operating system
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "📱 Detected macOS"
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "❌ Homebrew not found. Please install Homebrew first:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
    
    echo "🍺 Installing FFmpeg via Homebrew..."
    brew install ffmpeg
    
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "🐧 Detected Linux"
    
    # Check for package manager
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        echo "📦 Installing FFmpeg via apt..."
        sudo apt-get update
        sudo apt-get install -y ffmpeg
        
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        echo "📦 Installing FFmpeg via yum..."
        sudo yum install -y epel-release
        sudo yum install -y ffmpeg ffmpeg-devel
        
    elif command -v dnf &> /dev/null; then
        # Fedora
        echo "📦 Installing FFmpeg via dnf..."
        sudo dnf install -y ffmpeg ffmpeg-devel
        
    elif command -v pacman &> /dev/null; then
        # Arch Linux
        echo "📦 Installing FFmpeg via pacman..."
        sudo pacman -S ffmpeg
        
    else
        echo "❌ Unsupported Linux distribution. Please install FFmpeg manually."
        exit 1
    fi
    
else
    echo "❌ Unsupported operating system: $OSTYPE"
    echo "Please install FFmpeg manually and ensure it's in your PATH."
    exit 1
fi

# Verify installation
echo "🔍 Verifying FFmpeg installation..."

if command -v ffmpeg &> /dev/null; then
    echo "✅ FFmpeg installed successfully!"
    ffmpeg -version | head -1
else
    echo "❌ FFmpeg installation failed!"
    exit 1
fi

if command -v ffprobe &> /dev/null; then
    echo "✅ FFprobe installed successfully!"
    ffprobe -version | head -1
else
    echo "❌ FFprobe installation failed!"
    exit 1
fi

# Create temp directory for video processing
echo "📁 Creating temp directory for video processing..."
mkdir -p /tmp/video-processing
chmod 755 /tmp/video-processing

echo ""
echo "🎉 FFmpeg setup complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Set environment variables in your .env.local:"
echo "      FFMPEG_PATH=$(which ffmpeg)"
echo "      FFPROBE_PATH=$(which ffprobe)"
echo "      TEMP_DIR=/tmp/video-processing"
echo ""
echo "   2. Restart your development server:"
echo "      npm run dev"
echo ""
echo "   3. Test OnlyDuo download functionality"
echo ""

# Add environment variables to .env.local if it exists
if [ -f ".env.local" ]; then
    echo "📝 Adding FFmpeg paths to .env.local..."
    
    # Remove existing FFmpeg entries
    grep -v "FFMPEG_PATH\|FFPROBE_PATH\|TEMP_DIR" .env.local > .env.local.tmp
    mv .env.local.tmp .env.local
    
    # Add new entries
    echo "" >> .env.local
    echo "# FFmpeg configuration for video processing" >> .env.local
    echo "FFMPEG_PATH=$(which ffmpeg)" >> .env.local
    echo "FFPROBE_PATH=$(which ffprobe)" >> .env.local
    echo "TEMP_DIR=/tmp/video-processing" >> .env.local
    
    echo "✅ Environment variables added to .env.local"
else
    echo "⚠️  .env.local not found. Please create it with the environment variables above."
fi

echo ""
echo "🚀 Ready for OnlyDuo video processing!"
