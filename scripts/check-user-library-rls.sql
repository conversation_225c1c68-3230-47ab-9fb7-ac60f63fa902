-- Check user_library table RLS status and fix missing policies
-- This addresses the issue where free books aren't being added to "My Library"

-- 1. Check if R<PERSON> is enabled on user_library table
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'user_library';

-- 2. Check existing policies on user_library table
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'user_library';

-- 3. Check if there are any records in user_library (should show current state)
SELECT COUNT(*) as total_library_entries FROM user_library;

-- 4. Check recent free book download attempts (if any)
SELECT 
    ul.user_id,
    ul.project_id,
    ul.access_type,
    ul.added_at,
    p.title,
    p.price_amount
FROM user_library ul
JOIN projects p ON ul.project_id = p.id
WHERE ul.access_type = 'free'
ORDER BY ul.added_at DESC
LIMIT 10;

-- 5. Enable RLS on user_library if not already enabled
ALTER TABLE user_library ENABLE ROW LEVEL SECURITY;

-- 6. Create missing RLS policies for user_library table
-- Users can view their own library entries
CREATE POLICY "Users can view their own library" ON user_library
    FOR SELECT USING (auth.uid() = user_id);

-- Users can add books to their own library
CREATE POLICY "Users can add to their own library" ON user_library
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own library entries (for last_accessed_at)
CREATE POLICY "Users can update their own library" ON user_library
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Users can remove books from their own library
CREATE POLICY "Users can delete from their own library" ON user_library
    FOR DELETE USING (auth.uid() = user_id);

-- System/service role can manage all library entries (for webhooks, etc.)
CREATE POLICY "Service role can manage all library entries" ON user_library
    FOR ALL USING (auth.role() = 'service_role');

-- 7. Verify the policies were created
SELECT schemaname, tablename, policyname, permissive, roles, cmd
FROM pg_policies 
WHERE tablename = 'user_library'
ORDER BY policyname;

-- 8. Test query to verify access works (replace with actual user ID)
-- SELECT * FROM user_library WHERE user_id = 'your-user-id-here';
