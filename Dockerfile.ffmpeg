# Multi-stage Docker build for OnlyDiary with FFmpeg support
FROM node:18-alpine AS base

# Install FFmpeg and required dependencies
RUN apk add --no-cache \
    ffmpeg \
    ffmpeg-dev \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Verify FFmpeg installation
RUN ffmpeg -version && ffprobe -version

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Build the application
RUN npm run build

# Set environment variables for video processing
ENV FFMPEG_PATH=/usr/bin/ffmpeg
ENV FFPROBE_PATH=/usr/bin/ffprobe
ENV TEMP_DIR=/tmp/video-processing

# Create temp directory for video processing
RUN mkdir -p /tmp/video-processing && chmod 777 /tmp/video-processing

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "start"]
